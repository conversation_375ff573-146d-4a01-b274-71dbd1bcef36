# 智慧养鹅SaaS平台 - 生产环境配置
# 注意：部署前请修改所有密钥和密码

# ===========================================
# 🌍 基础环境配置
# ===========================================
NODE_ENV=production
PORT=3000
ADMIN_PORT=3002
SAAS_PORT=3003

# ===========================================
# 🗄️ 数据库配置
# ===========================================
DB_HOST=localhost
DB_PORT=3306
DB_USER=saas_admin
DB_PASSWORD=SaasGoose2025!
DB_NAME=smart_goose_saas
DB_SSL=false

# ===========================================
# 🔐 JWT和加密配置
# ===========================================
JWT_SECRET=SmartGoose2025JWTSecretKey9876543210
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=GooseRefreshToken2025SecretKey123456
ENCRYPT_KEY=GooseEncrypt2025Key567890123456

# ===========================================
# 🏢 多租户配置
# ===========================================
DEFAULT_TENANT=system
MAX_TENANTS=1000
TENANT_ISOLATION=true

# ===========================================
# 📱 微信小程序配置
# ===========================================
WECHAT_APPID=wx68a4d75dd80665ec
WECHAT_SECRET=wechat_secret_placeholder_change_me
WECHAT_MCH_ID=your_merchant_id
WECHAT_KEY=your_wechat_pay_key

# ===========================================
# 🌐 CORS和安全配置
# ===========================================
ALLOWED_ORIGINS=https://smart-goose.com,https://api.smart-goose.com,https://admin.smart-goose.com
CORS_CREDENTIALS=true
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# ===========================================
# 📧 邮件服务配置
# ===========================================
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
FROM_EMAIL=智慧养鹅SaaS <<EMAIL>>

# ===========================================
# 📊 监控和日志配置
# ===========================================
LOG_LEVEL=warn
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5
MONITORING_ENABLED=true

# ===========================================
# ☁️ 云服务配置
# ===========================================
# 阿里云OSS存储
ALIYUN_ACCESS_KEY=your_access_key
ALIYUN_SECRET_KEY=your_secret_key
ALIYUN_BUCKET=your_bucket_name
ALIYUN_REGION=oss-cn-hangzhou

# Redis缓存 (可选)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ===========================================
# 🎯 业务功能配置
# ===========================================
# AI功能配置
AI_ENABLED=true
AI_API_KEY=your_ai_api_key
AI_MODEL=gpt-3.5-turbo

# 支付配置
PAYMENT_ENABLED=true
STRIPE_KEY=your_stripe_key
ALIPAY_APP_ID=your_alipay_app_id

# 短信服务
SMS_ENABLED=true
SMS_ACCESS_KEY=your_sms_access_key
SMS_SECRET_KEY=your_sms_secret_key

# ===========================================
# 🚀 生产环境特定配置
# ===========================================
# 生产环境请设置为true
PRODUCTION=true
HTTPS_ENABLED=false
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem

# CDN配置
CDN_ENABLED=false
CDN_URL=https://cdn.yourdomain.com

# 备份配置
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# ===========================================
# 🐛 调试和开发配置
# ===========================================
DEBUG_MODE=false
DEBUG_NAMESPACE=saas:*
PROFILING_ENABLED=false
DEV_TOOLS_ENABLED=false