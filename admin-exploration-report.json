{"timestamp": "2025-08-29T03:19:04.586Z", "summary": {"totalMenuItems": 8, "missingPages": 5, "errors": 51, "databaseErrors": 0}, "findings": {"missingPages": [{"menuPath": "租户管理 > 租户列表", "url": "http://localhost:3002/tenants", "href": "/tenants", "issue": "页面不存在或返回错误"}, {"menuPath": "租户管理 > 使用统计", "url": "http://localhost:3002/tenants/usage", "href": "/tenants/usage", "issue": "页面不存在或返回错误"}, {"menuPath": "租户列表", "url": "http://localhost:3002/tenants", "href": "/tenants", "issue": "页面不存在或返回错误"}, {"menuPath": "使用统计", "url": "http://localhost:3002/tenants/usage", "href": "/tenants/usage", "issue": "页面不存在或返回错误"}, {"menuPath": "今日鹅价 > 地区配置", "url": "http://localhost:3002/goose-prices/regions", "href": "/goose-prices/regions", "issue": "页面不存在或返回错误"}], "errors": [{"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "url": "http://localhost:3002/auth/login"}, {"type": "page_error", "message": "Cannot use import statement outside a module", "url": "http://localhost:3002/dashboard"}, {"type": "page_error", "message": "Cannot use import statement outside a module", "url": "http://localhost:3002/dashboard"}, {"type": "page_error", "message": "Cannot use import statement outside a module", "url": "http://localhost:3002/tenants"}, {"type": "page_error", "message": "Cannot use import statement outside a module", "url": "http://localhost:3002/tenants/subscriptions"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 500 (Internal Server Error)", "url": "http://localhost:3002/tenants/usage"}, {"type": "page_error", "message": "Cannot use import statement outside a module", "url": "http://localhost:3002/tenants/usage"}, {"type": "page_error", "message": "Cannot use import statement outside a module", "url": "http://localhost:3002/tenants"}, {"type": "page_error", "message": "Cannot use import statement outside a module", "url": "http://localhost:3002/tenants/subscriptions"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 500 (Internal Server Error)", "url": "http://localhost:3002/tenants/usage"}, {"type": "page_error", "message": "Cannot use import statement outside a module", "url": "http://localhost:3002/tenants/usage"}, {"type": "page_error", "message": "Cannot use import statement outside a module", "url": "http://localhost:3002/goose-prices"}, {"type": "page_error", "message": "Chart is not defined", "url": "http://localhost:3002/goose-prices"}, {"type": "console_error", "message": "Failed to load resource: the server responded with a status of 404 (Not Found)", "url": "http://localhost:3002/goose-prices/regions"}, {"type": "menu_exploration_error", "message": "menuText is not defined", "menuIndex": 5}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(6)\u001b[22m\n", "menuIndex": 6}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(7)\u001b[22m\n", "menuIndex": 7}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(8)\u001b[22m\n", "menuIndex": 8}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(9)\u001b[22m\n", "menuIndex": 9}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(10)\u001b[22m\n", "menuIndex": 10}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(11)\u001b[22m\n", "menuIndex": 11}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(12)\u001b[22m\n", "menuIndex": 12}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(13)\u001b[22m\n", "menuIndex": 13}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(14)\u001b[22m\n", "menuIndex": 14}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(15)\u001b[22m\n", "menuIndex": 15}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(16)\u001b[22m\n", "menuIndex": 16}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(17)\u001b[22m\n", "menuIndex": 17}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(18)\u001b[22m\n", "menuIndex": 18}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(19)\u001b[22m\n", "menuIndex": 19}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(20)\u001b[22m\n", "menuIndex": 20}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(21)\u001b[22m\n", "menuIndex": 21}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(22)\u001b[22m\n", "menuIndex": 22}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(23)\u001b[22m\n", "menuIndex": 23}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(24)\u001b[22m\n", "menuIndex": 24}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(25)\u001b[22m\n", "menuIndex": 25}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(26)\u001b[22m\n", "menuIndex": 26}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(27)\u001b[22m\n", "menuIndex": 27}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(28)\u001b[22m\n", "menuIndex": 28}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(29)\u001b[22m\n", "menuIndex": 29}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(30)\u001b[22m\n", "menuIndex": 30}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(31)\u001b[22m\n", "menuIndex": 31}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(32)\u001b[22m\n", "menuIndex": 32}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(33)\u001b[22m\n", "menuIndex": 33}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(34)\u001b[22m\n", "menuIndex": 34}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(35)\u001b[22m\n", "menuIndex": 35}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(36)\u001b[22m\n", "menuIndex": 36}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(37)\u001b[22m\n", "menuIndex": 37}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(38)\u001b[22m\n", "menuIndex": 38}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(39)\u001b[22m\n", "menuIndex": 39}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(40)\u001b[22m\n", "menuIndex": 40}, {"type": "menu_exploration_error", "message": "locator.textContent: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('.main-sidebar .nav-link').nth(41)\u001b[22m\n", "menuIndex": 41}], "menuStructure": [{"path": "平台仪表盘", "url": "http://localhost:3002/dashboard", "href": "/dashboard", "hasContent": true}, {"path": "租户管理 > 租户列表", "url": "http://localhost:3002/tenants", "href": "/tenants", "hasContent": true}, {"path": "租户管理 > 订阅管理", "url": "http://localhost:3002/tenants/subscriptions", "href": "/tenants/subscriptions", "hasContent": true}, {"path": "租户管理 > 使用统计", "url": "http://localhost:3002/tenants/usage", "href": "/tenants/usage", "hasContent": true}, {"path": "租户列表", "url": "http://localhost:3002/tenants", "href": "/tenants", "hasContent": true}, {"path": "订阅管理", "url": "http://localhost:3002/tenants/subscriptions", "href": "/tenants/subscriptions", "hasContent": true}, {"path": "使用统计", "url": "http://localhost:3002/tenants/usage", "href": "/tenants/usage", "hasContent": true}, {"path": "今日鹅价 > 价格管理", "url": "http://localhost:3002/goose-prices", "href": "/goose-prices", "hasContent": true}], "databaseErrors": []}}