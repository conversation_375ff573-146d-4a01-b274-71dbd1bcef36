-- 创建缺失的数据库表
-- Smart Goose SAAS Platform - Missing Tables Creation Script

USE smart_goose_saas_platform;

-- 1. 创建 flocks 表 (鹅群管理)
CREATE TABLE IF NOT EXISTS flocks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL,
    name VARCHAR(200) NOT NULL COMMENT '鹅群名称',
    breed VARCHAR(100) DEFAULT NULL COMMENT '品种',
    hatch_date DATE DEFAULT NULL COMMENT '孵化日期',
    initial_count INT DEFAULT 0 COMMENT '初始数量',
    current_count INT DEFAULT 0 COMMENT '当前数量',
    status ENUM('active', 'inactive', 'sold', 'deceased') DEFAULT 'active' COMMENT '状态',
    location VARCHAR(500) DEFAULT NULL COMMENT '位置',
    notes TEXT DEFAULT NULL COMMENT '备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='鹅群管理表';

-- 2. 创建 mall_orders 表 (商城订单)
CREATE TABLE IF NOT EXISTS mall_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL,
    user_id INT NOT NULL,
    order_no VARCHAR(100) NOT NULL UNIQUE COMMENT '订单号',
    total_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
    status ENUM('pending', 'paid', 'shipped', 'completed', 'cancelled', 'refunded') DEFAULT 'pending' COMMENT '订单状态',
    payment_method VARCHAR(50) DEFAULT NULL COMMENT '支付方式',
    payment_status ENUM('unpaid', 'paid', 'refunded', 'partial_refund') DEFAULT 'unpaid' COMMENT '支付状态',
    shipping_address TEXT DEFAULT NULL COMMENT '配送地址',
    shipping_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '配送费',
    notes TEXT DEFAULT NULL COMMENT '备注',
    paid_at DATETIME DEFAULT NULL COMMENT '支付时间',
    shipped_at DATETIME DEFAULT NULL COMMENT '发货时间',
    completed_at DATETIME DEFAULT NULL COMMENT '完成时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_user_id (user_id),
    INDEX idx_order_no (order_no),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商城订单表';

-- 3. 创建 goose_prices 表 (鹅价行情)
CREATE TABLE IF NOT EXISTS goose_prices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL COMMENT '日期',
    region VARCHAR(100) NOT NULL COMMENT '地区',
    breed VARCHAR(100) NOT NULL COMMENT '品种',
    price_per_kg DECIMAL(8,2) NOT NULL COMMENT '每公斤价格',
    market_name VARCHAR(200) DEFAULT NULL COMMENT '市场名称',
    is_published BOOLEAN DEFAULT FALSE COMMENT '是否发布',
    data_source VARCHAR(100) DEFAULT 'manual' COMMENT '数据来源',
    notes TEXT DEFAULT NULL COMMENT '备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_date (date),
    INDEX idx_region (region),
    INDEX idx_breed (breed),
    INDEX idx_published (is_published),
    UNIQUE KEY uk_date_region_breed (date, region, breed)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='鹅价行情表';

-- 4. 创建 users 表 (用户表，用于 mall_orders 外键)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL,
    openid VARCHAR(100) DEFAULT NULL COMMENT '微信openid',
    unionid VARCHAR(100) DEFAULT NULL COMMENT '微信unionid',
    nickname VARCHAR(100) DEFAULT NULL COMMENT '昵称',
    full_name VARCHAR(100) DEFAULT NULL COMMENT '真实姓名',
    phone VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    email VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    avatar VARCHAR(500) DEFAULT NULL COMMENT '头像',
    role ENUM('user', 'manager', 'admin') DEFAULT 'user' COMMENT '角色',
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '状态',
    last_login_at DATETIME DEFAULT NULL COMMENT '最后登录时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_openid (openid),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 5. 创建 health_records 表 (健康记录)
CREATE TABLE IF NOT EXISTS health_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL,
    flock_id INT DEFAULT NULL,
    record_type ENUM('checkup', 'vaccination', 'treatment', 'death', 'disease') NOT NULL COMMENT '记录类型',
    title VARCHAR(200) NOT NULL COMMENT '标题',
    description TEXT DEFAULT NULL COMMENT '描述',
    symptoms TEXT DEFAULT NULL COMMENT '症状',
    diagnosis TEXT DEFAULT NULL COMMENT '诊断',
    treatment TEXT DEFAULT NULL COMMENT '治疗方案',
    medication TEXT DEFAULT NULL COMMENT '用药记录',
    affected_count INT DEFAULT 0 COMMENT '影响数量',
    temperature DECIMAL(5,2) DEFAULT NULL COMMENT '体温',
    weight DECIMAL(8,2) DEFAULT NULL COMMENT '体重',
    photos JSON DEFAULT NULL COMMENT '照片',
    status ENUM('pending', 'treating', 'recovered', 'deceased') DEFAULT 'pending' COMMENT '状态',
    recorded_by VARCHAR(100) DEFAULT NULL COMMENT '记录人',
    recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_flock_id (flock_id),
    INDEX idx_type (record_type),
    INDEX idx_status (status),
    INDEX idx_recorded_at (recorded_at),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (flock_id) REFERENCES flocks(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='健康记录表';

-- 6. 创建 production_records 表 (生产记录)
CREATE TABLE IF NOT EXISTS production_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL,
    flock_id INT DEFAULT NULL,
    record_type ENUM('egg', 'weight', 'feed', 'environment', 'mortality', 'income', 'expense') NOT NULL COMMENT '记录类型',
    title VARCHAR(200) NOT NULL COMMENT '标题',
    quantity INT DEFAULT 0 COMMENT '数量',
    weight DECIMAL(10,2) DEFAULT NULL COMMENT '重量(kg)',
    unit VARCHAR(20) DEFAULT 'piece' COMMENT '单位',
    unit_price DECIMAL(10,2) DEFAULT NULL COMMENT '单价',
    total_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT '总金额',
    temperature DECIMAL(5,2) DEFAULT NULL COMMENT '温度',
    humidity DECIMAL(5,2) DEFAULT NULL COMMENT '湿度',
    notes TEXT DEFAULT NULL COMMENT '备注',
    photos JSON DEFAULT NULL COMMENT '照片',
    recorded_by VARCHAR(100) DEFAULT NULL COMMENT '记录人',
    recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_flock_id (flock_id),
    INDEX idx_type (record_type),
    INDEX idx_recorded_at (recorded_at),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (flock_id) REFERENCES flocks(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生产记录表';

-- 7. 创建 mall_products 表 (商城商品)
CREATE TABLE IF NOT EXISTS mall_products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT DEFAULT NULL COMMENT '租户ID(NULL表示平台商品)',
    name VARCHAR(200) NOT NULL COMMENT '商品名称',
    description TEXT DEFAULT NULL COMMENT '商品描述',
    category VARCHAR(100) DEFAULT NULL COMMENT '分类',
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '价格',
    original_price DECIMAL(10,2) DEFAULT NULL COMMENT '原价',
    stock INT DEFAULT 0 COMMENT '库存',
    sold_count INT DEFAULT 0 COMMENT '销量',
    images JSON DEFAULT NULL COMMENT '商品图片',
    specifications JSON DEFAULT NULL COMMENT '规格参数',
    status ENUM('active', 'inactive', 'sold_out') DEFAULT 'active' COMMENT '状态',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商城商品表';

-- 8. 创建 mall_order_items 表 (订单商品)
CREATE TABLE IF NOT EXISTS mall_order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    product_image VARCHAR(500) DEFAULT NULL COMMENT '商品图片',
    price DECIMAL(10,2) NOT NULL COMMENT '单价',
    quantity INT NOT NULL DEFAULT 1 COMMENT '数量',
    total_amount DECIMAL(12,2) NOT NULL COMMENT '小计',
    specifications JSON DEFAULT NULL COMMENT '规格',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id),
    FOREIGN KEY (order_id) REFERENCES mall_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES mall_products(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单商品表';

-- 9. 创建 vaccination_templates 表 (防疫模板)
CREATE TABLE IF NOT EXISTS vaccination_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL COMMENT '模板名称',
    description TEXT DEFAULT NULL COMMENT '描述',
    breed VARCHAR(100) DEFAULT NULL COMMENT '适用品种',
    age_range VARCHAR(100) DEFAULT NULL COMMENT '适用日龄',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认模板',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_is_default (is_default)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='防疫模板表';

-- 10. 创建 vaccination_steps 表 (防疫步骤)
CREATE TABLE IF NOT EXISTS vaccination_steps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_id INT NOT NULL,
    step_order INT NOT NULL COMMENT '步骤顺序',
    name VARCHAR(200) NOT NULL COMMENT '步骤名称',
    vaccine_name VARCHAR(200) NOT NULL COMMENT '疫苗名称',
    age_days INT NOT NULL COMMENT '日龄',
    dosage VARCHAR(100) DEFAULT NULL COMMENT '剂量',
    method VARCHAR(100) DEFAULT NULL COMMENT '接种方式',
    notes TEXT DEFAULT NULL COMMENT '注意事项',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_template_id (template_id),
    INDEX idx_step_order (step_order),
    FOREIGN KEY (template_id) REFERENCES vaccination_templates(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='防疫步骤表';

-- 11. 创建 flock_vaccinations 表 (鹅群防疫计划)
CREATE TABLE IF NOT EXISTS flock_vaccinations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL,
    flock_id INT NOT NULL,
    template_id INT NOT NULL,
    start_date DATE NOT NULL COMMENT '开始日期',
    status ENUM('active', 'completed', 'cancelled') DEFAULT 'active' COMMENT '状态',
    created_by VARCHAR(100) DEFAULT NULL COMMENT '创建人',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_flock_id (flock_id),
    INDEX idx_template_id (template_id),
    INDEX idx_status (status),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (flock_id) REFERENCES flocks(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES vaccination_templates(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='鹅群防疫计划表';

-- 12. 创建 vaccination_tasks 表 (防疫任务)
CREATE TABLE IF NOT EXISTS vaccination_tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL,
    flock_vaccination_id INT NOT NULL,
    vaccination_step_id INT NOT NULL,
    flock_id INT NOT NULL,
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    vaccine_name VARCHAR(200) NOT NULL COMMENT '疫苗名称',
    scheduled_date DATE NOT NULL COMMENT '计划日期',
    status ENUM('pending', 'in_progress', 'completed', 'skipped', 'overdue') DEFAULT 'pending' COMMENT '状态',
    completed_at DATETIME DEFAULT NULL COMMENT '完成时间',
    completed_by VARCHAR(100) DEFAULT NULL COMMENT '完成人',
    actual_count INT DEFAULT 0 COMMENT '实际接种数量',
    notes TEXT DEFAULT NULL COMMENT '备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_flock_id (flock_id),
    INDEX idx_scheduled_date (scheduled_date),
    INDEX idx_status (status),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (flock_vaccination_id) REFERENCES flock_vaccinations(id) ON DELETE CASCADE,
    FOREIGN KEY (vaccination_step_id) REFERENCES vaccination_steps(id) ON DELETE CASCADE,
    FOREIGN KEY (flock_id) REFERENCES flocks(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='防疫任务表';

-- 插入示例数据

-- 插入防疫模板数据
INSERT INTO vaccination_templates (name, description, breed, age_range, is_default, status) VALUES
('标准鹅防疫程序', '适用于大部分鹅种的标准防疫流程', '通用', '1-120日龄', TRUE, 'active'),
('高产蛋鹅防疫程序', '针对蛋鹅的专门防疫流程', '蛋鹅', '1-300日龄', FALSE, 'active'),
('肉鹅快速出栏程序', '肉鹅60天出栏防疫程序', '肉鹅', '1-60日龄', FALSE, 'active');

-- 插入防疫步骤数据
INSERT INTO vaccination_steps (template_id, step_order, name, vaccine_name, age_days, dosage, method, notes) VALUES
-- 标准防疫程序
(1, 1, '小鹅瘟首免', '小鹅瘟疫苗', 1, '0.5ml/只', '皮下注射', '出壳后24小时内接种'),
(1, 2, '禽流感首免', 'H5N1禽流感疫苗', 7, '0.3ml/只', '皮下注射', '注意保存温度'),
(1, 3, '小鹅瘟二免', '小鹅瘟疫苗', 14, '1ml/只', '肌肉注射', '加强免疫'),
(1, 4, '禽流感二免', 'H5N1禽流感疫苗', 21, '0.5ml/只', '肌肉注射', '产蛋前完成'),
(1, 5, '鹅副粘病毒病疫苗', '鹅副粘病毒病疫苗', 35, '1ml/只', '肌肉注射', '预防呼吸道疾病'),
(1, 6, '禽霍乱疫苗', '禽霍乱疫苗', 60, '0.5ml/只', '皮下注射', '预防细菌性疾病'),

-- 高产蛋鹅防疫程序
(2, 1, '小鹅瘟首免', '小鹅瘟疫苗', 1, '0.5ml/只', '皮下注射', '出壳后24小时内接种'),
(2, 2, '禽流感首免', 'H5N1禽流感疫苗', 7, '0.3ml/只', '皮下注射', '注意保存温度'),
(2, 3, '小鹅瘟二免', '小鹅瘟疫苗', 14, '1ml/只', '肌肉注射', '加强免疫'),
(2, 4, '禽流感二免', 'H5N1禽流感疫苗', 21, '0.5ml/只', '肌肉注射', '产蛋前完成'),
(2, 5, '鹅副粘病毒病疫苗', '鹅副粘病毒病疫苗', 35, '1ml/只', '肌肉注射', '预防呼吸道疾病'),
(2, 6, '禽霍乱疫苗', '禽霍乱疫苗', 60, '0.5ml/只', '皮下注射', '预防细菌性疾病'),
(2, 7, '产蛋期加强免疫', '禽流感+新城疫联苗', 120, '1ml/只', '肌肉注射', '产蛋期加强保护'),
(2, 8, '年度加强免疫', '小鹅瘟+禽流感联苗', 300, '1ml/只', '肌肉注射', '成鹅年度免疫'),

-- 肉鹅快速出栏程序  
(3, 1, '小鹅瘟首免', '小鹅瘟疫苗', 1, '0.5ml/只', '皮下注射', '出壳后24小时内接种'),
(3, 2, '禽流感首免', 'H5N1禽流感疫苗', 7, '0.3ml/只', '皮下注射', '注意保存温度'),
(3, 3, '小鹅瘟二免', '小鹅瘟疫苗', 14, '1ml/只', '肌肉注射', '加强免疫'),
(3, 4, '禽流感二免', 'H5N1禽流感疫苗', 30, '0.5ml/只', '肌肉注射', '出栏前保护'),
(3, 5, '鹅副粘病毒病疫苗', '鹅副粘病毒病疫苗', 45, '1ml/只', '肌肉注射', '出栏前最后免疫');

-- 插入示例鹅价数据
INSERT INTO goose_prices (date, region, breed, price_per_kg, market_name, is_published, data_source) VALUES
(CURDATE(), '江苏南京', '扬州鹅', 28.50, '南京农副产品批发市场', TRUE, 'market'),
(CURDATE(), '江苏南京', '太湖鹅', 26.80, '南京农副产品批发市场', TRUE, 'market'),
(CURDATE(), '广东广州', '马岗鹅', 32.00, '广州江南果菜批发市场', TRUE, 'market'),
(CURDATE(), '山东济南', '豁眼鹅', 24.50, '济南七里堡批发市场', TRUE, 'market'),
(CURDATE(), '安徽合肥', '皖西白鹅', 27.30, '合肥周谷堆批发市场', TRUE, 'market'),
(DATE_SUB(CURDATE(), INTERVAL 1 DAY), '江苏南京', '扬州鹅', 28.00, '南京农副产品批发市场', TRUE, 'market'),
(DATE_SUB(CURDATE(), INTERVAL 1 DAY), '广东广州', '马岗鹅', 31.50, '广州江南果菜批发市场', TRUE, 'market'),
(DATE_SUB(CURDATE(), INTERVAL 2 DAY), '江苏南京', '扬州鹅', 27.80, '南京农副产品批发市场', TRUE, 'market'),
(DATE_SUB(CURDATE(), INTERVAL 2 DAY), '广东广州', '马岗鹅', 31.20, '广州江南果菜批发市场', TRUE, 'market');

-- 插入示例商品数据
INSERT INTO mall_products (tenant_id, name, description, category, price, original_price, stock, images, status, is_featured) VALUES
(NULL, '优质鹅苗', '健康扬州鹅苗，疫苗齐全', '鹅苗', 15.00, 18.00, 1000, '["https://example.com/goose1.jpg"]', 'active', TRUE),
(NULL, '鹅用疫苗套装', '包含小鹅瘟、禽流感等常用疫苗', '疫苗', 45.00, 50.00, 200, '["https://example.com/vaccine1.jpg"]', 'active', TRUE),
(NULL, '鹅用复合饲料', '营养均衡的鹅用全价饲料', '饲料', 3.20, 3.50, 5000, '["https://example.com/feed1.jpg"]', 'active', FALSE),
(NULL, '养殖围网', '高强度聚乙烯养殖围网', '设备', 12.50, 15.00, 800, '["https://example.com/net1.jpg"]', 'active', FALSE),
(NULL, '自动饮水器', '不锈钢自动饮水设备', '设备', 85.00, 95.00, 150, '["https://example.com/water1.jpg"]', 'active', TRUE);

-- 根据Playwright探索发现的问题，添加额外的表和字段

-- 添加知识库相关表
CREATE TABLE IF NOT EXISTS knowledge_articles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL COMMENT '文章标题',
    content TEXT NOT NULL COMMENT '文章内容',
    summary TEXT COMMENT '文章摘要',
    category_id INT DEFAULT NULL COMMENT '分类ID',
    author_id INT DEFAULT NULL COMMENT '作者ID',
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '状态',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库文章表';

CREATE TABLE IF NOT EXISTS knowledge_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库分类表';

CREATE TABLE IF NOT EXISTS knowledge_tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '标签名称',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库标签表';

-- 添加公告表
CREATE TABLE IF NOT EXISTS announcements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL COMMENT '公告标题',
    content TEXT NOT NULL COMMENT '公告内容',
    type ENUM('system', 'maintenance', 'feature', 'notice') DEFAULT 'system' COMMENT '公告类型',
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium' COMMENT '优先级',
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '状态',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统公告表';

-- 添加商城分类表
CREATE TABLE IF NOT EXISTS mall_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商城分类表';

-- 添加平台用户表
CREATE TABLE IF NOT EXISTS platform_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    full_name VARCHAR(100) COMMENT '全名',
    role ENUM('admin', 'operator', 'viewer') DEFAULT 'viewer' COMMENT '角色',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台用户表';

-- 修复字段问题
-- 修复 flocks 表的 currentCount 字段
ALTER TABLE flocks CHANGE COLUMN current_count currentCount INT DEFAULT 0 COMMENT '当前数量';

-- 修复 goose_prices 表添加 unit 字段
ALTER TABLE goose_prices ADD COLUMN IF NOT EXISTS unit VARCHAR(20) DEFAULT 'kg' COMMENT '价格单位' AFTER price_per_kg;

-- 修复 mall_products 表添加缺失字段
ALTER TABLE mall_products ADD COLUMN IF NOT EXISTS category_id INT COMMENT '分类ID' AFTER name;
ALTER TABLE mall_products ADD COLUMN IF NOT EXISTS stock_qty INT DEFAULT 0 COMMENT '库存数量' AFTER price;

-- 修复 tenants 表添加 company_name 字段 (如果不存在)
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS company_name VARCHAR(200) COMMENT '公司名称' AFTER id;

COMMIT;

-- 显示创建结果
SELECT 'Missing tables created successfully!' as message;