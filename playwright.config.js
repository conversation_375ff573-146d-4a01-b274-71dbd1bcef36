// @ts-check
const { defineConfig, devices } = require('@playwright/test');

/**
 * Playwright配置文件 - 后台管理中心功能审查
 * 基于Context7最佳实践配置的自动化测试环境
 */
module.exports = defineConfig({
  testDir: './tests',
  
  /* 并行运行测试 */
  fullyParallel: false, // 审查测试需要顺序执行
  
  /* 失败时不重试 */
  retries: 0,
  
  /* 测试超时设置 */
  timeout: 30000,
  expect: {
    timeout: 10000
  },
  
  /* 报告配置 */
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['json', { outputFile: 'test-results/test-results.json' }],
    ['list']
  ],
  
  /* 全局设置 */
  use: {
    /* 基础URL */
    baseURL: 'http://localhost:3003',
    
    /* 浏览器设置 */
    headless: false, // 显示浏览器窗口以便观察测试过程
    viewport: { width: 1280, height: 720 },
    
    /* 截图和视频 */
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    
    /* 网络设置 */
    ignoreHTTPSErrors: true,
    
    /* 等待设置 */
    actionTimeout: 10000,
    navigationTimeout: 30000,
  },

  /* 测试项目配置 */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    
    /* 可选：其他浏览器测试
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    */
    
    /* 移动端测试 */
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
  ],

  /* 本地开发服务器配置 */
  webServer: [
    {
      command: 'cd backend/saas-admin && PORT=3001 node server.js',
      port: 3001,
      reuseExistingServer: !process.env.CI,
      timeout: 30000,
    },
    {
      command: 'python3 -m http.server 3000',
      port: 3000,
      reuseExistingServer: !process.env.CI,
      timeout: 30000,
    }
  ],
  
  /* 输出目录 */
  outputDir: 'test-results/',
  
  /* 全局设置和清理 */
  globalSetup: require.resolve('./tests/global-setup.js'),
  globalTeardown: require.resolve('./tests/global-teardown.js'),
});
