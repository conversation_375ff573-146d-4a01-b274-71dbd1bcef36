{"env": {"CLAUDE_DEFAULT_LANGUAGE": "zh-CN", "PROJECT_LANGUAGE": "Chinese"}, "permissions": {"allow": ["Bash(node:*)", "Bash(brew services:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm start:*)", "WebSearch", "<PERSON><PERSON>(timeout 10 npm start)", "WebFetch(domain:developers.weixin.qq.com)", "Bash(grep:*)", "<PERSON><PERSON>(timeout:*)", "WebFetch(domain:weui.io)", "Bash(rm:*)", "Bash(npm:*)", "Bash(gtimeout 30s npm install --production)", "<PERSON><PERSON>(sudo:*)"], "deny": [], "ask": []}, "hooks": {"SessionStart": [{"matcher": "*", "hooks": [{"type": "command", "command": "echo '提醒：本项目优先使用中文交流和代码注释'"}]}]}}