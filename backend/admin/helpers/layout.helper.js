const fs = require('fs');
const path = require('path');
const ejs = require('ejs');

class LayoutHelper {
  constructor() {
    this.layoutPath = path.join(__dirname, '../views/layout.ejs');
    this.layoutTemplate = null;
    this.loadLayout();
  }

  loadLayout() {
    try {
      this.layoutTemplate = fs.readFileSync(this.layoutPath, 'utf8');
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('Failed to load layout template:', error); } catch(_) {}

      this.layoutTemplate = null;
    }
  }

  render(viewPath, data = {}) {
    try {
      // 读取视图文件
      const viewTemplate = fs.readFileSync(viewPath, 'utf8');

      // 移除layout指令
      const cleanViewTemplate = viewTemplate.replace(
        /<% layout\('layout'\) -%>\s*\n?/,
        ''
      );

      // 渲染视图内容
      const viewContent = ejs.render(cleanViewTemplate, data);

      // 如果有layout模板，将视图内容插入layout
      if (this.layoutTemplate) {
        const layoutData = {
          ...data,
          body: viewContent
        };
        return ejs.render(this.layoutTemplate, layoutData);
      } else {
        // 如果没有layout，直接返回视图内容
        return viewContent;
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('Layout render error:', error); } catch(_) {}

      throw error;
    }
  }
}

module.exports = new LayoutHelper();
