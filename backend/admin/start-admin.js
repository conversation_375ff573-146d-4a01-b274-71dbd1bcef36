const app = require('./app');

app.listen(PORT, () => {
  try { const { Logger } = require('./middleware/errorHandler'); Logger.info('🚀 智慧养鹅管理后台启动成功！'); } catch(_) {}

  try { const { Logger } = require('./middleware/errorHandler'); Logger.info(`📊 管理后台地址: http://localhost:${PORT}`); } catch(_) {}

  try { const { Logger } = require('./middleware/errorHandler'); Logger.info(`🔧 环境: ${process.env.NODE_ENV || 'development'}`); } catch(_) {}

  try { const { Logger } = require('./middleware/errorHandler'); Logger.info(`📅 启动时间: ${new Date().toLocaleString()}`); } catch(_) {}

});
