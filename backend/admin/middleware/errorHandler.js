/**
 * 错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('Admin error occurred', { error: err.message, stack: err.stack, path: req.originalUrl, method: req.method }); } catch(_) {}

  // 设置默认错误状态码
  let status = err.status || err.statusCode || 500;
  let message = err.message || '服务器内部错误';

  // 开发环境显示详细错误信息
  const isDevelopment = process.env.NODE_ENV === 'development';

  // 根据错误类型设置状态码和消息
  if (err.name === 'ValidationError') {
    status = 400;
    message = '数据验证失败';
  } else if (err.name === 'UnauthorizedError') {
    status = 401;
    message = '未授权访问';
  } else if (err.name === 'CastError') {
    status = 400;
    message = '无效的数据格式';
  }

  // 如果是AJAX请求，返回JSON格式错误
  if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
    return res.status(status).json({
      success: false,
      error: {
        status: status,
        message: message,
        ...(isDevelopment && { stack: err.stack })
      }
    });
  }

  // 渲染错误页面
  res.status(status).render('error', {
    title: `错误 ${status} - 智慧养鹅管理系统`,
    error: {
      status: status,
      message: message,
      stack: isDevelopment ? err.stack : ''
    }
  });
};

module.exports = errorHandler;
