/**
 * 通用中间件
 * 包含认证、权限、日志等通用功能
 */

const { renderError, errorResponse, log } = require('../utils/response-helper');

/**
 * 认证中间件 - 检查用户登录状态
 */
function requireAuth(req, res, next) {
  if (!req.session.user || !req.session.token) {
    if (req.headers['content-type'] === 'application/json' || req.xhr) {
      // API请求返回JSON错误
      return errorResponse(
        res,
        '未登录或登录已过期',
        401,
        null,
        'UNAUTHORIZED'
      );
    } else {
      // 页面请求重定向到登录页
      return res.redirect('/login');
    }
  }
  next();
}

/**
 * 权限检查中间件
 * @param {string|Array} roles 允许的角色
 */
function requireRole(roles) {
  const allowedRoles = Array.isArray(roles) ? roles : [roles];

  return (req, res, next) => {
    if (!req.session.user) {
      return errorResponse(res, '未登录', 401, null, 'UNAUTHORIZED');
    }

    const userRole = req.session.user.role;
    if (!allowedRoles.includes(userRole)) {
      if (req.headers['content-type'] === 'application/json' || req.xhr) {
        return errorResponse(res, '权限不足', 403, null, 'FORBIDDEN');
      } else {
        return renderError(res, new Error('权限不足'), 403);
      }
    }

    next();
  };
}

/**
 * 用户信息注入中间件
 * 将用户信息注入到res.locals中，方便模板使用
 */
function injectUserInfo(req, res, next) {
  res.locals.user = req.session.user || null;
  res.locals.isLoggedIn = !!req.session.user;
  res.locals.currentPath = req.path;
  res.locals.query = req.query;
  next();
}

/**
 * 请求日志中间件
 */
function requestLogger(req, res, next) {
  const startTime = Date.now();

  // 记录请求开始
  log('info', 'Request started', {
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    user: req.session.user?.username || 'anonymous'
  });

  // 监听响应结束
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    log('info', 'Request completed', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      user: req.session.user?.username || 'anonymous'
    });
  });

  next();
}

/**
 * 错误处理中间件
 */
function errorHandler(err, req, res, next) {
  // 记录错误日志
  log('error', 'Unhandled error', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    user: req.session.user?.username || 'anonymous'
  });

  // 根据请求类型返回不同格式的错误响应
  if (req.headers['content-type'] === 'application/json' || req.xhr) {
    // API请求返回JSON错误
    errorResponse(res, err.message, 500, null, 'INTERNAL_ERROR');
  } else {
    // 页面请求渲染错误页面
    renderError(res, err, 500);
  }
}

/**
 * 数据缓存中间件
 * @param {number} ttl 缓存时间（秒）
 * @param {string} keyGenerator 缓存键生成函数
 */
function cache(ttl = 300, keyGenerator = null) {
  const cacheStore = new Map();

  return (req, res, next) => {
    const key = keyGenerator
      ? keyGenerator(req)
      : `${req.method}:${req.originalUrl}`;
    const cached = cacheStore.get(key);

    if (cached && Date.now() - cached.timestamp < ttl * 1000) {
      log('info', 'Cache hit', { key });
      return res.json(cached.data);
    }

    // 重写res.json以拦截响应
    const originalJson = res.json;
    res.json = function (data) {
      if (res.statusCode === 200) {
        cacheStore.set(key, {
          data,
          timestamp: Date.now()
        });
        log('info', 'Cache set', { key });
      }
      return originalJson.call(this, data);
    };

    next();
  };
}

/**
 * API限流中间件
 * @param {number} maxRequests 最大请求数
 * @param {number} windowMs 时间窗口（毫秒）
 */
function rateLimit(maxRequests = 100, windowMs = 60000) {
  const requests = new Map();

  return (req, res, next) => {
    const key = `${req.ip}:${req.session.user?.id || 'anonymous'}`;
    const now = Date.now();
    const userRequests = requests.get(key) || [];

    // 清理过期的请求记录
    const validRequests = userRequests.filter((time) => now - time < windowMs);

    if (validRequests.length >= maxRequests) {
      log('warn', 'Rate limit exceeded', {
        ip: req.ip,
        user: req.session.user?.username || 'anonymous',
        requests: validRequests.length
      });
      return errorResponse(
        res,
        '请求过于频繁，请稍后再试',
        429,
        null,
        'RATE_LIMIT_EXCEEDED'
      );
    }

    validRequests.push(now);
    requests.set(key, validRequests);

    next();
  };
}

/**
 * 操作审计中间件
 * 记录重要操作的审计日志
 */
function auditLog(action, resourceType) {
  return (req, res, next) => {
    // 记录操作开始
    const auditData = {
      action,
      resourceType,
      user: req.session.user?.username || 'anonymous',
      userId: req.session.user?.id,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString(),
      requestData: req.body,
      params: req.params
    };

    log('audit', `${action} ${resourceType}`, auditData);

    // 监听响应结束，记录操作结果
    res.on('finish', () => {
      log('audit', `${action} ${resourceType} completed`, {
        ...auditData,
        statusCode: res.statusCode,
        success: res.statusCode < 400
      });
    });

    next();
  };
}

module.exports = {
  requireAuth,
  requireRole,
  injectUserInfo,
  requestLogger,
  errorHandler,
  cache,
  rateLimit,
  auditLog
};
