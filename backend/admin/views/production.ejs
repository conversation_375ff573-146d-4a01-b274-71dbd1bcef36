<% layout('layout') -%>

<div class="row mb-3">
    <div class="col-md-6">
        <div class="btn-group">
            <button class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i> 添加孵化记录
            </button>
            <button class="btn btn-success">
                <i class="bi bi-plus-circle me-1"></i> 添加生长记录
            </button>
            <button class="btn btn-info">
                <i class="bi bi-plus-circle me-1"></i> 添加出栏记录
            </button>
        </div>
    </div>
    <div class="col-md-6">
        <div class="row">
            <div class="col-5">
                <select class="form-select">
                    <option>全部类型</option>
                    <option>孵化</option>
                    <option>生长</option>
                    <option>出栏</option>
                </select>
            </div>
            <div class="col-7">
                <div class="input-group">
                    <input type="date" class="form-control">
                    <span class="input-group-text">至</span>
                    <input type="date" class="form-control">
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card border-primary">
            <div class="card-body text-center">
                <h5 class="card-title">孵化总数</h5>
                <h2 class="text-primary">12,580</h2>
                <p class="card-text">只</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card border-success">
            <div class="card-body text-center">
                <h5 class="card-title">当前存栏</h5>
                <h2 class="text-success">8,650</h2>
                <p class="card-text">只</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card border-info">
            <div class="card-body text-center">
                <h5 class="card-title">本月出栏</h5>
                <h2 class="text-info">3,240</h2>
                <p class="card-text">只</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card border-warning">
            <div class="card-body text-center">
                <h5 class="card-title">平均重量</h5>
                <h2 class="text-warning">3.2</h2>
                <p class="card-text">公斤</p>
            </div>
        </div>
    </div>
</div>

<ul class="nav nav-tabs mb-4" id="productionTab" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="hatching-tab" data-bs-toggle="tab" data-bs-target="#hatching" type="button" role="tab">孵化记录</button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="growth-tab" data-bs-toggle="tab" data-bs-target="#growth" type="button" role="tab">生长记录</button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="sale-tab" data-bs-toggle="tab" data-bs-target="#sale" type="button" role="tab">出栏记录</button>
    </li>
</ul>

<div class="tab-content" id="productionTabContent">
    <div class="tab-pane fade show active" id="hatching" role="tabpanel">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>记录ID</th>
                                <th>用户</th>
                                <th>批次编号</th>
                                <th>孵化数量</th>
                                <th>孵化率</th>
                                <th>健康率</th>
                                <th>记录时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2001</td>
                                <td>张三</td>
                                <td>H2023061501</td>
                                <td>1,000</td>
                                <td>92.5%</td>
                                <td>98.2%</td>
                                <td>2023-06-15</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">查看</button>
                                    <button class="btn btn-sm btn-outline-danger">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2002</td>
                                <td>李四</td>
                                <td>H2023061401</td>
                                <td>800</td>
                                <td>89.8%</td>
                                <td>96.5%</td>
                                <td>2023-06-14</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">查看</button>
                                    <button class="btn btn-sm btn-outline-danger">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="tab-pane fade" id="growth" role="tabpanel">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>记录ID</th>
                                <th>用户</th>
                                <th>批次编号</th>
                                <th>平均体重</th>
                                <th>料肉比</th>
                                <th>死亡率</th>
                                <th>记录时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>3001</td>
                                <td>张三</td>
                                <td>G2023061501</td>
                                <td>2.8kg</td>
                                <td>2.1:1</td>
                                <td>1.2%</td>
                                <td>2023-06-15</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">查看</button>
                                    <button class="btn btn-sm btn-outline-danger">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td>3002</td>
                                <td>李四</td>
                                <td>G2023061401</td>
                                <td>2.5kg</td>
                                <td>2.3:1</td>
                                <td>2.1%</td>
                                <td>2023-06-14</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">查看</button>
                                    <button class="btn btn-sm btn-outline-danger">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="tab-pane fade" id="sale" role="tabpanel">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>记录ID</th>
                                <th>用户</th>
                                <th>批次编号</th>
                                <th>出栏数量</th>
                                <th>平均重量</th>
                                <th>单价</th>
                                <th>总金额</th>
                                <th>记录时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>4001</td>
                                <td>张三</td>
                                <td>S2023061501</td>
                                <td>500</td>
                                <td>3.2kg</td>
                                <td>¥25.0</td>
                                <td>¥12,500</td>
                                <td>2023-06-15</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">查看</button>
                                    <button class="btn btn-sm btn-outline-danger">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-size: .875rem;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
        }
        
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .sidebar .nav-link {
            font-weight: 500;
            color: #333;
        }
        
        .sidebar .nav-link.active {
            color: #0066cc;
        }
        
        .sidebar-heading {
            font-size: .75rem;
            text-transform: uppercase;
        }
        
        .navbar-brand {
            padding-top: .75rem;
            padding-bottom: .75rem;
            font-size: 1rem;
            background-color: rgba(0, 0, 0, .25);
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .navbar-brand:hover {
            background-color: rgba(0, 0, 0, .35);
        }
        
        .stat-card {
            border-radius: 10px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
        }
        
        /* 图标管理样式 */
        .icon-preview {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 5px;
            margin-bottom: 15px;
            transition: all 0.3s;
        }
        
        .icon-item:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-color: #0066cc;
        }
        
        .icon-name {
            font-size: 0.8rem;
            margin-top: 8px;
            color: #666;
        }
    </style>
</head>
<body>
    <header class="navbar navbar-dark sticky-top bg-primary flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="/admin">智慧养鹅管理系统</a>
        <button class="navbar-toggler d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="navbar-nav">
            <div class="nav-item text-nowrap">
                <a class="nav-link px-3" href="#">退出登录</a>
            </div>
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'home') ? 'active' : '' %>" href="/admin">
                                <i class="bi bi-speedometer2 me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'users') ? 'active' : '' %>" href="/admin/users">
                                <i class="bi bi-people me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'health') ? 'active' : '' %>" href="/admin/health">
                                <i class="bi bi-heart-pulse me-2"></i>
                                健康管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'production') ? 'active' : '' %>" href="/admin/production">
                                <i class="bi bi-egg me-2"></i>
                                生产管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'icons') ? 'active' : '' %>" href="/admin/icons">
                                <i class="bi bi-image me-2"></i>
                                图标管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'settings') ? 'active' : '' %>" href="/admin/settings">
                                <i class="bi bi-gear me-2"></i>
                                系统设置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><%= title %></h1>
                </div>
                
                <%- typeof body !== 'undefined' ? body : '' %>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</body>
</html>