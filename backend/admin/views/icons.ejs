<% layout('layout') -%>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">图标管理</h5>
                    <a href="/admin/icons/help" class="btn btn-sm btn-outline-info">
                        <i class="bi bi-question-circle me-1"></i>使用说明
                    </a>
                </div>
            </div>
            <div class="card-body">
                <p>在此页面可以管理和替换小程序中使用的图标。系统会自动检测现有的图标文件，并允许您上传新的图标来替换它们。</p>
                
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    请确保上传的图标文件满足以下要求：
                    <ul class="mb-0">
                        <li>文件格式：PNG</li>
                        <li>推荐尺寸：48x48 像素</li>
                        <li>背景透明</li>
                        <li>文件名应与现有图标保持一致</li>
                    </ul>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <button class="btn btn-primary" onclick="refreshIcons()">
                            <i class="bi bi-arrow-clockwise me-1"></i>刷新列表
                        </button>
                    </div>
                    <div class="form-text">
                        共找到 <span id="iconCount">0</span> 个图标
                    </div>
                </div>
                
                <div class="row" id="iconsContainer">
                    <!-- 图标列表将通过JavaScript动态加载 -->
                    <div class="col-12 text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载图标列表...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 替换图标模态框 -->
<div class="modal fade" id="replaceIconModal" tabindex="-1" aria-labelledby="replaceIconModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="replaceIconModalLabel">替换图标</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <div class="icon-preview mx-auto mb-2">
                        <img id="currentIconPreview" src="" alt="当前图标" style="max-width: 100%; max-height: 100%;">
                    </div>
                    <div id="currentIconName" class="fw-bold"></div>
                    <div id="currentIconUsage" class="text-muted small"></div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">选择新图标文件</label>
                    <input class="form-control" type="file" id="newIconFile" accept="image/png">
                    <div class="form-text">请上传PNG格式的图片文件，推荐尺寸48x48像素</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadNewIcon()">上传替换</button>
            </div>
        </div>
    </div>
</div>

<script>
// 当前选中的图标信息
let currentIcon = null;

// 模拟图标数据（实际应用中应从服务器获取）
let iconsData = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadIcons();
});

// 加载图标列表
function loadIcons() {
    // 模拟从服务器获取数据
    setTimeout(() => {
        iconsData = [
            { id: 'home', name: '首页图标', path: '/assets/icons/home.png', usage: '底部导航栏' },
            { id: 'home_selected', name: '首页选中图标', path: '/assets/icons/home_selected.png', usage: '底部导航栏' },
            { id: 'health', name: '健康图标', path: '/assets/icons/health.png', usage: '底部导航栏、首页快捷功能' },
            { id: 'health_selected', name: '健康选中图标', path: '/assets/icons/health_selected.png', usage: '底部导航栏' },
            { id: 'production', name: '生产图标', path: '/assets/icons/production.png', usage: '底部导航栏、首页快捷功能' },
            { id: 'production_selected', name: '生产选中图标', path: '/assets/icons/production_selected.png', usage: '底部导航栏' },
            { id: 'shop', name: '商城图标', path: '/assets/icons/shop.png', usage: '底部导航栏、首页快捷功能' },
            { id: 'shop_selected', name: '商城选中图标', path: '/assets/icons/shop_selected.png', usage: '底部导航栏' },
            { id: 'profile', name: '个人中心图标', path: '/assets/icons/profile.png', usage: '底部导航栏' },
            { id: 'profile_selected', name: '个人中心选中图标', path: '/assets/icons/profile_selected.png', usage: '底部导航栏' },
            { id: 'notification', name: '通知图标', path: '/assets/icons/notification.png', usage: '首页顶部' },
            { id: 'task', name: '任务图标', path: '/assets/icons/task.png', usage: '首页待办任务' },
            { id: 'announcement', name: '公告图标', path: '/assets/icons/announcement.png', usage: '首页公告' },
            { id: 'settings', name: '设置图标', path: '/assets/icons/settings.png', usage: '个人中心' },
            { id: 'help', name: '帮助图标', path: '/assets/icons/help.png', usage: '个人中心' },
            { id: 'about', name: '关于图标', path: '/assets/icons/about.png', usage: '个人中心' },
            { id: 'feedback', name: '反馈图标', path: '/assets/icons/feedback.png', usage: '个人中心' },
            { id: 'search', name: '搜索图标', path: '/assets/icons/search.png', usage: '各页面搜索栏' },
            { id: 'arrow_right', name: '右箭头图标', path: '/assets/icons/arrow_right.png', usage: '列表项导航' },
            { id: 'arrow_down', name: '下箭头图标', path: '/assets/icons/arrow_down.png', usage: '可展开项' }
        ];
        
        renderIcons();
        document.getElementById('iconCount').textContent = iconsData.length;
    }, 500);
}

// 刷新图标列表
function refreshIcons() {
    const container = document.getElementById('iconsContainer');
    container.innerHTML = `
        <div class="col-12 text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载图标列表...</p>
        </div>
    `;
    
    loadIcons();
}

// 渲染图标列表
function renderIcons() {
    const container = document.getElementById('iconsContainer');
    container.innerHTML = '';
    
    if (iconsData.length === 0) {
        container.innerHTML = `
            <div class="col-12 text-center py-5">
                <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                <p class="mt-2 text-muted">暂无图标数据</p>
            </div>
        `;
        return;
    }
    
    iconsData.forEach(icon => {
        const iconElement = `
            <div class="col-md-3 col-sm-4 col-6 mb-4">
                <div class="icon-item">
                    <div class="icon-preview">
                        <img src="${icon.path}" alt="${icon.name}" style="max-width: 100%; max-height: 100%;">
                    </div>
                    <div class="icon-name">${icon.name}</div>
                    <div class="icon-usage text-muted small">${icon.usage}</div>
                    <button class="btn btn-sm btn-outline-primary mt-2 w-100" onclick="openReplaceModal('${icon.id}')">
                        <i class="bi bi-upload me-1"></i>替换
                    </button>
                </div>
            </div>
        `;
        container.innerHTML += iconElement;
    });
}

// 打开替换图标模态框
function openReplaceModal(iconId) {
    currentIcon = iconsData.find(i => i.id === iconId);
    if (!currentIcon) return;
    
    // 设置模态框内容
    document.getElementById('currentIconPreview').src = currentIcon.path;
    document.getElementById('currentIconName').textContent = currentIcon.name;
    document.getElementById('currentIconUsage').textContent = currentIcon.usage;
    
    // 清空文件选择
    document.getElementById('newIconFile').value = '';
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('replaceIconModal'));
    modal.show();
}

// 上传新图标
function uploadNewIcon() {
    const fileInput = document.getElementById('newIconFile');
    const file = fileInput.files[0];
    
    if (!file) {
        alert('请选择要上传的图标文件！');
        return;
    }
    
    if (file.type !== 'image/png') {
        alert('请选择PNG格式的图片文件！');
        return;
    }
    
    // 模拟上传过程
    const reader = new FileReader();
    reader.onload = function(event) {
        // 更新预览
        const preview = document.querySelector(`[alt="${currentIcon.name}"]`);
        if (preview) {
            preview.src = event.target.result;
        }
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('replaceIconModal'));
        modal.hide();
        
        // 显示成功消息
        alert(`图标 "${currentIcon.name}" 已成功替换！`);
    };
    reader.readAsDataURL(file);
}
</script>html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-size: .875rem;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
        }
        
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .sidebar .nav-link {
            font-weight: 500;
            color: #333;
        }
        
        .sidebar .nav-link.active {
            color: #0066cc;
        }
        
        .sidebar-heading {
            font-size: .75rem;
            text-transform: uppercase;
        }
        
        .navbar-brand {
            padding-top: .75rem;
            padding-bottom: .75rem;
            font-size: 1rem;
            background-color: rgba(0, 0, 0, .25);
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .navbar-brand:hover {
            background-color: rgba(0, 0, 0, .35);
        }
        
        .stat-card {
            border-radius: 10px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
        }
        
        /* 图标管理样式 */
        .icon-preview {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 5px;
            margin-bottom: 15px;
            transition: all 0.3s;
        }
        
        .icon-item:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-color: #0066cc;
        }
        
        .icon-name {
            font-size: 0.8rem;
            margin-top: 8px;
            color: #666;
        }
    </style>
</head>
<body>
    <header class="navbar navbar-dark sticky-top bg-primary flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="/admin">智慧养鹅管理系统</a>
        <button class="navbar-toggler d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="navbar-nav">
            <div class="nav-item text-nowrap">
                <a class="nav-link px-3" href="#">退出登录</a>
            </div>
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'home') ? 'active' : '' %>" href="/admin">
                                <i class="bi bi-speedometer2 me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'users') ? 'active' : '' %>" href="/admin/users">
                                <i class="bi bi-people me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'health') ? 'active' : '' %>" href="/admin/health">
                                <i class="bi bi-heart-pulse me-2"></i>
                                健康管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'production') ? 'active' : '' %>" href="/admin/production">
                                <i class="bi bi-egg me-2"></i>
                                生产管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'icons') ? 'active' : '' %>" href="/admin/icons">
                                <i class="bi bi-image me-2"></i>
                                图标管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'settings') ? 'active' : '' %>" href="/admin/settings">
                                <i class="bi bi-gear me-2"></i>
                                系统设置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><%= title %></h1>
                </div>
                
                <%- typeof body !== 'undefined' ? body : '' %>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</body>
</html>