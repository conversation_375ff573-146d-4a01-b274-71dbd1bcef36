<!-- 生产管理页面 -->
<div class="enterprise-page-container">
    <!-- 页面头部 -->
    <div class="page-header-modern">
        <div class="page-header-content">
            <div class="page-title-section">
                <h1 class="page-title-modern">
                    <i class="bi bi-clipboard-data"></i>
                    生产管理
                </h1>
                <p class="page-subtitle-modern">监控生产数据、分析生产效率和管理生产记录</p>
                <div class="page-breadcrumb">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/dashboard">首页</a></li>
                            <li class="breadcrumb-item active">生产管理</li>
                        </ol>
                    </nav>
                </div>
            </div>
            <div class="page-actions-modern">
                <button type="button" class="btn btn-outline-primary" onclick="exportProduction()">
                    <i class="bi bi-download"></i>
                    导出报表
                </button>
                <button type="button" class="btn btn-primary" onclick="showCreateRecordModal()">
                    <i class="bi bi-plus-lg"></i>
                    新增记录
                </button>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards-container">
        <div class="row g-3">
            <div class="col-xl-3 col-md-6">
                <div class="stats-card primary">
                    <div class="stats-card-body">
                        <div class="stats-icon">
                            <i class="bi bi-egg"></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" id="todayEggs">0</div>
                            <div class="stats-label">今日产蛋</div>
                            <div class="stats-change positive">
                                <i class="bi bi-arrow-up"></i> +5.2% 昨日
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card success">
                    <div class="stats-card-body">
                        <div class="stats-icon">
                            <i class="bi bi-graph-up"></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" id="productionRate">0%</div>
                            <div class="stats-label">产蛋率</div>
                            <div class="stats-change positive">
                                <i class="bi bi-arrow-up"></i> +2.1% 本周
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card warning">
                    <div class="stats-card-body">
                        <div class="stats-icon">
                            <i class="bi bi-bucket"></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" id="feedConsumption">0</div>
                            <div class="stats-label">饲料消耗(kg)</div>
                            <div class="stats-change negative">
                                <i class="bi bi-arrow-down"></i> -1.8% 昨日
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card info">
                    <div class="stats-card-body">
                        <div class="stats-icon">
                            <i class="bi bi-arrow-repeat"></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" id="conversionRatio">0</div>
                            <div class="stats-label">料蛋比</div>
                            <div class="stats-change positive">
                                <i class="bi bi-arrow-up"></i> 优化中
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
        <div class="row g-3">
            <div class="col-xl-8">
                <div class="chart-card">
                    <div class="chart-header">
                        <h5>生产趋势</h5>
                        <div class="chart-controls">
                            <select class="form-select form-select-sm" id="trendPeriod">
                                <option value="7">最近7天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="90">最近90天</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-body">
                        <canvas id="productionTrendChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-xl-4">
                <div class="chart-card">
                    <div class="chart-header">
                        <h5>鹅群产蛋分布</h5>
                    </div>
                    <div class="chart-body">
                        <canvas id="flockDistributionChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-toolbar-modern">
        <div class="filter-section">
            <div class="search-box-modern">
                <i class="bi bi-search"></i>
                <input type="text" id="searchInput" placeholder="搜索生产记录..." class="form-control">
                <button type="button" class="search-clear" onclick="clearSearch()" style="display: none;">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="filter-group-modern">
                <div class="filter-item">
                    <label>鹅群</label>
                    <select id="flockFilter" class="form-select">
                        <option value="">全部鹅群</option>
                        <!-- 动态加载鹅群选项 -->
                    </select>
                </div>
                <div class="filter-item">
                    <label>日期范围</label>
                    <select id="dateRangeFilter" class="form-select">
                        <option value="">全部时间</option>
                        <option value="today">今天</option>
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                        <option value="quarter">本季度</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>健康状态</label>
                    <select id="healthFilter" class="form-select">
                        <option value="">全部状态</option>
                        <option value="normal">正常</option>
                        <option value="warning">警告</option>
                        <option value="critical">严重</option>
                    </select>
                </div>
                <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                    <i class="bi bi-arrow-clockwise"></i>
                    重置
                </button>
            </div>
        </div>

        <!-- 批量操作工具栏 -->
        <div class="batch-actions-modern" id="batchActions" style="display: none;">
            <div class="batch-info">
                <span class="selected-count">已选择 <span id="selectedCount">0</span> 项</span>
                <button type="button" class="btn-link" onclick="clearSelection()">清除选择</button>
            </div>
            <div class="batch-buttons">
                <button type="button" class="btn btn-outline-primary" onclick="batchExport()">
                    <i class="bi bi-download"></i>
                    导出选中
                </button>
                <button type="button" class="btn btn-outline-warning" onclick="batchAnalyze()">
                    <i class="bi bi-graph-up"></i>
                    批量分析
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="batchDeleteRecords()">
                    <i class="bi bi-trash"></i>
                    批量删除
                </button>
            </div>
        </div>
    </div>

    <!-- 数据表格 -->
    <div class="data-table-modern">
        <div class="table-header">
            <div class="table-title">
                <h5>生产记录</h5>
                <span class="table-count">共 <span id="totalCount">0</span> 条记录</span>
            </div>
            <div class="table-tools">
                <div class="view-toggle">
                    <button type="button" class="btn btn-sm btn-outline-secondary active" data-view="table">
                        <i class="bi bi-table"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" data-view="card">
                        <i class="bi bi-grid-3x3-gap"></i>
                    </button>
                </div>
                <div class="table-settings">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="showColumnSettings()">
                        <i class="bi bi-gear"></i>
                        列设置
                    </button>
                </div>
            </div>
        </div>

        <div class="table-container-modern">
            <div class="table-responsive">
                <table class="table table-hover enterprise-table" id="productionTable">
                    <thead>
                        <tr>
                            <th width="50">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th class="sortable" data-sort="recordedDate">
                                记录日期
                                <i class="bi bi-arrow-down-up sort-icon"></i>
                            </th>
                            <th>鹅群</th>
                            <th class="sortable" data-sort="eggCount">
                                产蛋数量
                                <i class="bi bi-arrow-down-up sort-icon"></i>
                            </th>
                            <th>蛋重(g)</th>
                            <th class="sortable" data-sort="feedConsumption">
                                饲料消耗(kg)
                                <i class="bi bi-arrow-down-up sort-icon"></i>
                            </th>
                            <th>环境条件</th>
                            <th>死亡数</th>
                            <th>健康状态</th>
                            <th>产蛋率</th>
                            <th>料蛋比</th>
                            <th>备注</th>
                            <th width="140">操作</th>
                        </tr>
                    </thead>
                    <tbody id="productionTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 加载状态 -->
            <div class="loading-state-modern" id="loadingState">
                <div class="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
                <p>正在加载生产数据...</p>
            </div>

            <!-- 空状态 -->
            <div class="empty-state-modern" id="emptyState" style="display: none;">
                <div class="empty-icon">
                    <i class="bi bi-clipboard-data"></i>
                </div>
                <h3>暂无生产记录</h3>
                <p>还没有任何生产记录，点击上方按钮创建第一条记录</p>
                <button type="button" class="btn btn-primary" onclick="showCreateRecordModal()">
                    <i class="bi bi-plus-lg"></i>
                    创建记录
                </button>
            </div>

            <!-- 错误状态 -->
            <div class="error-state-modern" id="errorState" style="display: none;">
                <div class="error-icon">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <h3>加载失败</h3>
                <p id="errorMessage">数据加载失败，请稍后重试</p>
                <button type="button" class="btn btn-outline-primary" onclick="loadProductionRecords()">
                    <i class="bi bi-arrow-clockwise"></i>
                    重新加载
                </button>
            </div>
        </div>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container-modern">
        <div class="pagination-info">
            <span id="paginationInfo">显示第 1-10 条，共 0 条记录</span>
        </div>
        <nav aria-label="生产记录分页">
            <ul class="pagination pagination-modern" id="pagination">
                <!-- 分页按钮将通过JavaScript动态生成 -->
            </ul>
        </nav>
        <div class="page-size-selector">
            <label>每页显示</label>
            <select id="pageSizeSelect" class="form-select">
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
            <label>条</label>
        </div>
    </div>
</div>

<script>
// 搜索功能
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('tbody tr');
    
    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// 删除记录
function deleteRecord(recordId) {
    if (confirm('确定要删除这条生产记录吗？此操作不可恢复。')) {
        fetch(\`/api/production/\${recordId}\`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showMessage('生产记录删除成功', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showMessage(result.message || '删除失败', 'danger');
            }
        })
        .catch(error => {
            console.error('删除记录错误:', error);
            showMessage('删除记录失败', 'danger');
        });
    }
}
</script>

<style>
/* 企业级生产管理页面样式 */
.enterprise-page-container {
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header-modern {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 24px;
    color: white;
    position: relative;
    overflow: hidden;
}

.page-header-modern::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.page-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
    z-index: 2;
}

.page-title-modern {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 16px;
}

.page-subtitle-modern {
    margin: 8px 0 16px 0;
    opacity: 0.9;
    font-size: 1rem;
}

.page-breadcrumb {
    margin-top: 8px;
}

.page-breadcrumb .breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.page-breadcrumb .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.page-breadcrumb .breadcrumb-item.active {
    color: white;
}

.page-actions-modern {
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.page-actions-modern .btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 20px;
}

.page-actions-modern .btn-outline-primary {
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.page-actions-modern .btn-outline-primary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.page-actions-modern .btn-primary {
    background: white;
    color: #f59e0b;
    border: none;
}

.page-actions-modern .btn-primary:hover {
    background: #f8f9ff;
    color: #d97706;
}

.stats-cards-container {
    margin-bottom: 24px;
}

.stats-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
    transition: transform 0.2s, box-shadow 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stats-card.primary {
    border-left-color: #3b82f6;
}

.stats-card.success {
    border-left-color: #10b981;
}

.stats-card.warning {
    border-left-color: #f59e0b;
}

.stats-card.info {
    border-left-color: #06b6d4;
}

.stats-card-body {
    display: flex;
    align-items: center;
    gap: 16px;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stats-card.primary .stats-icon {
    background: #3b82f6;
}

.stats-card.success .stats-icon {
    background: #10b981;
}

.stats-card.warning .stats-icon {
    background: #f59e0b;
}

.stats-card.info .stats-icon {
    background: #06b6d4;
}

.stats-content {
    flex: 1;
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 4px;
}

.stats-label {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 8px;
}

.stats-change {
    font-size: 0.75rem;
    font-weight: 500;
}

.stats-change.positive {
    color: #10b981;
}

.stats-change.negative {
    color: #ef4444;
}

.stats-change.neutral {
    color: #6b7280;
}

.charts-container {
    margin-bottom: 24px;
}

.chart-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.chart-header {
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h5 {
    margin: 0;
    font-weight: 600;
    color: #1f2937;
}

.chart-body {
    padding: 20px;
}

.chart-controls select {
    min-width: 120px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .enterprise-page-container {
        padding: 16px;
    }

    .page-header-modern {
        padding: 24px;
    }

    .page-header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .stats-card-body {
        flex-direction: column;
        text-align: center;
    }

    .stats-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .stats-number {
        font-size: 1.5rem;
    }

    .charts-container .col-xl-8,
    .charts-container .col-xl-4 {
        margin-bottom: 16px;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="/js/production-enterprise.js"></script>
