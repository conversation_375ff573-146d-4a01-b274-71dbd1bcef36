<!-- 健康管理页面 -->
<div class="enterprise-page-container">
    <!-- 页面头部 -->
    <div class="page-header-modern">
        <div class="page-header-content">
            <div class="page-title-section">
                <h1 class="page-title-modern">
                    <i class="bi bi-heart-pulse"></i>
                    健康管理
                </h1>
                <p class="page-subtitle-modern">监控鹅群健康状态、管理疫苗接种和疾病预防</p>
                <div class="page-breadcrumb">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/dashboard">首页</a></li>
                            <li class="breadcrumb-item active">健康管理</li>
                        </ol>
                    </nav>
                </div>
            </div>
            <div class="page-actions-modern">
                <button type="button" class="btn btn-outline-primary" onclick="exportHealthData()">
                    <i class="bi bi-download"></i>
                    导出报告
                </button>
                <button type="button" class="btn btn-primary" onclick="showCreateRecordModal()">
                    <i class="bi bi-plus-lg"></i>
                    新增记录
                </button>
            </div>
        </div>
    </div>

    <!-- 健康状态概览 -->
    <div class="health-overview-container">
        <div class="row g-3">
            <div class="col-xl-3 col-md-6">
                <div class="health-card excellent">
                    <div class="health-card-body">
                        <div class="health-icon">
                            <i class="bi bi-shield-check"></i>
                        </div>
                        <div class="health-content">
                            <div class="health-number" id="healthyFlocks">0</div>
                            <div class="health-label">健康鹅群</div>
                            <div class="health-percentage">95.2%</div>
                        </div>
                    </div>
                </div>
            </div>
    <div class="card-body">
        ${records.length > 0 ? `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>记录日期</th>
                        <th>健康状态</th>
                        <th>症状</th>
                        <th>治疗方案</th>
                        <th>备注</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${records.map(record => {
                        const status = record.健康状态 || '健康';
                        let statusBadge = '';
                        if (status === '健康' || status === 'healthy') {
                            statusBadge = '<span class="badge bg-success">健康</span>';
                        } else if (status === '警告' || status === 'warning') {
                            statusBadge = '<span class="badge bg-warning">警告</span>';
                        } else {
                            statusBadge = '<span class="badge bg-danger">严重</span>';
                        }
                        
                        return `
                        <tr>
                            <td>
                                <strong>${moment(record.记录日期 || record.createdAt).format('YYYY-MM-DD')}</strong>
                            </td>
                            <td>${statusBadge}</td>
                            <td>
                                <span class="text-muted">
                                    ${record.症状 || '-'}
                                </span>
                            </td>
                            <td>
                                <span class="text-muted">
                                    ${record.治疗方案 || '-'}
                                </span>
                            </td>
                            <td>
                                <span class="text-muted">
                                    ${record.备注 ? (record.备注.length > 20 ? record.备注.substring(0, 20) + '...' : record.备注) : '-'}
                                </span>
                            </td>
                            <td>
                                <small class="text-muted">
                                    ${moment(record.createdAt).format('MM-DD HH:mm')}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-info" title="查看详情" 
                                            onclick="viewRecord(${record.id})">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" title="编辑">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" title="删除" 
                                            onclick="deleteRecord(${record.id})">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
        </div>
    </div>
</div>

<style>
/* 企业级健康管理页面样式 */
.enterprise-page-container {
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header-modern {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 24px;
    color: white;
    position: relative;
    overflow: hidden;
}

.page-header-modern::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.page-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
    z-index: 2;
}

.page-title-modern {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 16px;
}

.page-subtitle-modern {
    margin: 8px 0 16px 0;
    opacity: 0.9;
    font-size: 1rem;
}

.page-breadcrumb {
    margin-top: 8px;
}

.page-breadcrumb .breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.page-breadcrumb .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.page-breadcrumb .breadcrumb-item.active {
    color: white;
}

.page-actions-modern {
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.page-actions-modern .btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 20px;
}

.page-actions-modern .btn-outline-primary {
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.page-actions-modern .btn-outline-primary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.page-actions-modern .btn-primary {
    background: white;
    color: #10b981;
    border: none;
}

.page-actions-modern .btn-primary:hover {
    background: #f8f9ff;
    color: #059669;
}

.health-overview-container {
    margin-bottom: 24px;
}

.health-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
    transition: transform 0.2s, box-shadow 0.2s;
}

.health-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.health-card.excellent {
    border-left-color: #10b981;
}

.health-card.warning {
    border-left-color: #f59e0b;
}

.health-card.critical {
    border-left-color: #ef4444;
}

.health-card.info {
    border-left-color: #06b6d4;
}

.health-card-body {
    display: flex;
    align-items: center;
    gap: 16px;
}

.health-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.health-card.excellent .health-icon {
    background: #10b981;
}

.health-card.warning .health-icon {
    background: #f59e0b;
}

.health-card.critical .health-icon {
    background: #ef4444;
}

.health-card.info .health-icon {
    background: #06b6d4;
}

.health-content {
    flex: 1;
}

.health-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 4px;
}

.health-label {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 8px;
}

.health-percentage {
    font-size: 0.75rem;
    font-weight: 500;
    color: #10b981;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .enterprise-page-container {
        padding: 16px;
    }

    .page-header-modern {
        padding: 24px;
    }

    .page-header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .health-card-body {
        flex-direction: column;
        text-align: center;
    }

    .health-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .health-number {
        font-size: 1.5rem;
    }
}
</style>

<script src="/js/health-enterprise.js"></script>
