<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>
    <%= title %>
  </title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
  <style>
    .shop-header {
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      color: white;
      padding: 2rem 0;
      margin-bottom: 2rem;
    }

    .product-card {
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      transition: all 0.2s;
    }

    .product-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .product-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
      border-radius: 8px;
    }

    .status-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
    }

    .status-active {
      background-color: #10b981;
    }

    .status-inactive {
      background-color: #ef4444;
    }

    .status-out_of_stock {
      background-color: #f59e0b;
    }

    .filter-section {
      background: #f8fafc;
      padding: 1.5rem;
      border-radius: 12px;
      margin-bottom: 2rem;
    }
  </style>
</head>

<body>
  <div class="container-fluid">
    <!-- 页头 -->
    <div class="shop-header">
      <div class="container">
        <div class="row align-items-center">
          <div class="col">
            <h1 class="mb-0">
              <i class="bi bi-shop me-3"></i>
              商品管理
            </h1>
            <p class="mb-0 mt-2 opacity-75">管理商城商品、分类和库存</p>
          </div>
          <div class="col-auto">
            <a href="/admin/shop/create" class="btn btn-light btn-lg">
              <i class="bi bi-plus-circle me-2"></i>
              添加商品
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="container">
      <!-- 错误信息 -->
      <% if (error) { %>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
          <i class="bi bi-exclamation-triangle me-2"></i>
          <%= error %>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <% } %>

          <!-- 筛选区域 -->
          <div class="filter-section">
            <form method="GET" class="row g-3">
              <div class="col-md-4">
                <label class="form-label">搜索商品</label>
                <input type="text" class="form-control" name="search" value="<%= filters.search %>"
                  placeholder="输入商品名称或描述">
              </div>
              <div class="col-md-3">
                <label class="form-label">商品分类</label>
                <select class="form-select" name="category">
                  <option value="">全部分类</option>
                  <% categories.forEach(category=> { %>
                    <option value="<%= category.name %>" <%=filters.category===category.name ? 'selected' : '' %>>
                      <%= category.name %> (<%= category.count %>)
                    </option>
                    <% }); %>
                </select>
              </div>
              <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                  <i class="bi bi-search me-2"></i>筛选
                </button>
                <a href="/admin/shop" class="btn btn-outline-secondary">重置</a>
              </div>
            </form>
          </div>

          <!-- 统计信息 -->
          <div class="row mb-4">
            <div class="col-md-3">
              <div class="card text-center">
                <div class="card-body">
                  <h3 class="text-primary">
                    <%= total %>
                  </h3>
                  <p class="mb-0">商品总数</p>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card text-center">
                <div class="card-body">
                  <h3 class="text-success">
                    <%= categories.length %>
                  </h3>
                  <p class="mb-0">商品分类</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 商品列表 -->
          <div class="row">
            <% if (products && products.length> 0) { %>
              <% products.forEach(product=> { %>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                  <div class="product-card card h-100">
                    <div class="card-body">
                      <div class="position-relative mb-3">
                        <img src="<%= product.image || '/images/products/default.jpg' %>" class="product-image"
                          alt="<%= product.name %>">
                        <span class="badge status-badge status-<%= product.status %> position-absolute top-0 end-0 m-2">
                          <%= product.status==='active' ? '在售' : product.status==='inactive' ? '下架' : '缺货' %>
                        </span>
                      </div>

                      <h6 class="card-title mb-2">
                        <%= product.name %>
                      </h6>
                      <p class="card-text text-muted small mb-2">
                        <%= product.description ? product.description.substring(0, 50) + '...' : '暂无描述' %>
                      </p>

                      <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="text-primary fw-bold">¥<%= product.price %></span>
                        <small class="text-muted">库存: <%= product.stock %></small>
                      </div>

                      <div class="d-flex gap-2">
                        <a href="/admin/shop/edit/<%= product.id %>" class="btn btn-outline-primary btn-sm flex-fill">
                          <i class="bi bi-pencil me-1"></i>编辑
                        </a>
                        <button onclick="deleteProduct(<%= product.id %>)" class="btn btn-outline-danger btn-sm">
                          <i class="bi bi-trash"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <% }); %>
                  <% } else { %>
                    <div class="col-12">
                      <div class="text-center py-5">
                        <i class="bi bi-box-seam text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">暂无商品</h4>
                        <p class="text-muted">点击上方"添加商品"按钮开始添加商品</p>
                        <a href="/admin/shop/create" class="btn btn-primary">
                          <i class="bi bi-plus-circle me-2"></i>添加第一个商品
                        </a>
                      </div>
                    </div>
                    <% } %>
          </div>

          <!-- 分页 -->
          <% if (pagination && pagination.pages> 1) { %>
            <nav aria-label="商品分页">
              <ul class="pagination justify-content-center">
                <% for (let i=1; i <=pagination.pages; i++) { %>
                  <li class="page-item <%= pagination.page === i ? 'active' : '' %>">
                    <a class="page-link"
                      href="?page=<%= i %>&category=<%= filters.category %>&search=<%= filters.search %>">
                      <%= i %>
                    </a>
                  </li>
                  <% } %>
              </ul>
            </nav>
            <% } %>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    function deleteProduct(id) {
      if (confirm('确定要删除这个商品吗？此操作不可撤销。')) {
        fetch(`/admin/api/shop/products/${id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          }
        })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              location.reload();
            } else {
              alert('删除失败：' + data.message);
            }
          })
          .catch(error => {
            console.error('Error:', error);
            alert('删除失败，请稍后重试');
          });
      }
    }
  </script>
</body>

</html>