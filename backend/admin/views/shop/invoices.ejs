<!-- 发票管理页面 -->
<div class="enterprise-page-container">
  <!-- 页面头部 -->
  <div class="page-header-modern">
    <div class="page-header-content">
      <div class="page-title-section">
        <h1 class="page-title-modern">
          <i class="bi bi-receipt"></i>
          发票管理
        </h1>
        <p class="page-subtitle-modern">管理订单发票、开具状态和发票类型</p>
        <div class="page-breadcrumb">
          <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
              <li class="breadcrumb-item"><a href="/dashboard">首页</a></li>
              <li class="breadcrumb-item"><a href="/shop">商城管理</a></li>
              <li class="breadcrumb-item active">发票管理</li>
            </ol>
          </nav>
        </div>
      </div>
      <div class="page-actions-modern">
        <button type="button" class="btn btn-outline-primary" onclick="exportInvoices()">
          <i class="bi bi-download"></i>
          导出发票
        </button>
        <button type="button" class="btn btn-primary" onclick="batchProcessInvoices()">
          <i class="bi bi-gear"></i>
          批量处理
        </button>
      </div>
    </div>
  </div>

  <!-- 筛选器 -->
  <div class="filters-container">
    <form method="GET" class="filters-form">
      <div class="row g-3">
        <div class="col-md-3">
          <label class="form-label">发票状态</label>
          <select name="status" class="form-select">
            <option value="">全部状态</option>
            <option value="pending" <%=filters.status==='pending' ? 'selected' : '' %>>待开具</option>
            <option value="processing" <%=filters.status==='processing' ? 'selected' : '' %>>开具中</option>
            <option value="issued" <%=filters.status==='issued' ? 'selected' : '' %>>已开具</option>
            <option value="cancelled" <%=filters.status==='cancelled' ? 'selected' : '' %>>已取消</option>
          </select>
        </div>
        <div class="col-md-3">
          <label class="form-label">发票类型</label>
          <select name="type" class="form-select">
            <option value="">全部类型</option>
            <option value="personal" <%=filters.type==='personal' ? 'selected' : '' %>>个人发票</option>
            <option value="company" <%=filters.type==='company' ? 'selected' : '' %>>企业发票</option>
          </select>
        </div>
        <div class="col-md-3">
          <label class="form-label">搜索</label>
          <input type="text" name="search" class="form-control" placeholder="订单号、发票号"
            value="<%= filters.search || '' %>">
        </div>
        <div class="col-md-3">
          <label class="form-label">&nbsp;</label>
          <div class="d-flex gap-2">
            <button type="submit" class="btn btn-primary">筛选</button>
            <a href="/shop/invoices" class="btn btn-outline-secondary">重置</a>
          </div>
        </div>
      </div>
    </form>
  </div>

  <!-- 统计卡片 -->
  <div class="stats-cards-container">
    <div class="row g-4">
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-icon bg-warning">
            <i class="bi bi-clock"></i>
          </div>
          <div class="stats-content">
            <h3 class="stats-number">
              <%= invoices.filter(inv=> inv.status === 'pending').length %>
            </h3>
            <p class="stats-label">待开具</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-icon bg-primary">
            <i class="bi bi-gear"></i>
          </div>
          <div class="stats-content">
            <h3 class="stats-number">
              <%= invoices.filter(inv=> inv.status === 'processing').length %>
            </h3>
            <p class="stats-label">开具中</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-icon bg-success">
            <i class="bi bi-check-circle"></i>
          </div>
          <div class="stats-content">
            <h3 class="stats-number">
              <%= invoices.filter(inv=> inv.status === 'issued').length %>
            </h3>
            <p class="stats-label">已开具</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="stats-icon bg-secondary">
            <i class="bi bi-x-circle"></i>
          </div>
          <div class="stats-content">
            <h3 class="stats-number">
              <%= invoices.filter(inv=> inv.status === 'cancelled').length %>
            </h3>
            <p class="stats-label">已取消</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 发票列表 -->
  <div class="table-container">
    <div class="table-header">
      <h5 class="table-title">发票列表</h5>
      <div class="table-actions">
        <small class="text-muted">共 <%= total %> 条记录</small>
      </div>
    </div>

    <% if (error) { %>
      <div class="alert alert-danger" role="alert">
        <i class="bi bi-exclamation-triangle"></i>
        <%= error %>
      </div>
      <% } %>

        <% if (invoices && invoices.length> 0) { %>
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th><input type="checkbox" class="form-check-input" id="selectAll"></th>
                  <th>发票号</th>
                  <th>订单号</th>
                  <th>发票类型</th>
                  <th>发票抬头</th>
                  <th>金额</th>
                  <th>状态</th>
                  <th>申请时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <% invoices.forEach(function(invoice) { %>
                  <tr>
                    <td>
                      <input type="checkbox" class="form-check-input invoice-checkbox" value="<%= invoice.id %>">
                    </td>
                    <td>
                      <strong>
                        <%= invoice.invoiceNumber || '-' %>
                      </strong>
                    </td>
                    <td>
                      <a href="/shop/orders/<%= invoice.orderId %>" class="text-decoration-none">
                        <%= invoice.orderNumber %>
                      </a>
                    </td>
                    <td>
                      <span class="badge bg-<%= invoice.type === 'personal' ? 'info' : 'primary' %>">
                        <%= invoice.type==='personal' ? '个人' : '企业' %>
                      </span>
                    </td>
                    <td>
                      <div class="text-truncate" style="max-width: 200px;" title="<%= invoice.title %>">
                        <%= invoice.title %>
                      </div>
                      <% if (invoice.taxNumber) { %>
                        <small class="text-muted d-block">税号: <%= invoice.taxNumber %></small>
                        <% } %>
                    </td>
                    <td>
                      <strong class="text-success">¥<%= invoice.amount %></strong>
                    </td>
                    <td>
                      <% let statusClass='secondary' ; let statusText='未知' ; switch(invoice.status) { case 'pending' :
                        statusClass='warning' ; statusText='待开具' ; break; case 'processing' : statusClass='primary' ;
                        statusText='开具中' ; break; case 'issued' : statusClass='success' ; statusText='已开具' ; break;
                        case 'cancelled' : statusClass='secondary' ; statusText='已取消' ; break; } %>
                        <span class="badge bg-<%= statusClass %>">
                          <%= statusText %>
                        </span>
                    </td>
                    <td>
                      <small class="text-muted">
                        <%= new Date(invoice.createTime).toLocaleDateString('zh-CN') %>
                          <br>
                          <%= new Date(invoice.createTime).toLocaleTimeString('zh-CN') %>
                      </small>
                    </td>
                    <td>
                      <div class="btn-group btn-group-sm">
                        <% if (invoice.status==='pending' ) { %>
                          <button class="btn btn-outline-primary" onclick="processInvoice('<%= invoice.id %>')">
                            <i class="bi bi-gear"></i> 开具
                          </button>
                          <% } else if (invoice.status==='issued' ) { %>
                            <button class="btn btn-outline-success" onclick="downloadInvoice('<%= invoice.id %>')">
                              <i class="bi bi-download"></i> 下载
                            </button>
                            <% } %>
                              <button class="btn btn-outline-info" onclick="viewInvoiceDetail('<%= invoice.id %>')">
                                <i class="bi bi-eye"></i> 详情
                              </button>
                              <% if (invoice.status==='pending' ) { %>
                                <button class="btn btn-outline-danger" onclick="cancelInvoice('<%= invoice.id %>')">
                                  <i class="bi bi-x"></i> 取消
                                </button>
                                <% } %>
                      </div>
                    </td>
                  </tr>
                  <% }); %>
              </tbody>
            </table>
          </div>

          <!-- 分页 -->
          <% if (pagination.pages> 1) { %>
            <%- include('../../partials/pagination', { pagination: pagination, baseUrl: '/shop/invoices' , params:
              filters }) %>
              <% } %>
                <% } else { %>
                  <div class="empty-state">
                    <i class="bi bi-receipt"></i>
                    <h5>暂无发票记录</h5>
                    <p class="text-muted">还没有发票申请记录</p>
                  </div>
                  <% } %>
  </div>
</div>

<!-- 发票详情模态框 -->
<div class="modal fade" id="invoiceDetailModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">发票详情</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="invoiceDetailContent">
        <!-- 发票详情内容将动态加载 -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        <button type="button" class="btn btn-primary" onclick="printInvoice()">打印发票</button>
      </div>
    </div>
  </div>
</div>

<script>
  // 发票管理相关JavaScript
  function processInvoice(invoiceId) {
    if (confirm('确定要开具这张发票吗？')) {
      // 发送请求开具发票
      fetch(`/api/shop/invoices/${invoiceId}/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      }).then(response => response.json())
        .then(data => {
          if (data.success) {
            alert('发票开具成功！');
            location.reload();
          } else {
            alert('发票开具失败：' + data.message);
          }
        }).catch(error => {
          alert('操作失败：' + error.message);
        });
    }
  }

  function cancelInvoice(invoiceId) {
    if (confirm('确定要取消这张发票申请吗？')) {
      // 发送请求取消发票
      fetch(`/api/shop/invoices/${invoiceId}/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      }).then(response => response.json())
        .then(data => {
          if (data.success) {
            alert('发票已取消！');
            location.reload();
          } else {
            alert('取消失败：' + data.message);
          }
        }).catch(error => {
          alert('操作失败：' + error.message);
        });
    }
  }

  function downloadInvoice(invoiceId) {
    // 下载发票PDF
    window.open(`/api/shop/invoices/${invoiceId}/download`, '_blank');
  }

  function viewInvoiceDetail(invoiceId) {
    // 加载发票详情
    fetch(`/api/shop/invoices/${invoiceId}`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          document.getElementById('invoiceDetailContent').innerHTML = generateInvoiceDetailHTML(data.data);
          new bootstrap.Modal(document.getElementById('invoiceDetailModal')).show();
        } else {
          alert('加载发票详情失败：' + data.message);
        }
      }).catch(error => {
        alert('加载失败：' + error.message);
      });
  }

  function generateInvoiceDetailHTML(invoice) {
    return `
        <div class="invoice-detail">
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td>发票号:</td><td>${invoice.invoiceNumber || '未生成'}</td></tr>
                        <tr><td>订单号:</td><td>${invoice.orderNumber}</td></tr>
                        <tr><td>发票类型:</td><td>${invoice.type === 'personal' ? '个人发票' : '企业发票'}</td></tr>
                        <tr><td>发票金额:</td><td>¥${invoice.amount}</td></tr>
                        <tr><td>申请时间:</td><td>${new Date(invoice.createTime).toLocaleString('zh-CN')}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>发票信息</h6>
                    <table class="table table-sm">
                        <tr><td>发票抬头:</td><td>${invoice.title}</td></tr>
                        ${invoice.taxNumber ? `<tr><td>税号:</td><td>${invoice.taxNumber}</td></tr>` : ''}
                        ${invoice.address ? `<tr><td>地址:</td><td>${invoice.address}</td></tr>` : ''}
                        ${invoice.phone ? `<tr><td>电话:</td><td>${invoice.phone}</td></tr>` : ''}
                        ${invoice.email ? `<tr><td>邮箱:</td><td>${invoice.email}</td></tr>` : ''}
                    </table>
                </div>
            </div>
        </div>
    `;
  }

  function exportInvoices() {
    // 导出发票列表
    window.open('/api/shop/invoices/export?' + new URLSearchParams(window.location.search), '_blank');
  }

  function batchProcessInvoices() {
    const selected = Array.from(document.querySelectorAll('.invoice-checkbox:checked')).map(cb => cb.value);
    if (selected.length === 0) {
      alert('请先选择要处理的发票');
      return;
    }

    if (confirm(`确定要批量处理选中的 ${selected.length} 张发票吗？`)) {
      // 批量处理逻辑
      fetch('/api/shop/invoices/batch-process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ invoiceIds: selected })
      }).then(response => response.json())
        .then(data => {
          if (data.success) {
            alert('批量处理成功！');
            location.reload();
          } else {
            alert('批量处理失败：' + data.message);
          }
        }).catch(error => {
          alert('操作失败：' + error.message);
        });
    }
  }

  // 全选功能
  document.getElementById('selectAll').addEventListener('change', function () {
    const checkboxes = document.querySelectorAll('.invoice-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
  });
</script>