<%- include('../layouts/main', { body: ` <!-- 设置导航 -->
  <div class="row">
    <div class="col-md-3">
      <div class="list-group">
        <a href="#basic" class="list-group-item list-group-item-action active" data-bs-toggle="tab">
          <i class="bi bi-gear me-2"></i>基本设置
        </a>
        <a href="#users" class="list-group-item list-group-item-action" data-bs-toggle="tab">
          <i class="bi bi-people me-2"></i>用户权限
        </a>
        <a href="#notifications" class="list-group-item list-group-item-action" data-bs-toggle="tab">
          <i class="bi bi-bell me-2"></i>通知设置
        </a>
        <a href="#backup" class="list-group-item list-group-item-action" data-bs-toggle="tab">
          <i class="bi bi-archive me-2"></i>数据备份
        </a>
        <a href="#logs" class="list-group-item list-group-item-action" data-bs-toggle="tab">
          <i class="bi bi-file-text me-2"></i>系统日志
        </a>
      </div>
    </div>
    <div class="col-md-9">
      <div class="tab-content">
        <!-- 基本设置 -->
        <div class="tab-pane fade show active" id="basic">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">基本设置</h5>
            </div>
            <div class="card-body">
              <form>
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="systemName" class="form-label">系统名称</label>
                      <input type="text" class="form-control" id="systemName" value="智慧养鹅管理系统">
                    </div>
                    <div class="mb-3">
                      <label for="adminEmail" class="form-label">管理员邮箱</label>
                      <input type="email" class="form-control" id="adminEmail" value="<EMAIL>">
                    </div>
                    <div class="mb-3">
                      <label for="timezone" class="form-label">时区</label>
                      <select class="form-select" id="timezone">
                        <option value="Asia/Shanghai" selected>Asia/Shanghai</option>
                        <option value="UTC">UTC</option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="language" class="form-label">系统语言</label>
                      <select class="form-select" id="language">
                        <option value="zh-CN" selected>简体中文</option>
                        <option value="en-US">English</option>
                      </select>
                    </div>
                    <div class="mb-3">
                      <label for="theme" class="form-label">主题</label>
                      <select class="form-select" id="theme">
                        <option value="light" selected>浅色主题</option>
                        <option value="dark">深色主题</option>
                      </select>
                    </div>
                    <div class="mb-3">
                      <label for="pageSize" class="form-label">每页显示条数</label>
                      <select class="form-select" id="pageSize">
                        <option value="10">10</option>
                        <option value="20" selected>20</option>
                        <option value="50">50</option>
                      </select>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-12">
                    <button type="button" class="btn btn-primary" onclick="saveSettings()">保存设置</button>
                    <button type="button" class="btn btn-secondary">重置</button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- 其他标签页内容省略，保持简洁 -->
        <div class="tab-pane fade" id="users">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">用户权限设置</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">默认用户权限</label>
                    <select class="form-select" id="defaultUserRole">
                      <option value="basic">基础用户</option>
                      <option value="premium">高级用户</option>
                      <option value="enterprise">企业用户</option>
                    </select>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">权限验证模式</label>
                    <select class="form-select" id="permissionMode">
                      <option value="strict">严格模式</option>
                      <option value="normal">普通模式</option>
                      <option value="loose">宽松模式</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="form-check form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="autoApproval" checked>
                <label class="form-check-label" for="autoApproval">
                  新用户自动审批
                </label>
              </div>
              <button type="button" class="btn btn-primary" onclick="saveUserSettings()">保存用户设置</button>
            </div>
          </div>
        </div>

        <div class="tab-pane fade" id="notifications">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">通知设置</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">邮件通知</label>
                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                      <label class="form-check-label" for="emailNotifications">
                        启用邮件通知
                      </label>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">短信通知</label>
                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="smsNotifications">
                      <label class="form-check-label" for="smsNotifications">
                        启用短信通知
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div class="mb-3">
                <label class="form-label">通知邮箱</label>
                <input type="email" class="form-control" id="notificationEmail" value="<EMAIL>"
                  placeholder="接收通知的邮箱地址">
              </div>
              <a href="/notifications" class="btn btn-primary">管理通知</a>
            </div>
          </div>
        </div>

        <div class="tab-pane fade" id="backup">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">数据备份</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">自动备份</label>
                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="autoBackup" checked>
                      <label class="form-check-label" for="autoBackup">
                        启用自动备份
                      </label>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">备份频率</label>
                    <select class="form-select" id="backupFrequency">
                      <option value="daily">每日备份</option>
                      <option value="weekly">每周备份</option>
                      <option value="monthly">每月备份</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="mb-3">
                <label class="form-label">保留天数</label>
                <input type="number" class="form-control" id="retentionDays" value="30" min="1" max="365">
              </div>
              <a href="/backup" class="btn btn-primary">管理备份</a>
            </div>
          </div>
        </div>

        <div class="tab-pane fade" id="logs">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">系统日志</h5>
            </div>
            <div class="card-body">
              <p class="text-muted">系统日志功能开发中...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 标签页切换功能
    document.addEventListener('DOMContentLoaded', function () {
      const tabLinks = document.querySelectorAll('.list-group-item[data-bs-toggle="tab"]');
      const tabPanes = document.querySelectorAll('.tab-pane');

      tabLinks.forEach(link => {
        link.addEventListener('click', function (e) {
          e.preventDefault();

          // 移除所有活动状态
          tabLinks.forEach(l => l.classList.remove('active'));
          tabPanes.forEach(p => p.classList.remove('show', 'active'));

          // 添加当前活动状态
          this.classList.add('active');
          const targetId = this.getAttribute('href').substring(1);
          const targetPane = document.getElementById(targetId);
          if (targetPane) {
            targetPane.classList.add('show', 'active');
          }
        });
      });
    });

    // 保存设置
    function saveSettings() {
      showMessage('设置保存成功', 'success');
    }
  </script>
  ` }) %>