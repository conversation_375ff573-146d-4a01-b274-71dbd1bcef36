<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .error-container {
            text-align: center;
            color: white;
            max-width: 600px;
            padding: 2rem;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .error-message {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        .error-description {
            font-size: 1rem;
            margin-bottom: 3rem;
            opacity: 0.8;
        }
        .btn-home {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            color: white;
            transform: translateY(-2px);
        }
        .error-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.7;
        }
        .error-details {
            background: rgba(0,0,0,0.2);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 2rem;
            text-align: left;
            font-family: monospace;
            font-size: 0.875rem;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <% if (error.status == 404) { %>
                <i class="bi bi-search"></i>
            <% } else if (error.status == 403) { %>
                <i class="bi bi-shield-exclamation"></i>
            <% } else if (error.status == 500) { %>
                <i class="bi bi-exclamation-triangle"></i>
            <% } else { %>
                <i class="bi bi-exclamation-circle"></i>
            <% } %>
        </div>
        
        <div class="error-code"><%= error.status %></div>
        
        <div class="error-message">
            <%= error.message %>
        </div>
        
        <div class="error-description">
            <% if (error.status == 404) { %>
                抱歉，您访问的页面不存在或已被移除。
            <% } else if (error.status == 403) { %>
                您没有权限访问此页面，请联系管理员。
            <% } else if (error.status == 500) { %>
                服务器遇到了一些问题，我们正在努力修复。
            <% } else { %>
                系统遇到了一个错误，请稍后再试。
            <% } %>
        </div>
        
        <div>
            <a href="/dashboard" class="btn-home">
                <i class="bi bi-house-door me-2"></i>
                返回首页
            </a>
        </div>
        
        <% if (error.stack && process.env.NODE_ENV === 'development') { %>
        <div class="error-details">
            <strong>错误详情（开发模式）：</strong><br>
            <%= error.stack %>
        </div>
        <% } %>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
