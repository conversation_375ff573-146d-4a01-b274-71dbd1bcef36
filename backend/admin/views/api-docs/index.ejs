<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        .api-stats-card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .api-stats-card:hover {
            transform: translateY(-2px);
        }
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        .changelog-item {
            border-left: 3px solid #dee2e6;
            padding-left: 1rem;
            margin-bottom: 1rem;
        }
        .changelog-item.added { border-left-color: #198754; }
        .changelog-item.updated { border-left-color: #0d6efd; }
        .changelog-item.fixed { border-left-color: #ffc107; }
        .changelog-item.breaking { border-left-color: #dc3545; }
        .search-box {
            max-width: 400px;
        }
    </style>
</head>
<body>
    <%- include('../layouts/main', { 
        title: title, 
        user: user, 
        currentPage: currentPage 
    }) %>

    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-1">
                            <i class="bi bi-book text-primary me-2"></i>
                            API文档中心
                        </h1>
                        <p class="text-muted mb-0">智慧养鹅SaaS平台API文档和开发工具</p>
                    </div>
                    <div>
                        <a href="/api-docs/swagger" class="btn btn-primary me-2" target="_blank">
                            <i class="bi bi-box-arrow-up-right me-1"></i>
                            打开Swagger UI
                        </a>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi bi-download me-1"></i>
                                下载
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="/api-docs/openapi.json" target="_blank">
                                        <i class="bi bi-filetype-json me-2"></i>
                                        OpenAPI JSON
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="/api-docs/openapi.yaml" target="_blank">
                                        <i class="bi bi-filetype-yml me-2"></i>
                                        OpenAPI YAML
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="/api-docs/api/postman-collection" target="_blank">
                                        <i class="bi bi-collection me-2"></i>
                                        Postman集合
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API统计卡片 -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card api-stats-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-api feature-icon text-primary"></i>
                        <h4 class="card-title" id="totalEndpoints"><%= data.totalEndpoints %></h4>
                        <p class="card-text text-muted">API端点</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card api-stats-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-tags feature-icon text-success"></i>
                        <h4 class="card-title" id="totalTags">-</h4>
                        <p class="card-text text-muted">API分类</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card api-stats-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-diagram-3 feature-icon text-info"></i>
                        <h4 class="card-title" id="totalSchemas">-</h4>
                        <p class="card-text text-muted">数据模型</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card api-stats-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-shield-check feature-icon text-warning"></i>
                        <h4 class="card-title" id="securitySchemes">-</h4>
                        <p class="card-text text-muted">安全方案</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 左侧主要内容 -->
            <div class="col-lg-8">
                <!-- API搜索 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-search me-2"></i>
                            API搜索
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchInput" placeholder="搜索API端点、标签或描述...">
                                    <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="tagFilter">
                                    <option value="">所有分类</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="methodFilter">
                                    <option value="">所有方法</option>
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                    <option value="PUT">PUT</option>
                                    <option value="PATCH">PATCH</option>
                                    <option value="DELETE">DELETE</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 搜索结果 -->
                        <div id="searchResults" class="mt-3" style="display: none;">
                            <h6>搜索结果：</h6>
                            <div id="searchResultsList"></div>
                        </div>
                    </div>
                </div>

                <!-- API功能特性 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-stars me-2"></i>
                            功能特性
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-shield-check text-success me-3" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <div>
                                        <h6>安全认证</h6>
                                        <p class="text-muted mb-0">支持JWT Bearer Token和租户标识认证</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-check2-square text-primary me-3" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <div>
                                        <h6>类型安全</h6>
                                        <p class="text-muted mb-0">基于Zod的请求验证和类型推断</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-arrow-left-right text-info me-3" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <div>
                                        <h6>标准化响应</h6>
                                        <p class="text-muted mb-0">统一的API响应格式和错误处理</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="bi bi-speedometer2 text-warning me-3" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <div>
                                        <h6>性能优化</h6>
                                        <p class="text-muted mb-0">内置缓存、限流和重试机制</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- API按方法统计 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-bar-chart me-2"></i>
                            端点统计
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="methodStats">
                            <!-- 动态生成HTTP方法统计 -->
                        </div>
                        
                        <hr>
                        
                        <div class="row" id="tagStats">
                            <!-- 动态生成标签统计 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧侧边栏 -->
            <div class="col-lg-4">
                <!-- 快速链接 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-link-45deg me-2"></i>
                            快速链接
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <a href="/api-docs/swagger" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" target="_blank">
                                <div>
                                    <i class="bi bi-window me-2"></i>
                                    Swagger UI
                                </div>
                                <i class="bi bi-box-arrow-up-right"></i>
                            </a>
                            <a href="/api-docs/api/postman-collection" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-collection me-2"></i>
                                    Postman集合
                                </div>
                                <i class="bi bi-download"></i>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" onclick="validateAPI()">
                                <div>
                                    <i class="bi bi-check-circle me-2"></i>
                                    验证API规范
                                </div>
                                <i class="bi bi-play"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- API信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            API信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <dl class="row mb-0">
                            <dt class="col-sm-5">版本：</dt>
                            <dd class="col-sm-7"><%= data.apiVersion %></dd>
                            
                            <dt class="col-sm-5">OpenAPI：</dt>
                            <dd class="col-sm-7" id="openApiVersion">-</dd>
                            
                            <dt class="col-sm-5">更新时间：</dt>
                            <dd class="col-sm-7" id="lastUpdated">-</dd>
                        </dl>
                    </div>
                </div>

                <!-- 最近更新 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history me-2"></i>
                            更新日志
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="changelogList">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <button class="btn btn-sm btn-outline-primary" onclick="loadChangelog()">
                                查看完整日志
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadAPIStats();
            loadChangelog();
            initializeSearch();
        });

        // 加载API统计信息
        function loadAPIStats() {
            fetch('/api-docs/api/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStats(data.data);
                    }
                })
                .catch(error => {
                    console.error('加载API统计失败:', error);
                });
        }

        // 更新统计显示
        function updateStats(stats) {
            document.getElementById('totalEndpoints').textContent = stats.totalEndpoints;
            document.getElementById('totalTags').textContent = Object.keys(stats.endpointsByTag).length;
            document.getElementById('totalSchemas').textContent = stats.schemas;
            document.getElementById('securitySchemes').textContent = stats.securitySchemes;
            document.getElementById('openApiVersion').textContent = stats.openApiVersion;
            document.getElementById('lastUpdated').textContent = formatDate(stats.lastUpdated);

            // 更新方法统计
            updateMethodStats(stats.endpointsByMethod);
            updateTagStats(stats.endpointsByTag);
            updateTagFilter(stats.endpointsByTag);
        }

        // 更新HTTP方法统计
        function updateMethodStats(methodStats) {
            const container = document.getElementById('methodStats');
            const colors = {
                GET: 'success',
                POST: 'primary', 
                PUT: 'warning',
                PATCH: 'info',
                DELETE: 'danger'
            };
            
            container.innerHTML = Object.entries(methodStats).map(([method, count]) => `
                <div class="col-auto mb-2">
                    <span class="badge bg-${colors[method] || 'secondary'} fs-6">
                        ${method} <span class="badge bg-light text-dark ms-1">${count}</span>
                    </span>
                </div>
            `).join('');
        }

        // 更新标签统计
        function updateTagStats(tagStats) {
            const container = document.getElementById('tagStats');
            const sortedTags = Object.entries(tagStats).sort(([,a], [,b]) => b - a);
            
            container.innerHTML = sortedTags.map(([tag, count]) => `
                <div class="col-md-6 mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-capitalize">${tag}</span>
                        <span class="badge bg-outline-secondary">${count}</span>
                    </div>
                </div>
            `).join('');
        }

        // 更新标签过滤器
        function updateTagFilter(tagStats) {
            const select = document.getElementById('tagFilter');
            const options = Object.keys(tagStats).map(tag => 
                `<option value="${tag}">${tag}</option>`
            ).join('');
            select.innerHTML = '<option value="">所有分类</option>' + options;
        }

        // 初始化搜索功能
        function initializeSearch() {
            const searchInput = document.getElementById('searchInput');
            const searchBtn = document.getElementById('searchBtn');
            
            searchBtn.addEventListener('click', performSearch);
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        }

        // 执行搜索
        function performSearch() {
            const query = document.getElementById('searchInput').value.trim();
            const tag = document.getElementById('tagFilter').value;
            const method = document.getElementById('methodFilter').value;
            
            if (!query) {
                hideSearchResults();
                return;
            }

            const params = new URLSearchParams({
                q: query,
                ...(tag && { tag }),
                ...(method && { method })
            });

            fetch(`/api-docs/api/search?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displaySearchResults(data.data);
                    }
                })
                .catch(error => {
                    console.error('搜索失败:', error);
                });
        }

        // 显示搜索结果
        function displaySearchResults(results) {
            const container = document.getElementById('searchResults');
            const list = document.getElementById('searchResultsList');
            
            if (results.length === 0) {
                list.innerHTML = '<p class="text-muted">未找到匹配的API端点</p>';
            } else {
                list.innerHTML = results.map(result => `
                    <div class="border rounded p-3 mb-2">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <span class="badge bg-primary me-2">${result.method}</span>
                                    <code>${result.path}</code>
                                </h6>
                                <p class="mb-1">${result.summary || '无描述'}</p>
                                <small class="text-muted">${result.description || ''}</small>
                            </div>
                            <div class="text-end">
                                ${result.tags.map(tag => `<span class="badge bg-light text-dark me-1">${tag}</span>`).join('')}
                            </div>
                        </div>
                    </div>
                `).join('');
            }
            
            container.style.display = 'block';
        }

        // 隐藏搜索结果
        function hideSearchResults() {
            document.getElementById('searchResults').style.display = 'none';
        }

        // 加载更新日志
        function loadChangelog() {
            fetch('/api-docs/api/changelog')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayChangelog(data.data.slice(0, 3)); // 只显示最近3个版本
                    }
                })
                .catch(error => {
                    console.error('加载更新日志失败:', error);
                    document.getElementById('changelogList').innerHTML = '<p class="text-muted">加载失败</p>';
                });
        }

        // 显示更新日志
        function displayChangelog(changelog) {
            const container = document.getElementById('changelogList');
            const typeColors = {
                added: 'success',
                updated: 'primary',
                fixed: 'warning',
                breaking: 'danger',
                deprecated: 'secondary'
            };
            
            container.innerHTML = changelog.map(version => `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">v${version.version}</h6>
                        <small class="text-muted">${version.date}</small>
                    </div>
                    ${version.changes.slice(0, 2).map(change => `
                        <div class="changelog-item ${change.type}">
                            <span class="badge bg-${typeColors[change.type] || 'secondary'} me-2">${change.type}</span>
                            <small>${change.description}</small>
                        </div>
                    `).join('')}
                </div>
            `).join('');
        }

        // 验证API规范
        function validateAPI() {
            const btn = event.target.closest('a');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="bi bi-spinner spin me-2"></i>验证中...';
            
            fetch('/api-docs/api/validate', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('API规范验证通过！');
                    } else {
                        alert('API规范验证失败：' + data.message);
                    }
                })
                .catch(error => {
                    alert('验证过程中发生错误');
                })
                .finally(() => {
                    btn.innerHTML = originalText;
                });
        }

        // 格式化日期
        function formatDate(dateString) {
            return new Date(dateString).toLocaleString('zh-CN');
        }
    </script>
</body>
</html>