<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>通知管理 - 智慧养鹅管理系统</title>

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

  <style>
    .notification-card {
      border-left: 4px solid #007bff;
      background: #f8f9fa;
      transition: all 0.3s ease;
    }

    .notification-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .notification-card.system {
      border-left-color: #dc3545;
    }

    .notification-card.feature {
      border-left-color: #28a745;
    }

    .notification-card.maintenance {
      border-left-color: #ffc107;
    }

    .priority-high {
      color: #dc3545;
    }

    .priority-medium {
      color: #ffc107;
    }

    .priority-low {
      color: #28a745;
    }

    .stats-card {
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }

    .stats-card:hover {
      transform: translateY(-2px);
    }
  </style>
</head>

<body class="bg-light">
  <%- include('partials/header') %>

    <div class="container-fluid">
      <div class="row">
        <%- include('partials/sidebar') %>

          <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div
              class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
              <h1 class="h2">
                <i class="fas fa-bell me-2"></i>通知管理
              </h1>
              <div class="btn-toolbar mb-2 mb-md-0">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                  data-bs-target="#createNotificationModal">
                  <i class="fas fa-plus me-1"></i>创建通知
                </button>
              </div>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
              <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card border-left-primary h-100 py-2">
                  <div class="card-body">
                    <div class="row no-gutters align-items-center">
                      <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                          总通知数</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalNotifications">-</div>
                      </div>
                      <div class="col-auto">
                        <i class="fas fa-bell fa-2x text-gray-300"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card border-left-success h-100 py-2">
                  <div class="card-body">
                    <div class="row no-gutters align-items-center">
                      <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                          活跃通知</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="activeNotifications">-</div>
                      </div>
                      <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card border-left-info h-100 py-2">
                  <div class="card-body">
                    <div class="row no-gutters align-items-center">
                      <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                          系统通知</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="systemNotifications">-</div>
                      </div>
                      <div class="col-auto">
                        <i class="fas fa-cog fa-2x text-gray-300"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card border-left-warning h-100 py-2">
                  <div class="card-body">
                    <div class="row no-gutters align-items-center">
                      <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                          高优先级</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="highPriorityNotifications">-</div>
                      </div>
                      <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 搜索和过滤 -->
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="mb-0">
                  <i class="fas fa-search me-2"></i>搜索与过滤
                </h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-4">
                    <label for="searchInput" class="form-label">搜索关键词</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索标题或内容...">
                  </div>
                  <div class="col-md-3">
                    <label for="typeFilter" class="form-label">通知类型</label>
                    <select class="form-select" id="typeFilter">
                      <option value="">全部类型</option>
                      <option value="system">系统通知</option>
                      <option value="feature">功能更新</option>
                      <option value="maintenance">维护通知</option>
                      <option value="general">一般通知</option>
                    </select>
                  </div>
                  <div class="col-md-3">
                    <label for="statusFilter" class="form-label">状态</label>
                    <select class="form-select" id="statusFilter">
                      <option value="">全部状态</option>
                      <option value="active">活跃</option>
                      <option value="inactive">停用</option>
                    </select>
                  </div>
                  <div class="col-md-2 d-flex align-items-end">
                    <button type="button" class="btn btn-outline-secondary w-100" onclick="loadNotifications()">
                      <i class="fas fa-search"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 通知列表 -->
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                  <i class="fas fa-list me-2"></i>通知列表
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                  <button type="button" class="btn btn-outline-primary" onclick="loadNotifications()">
                    <i class="fas fa-sync-alt"></i> 刷新
                  </button>
                </div>
              </div>
              <div class="card-body">
                <div id="notificationsList">
                  <div class="text-center py-5">
                    <div class="spinner-border" role="status">
                      <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载通知列表...</p>
                  </div>
                </div>

                <!-- 分页 -->
                <nav aria-label="通知分页" class="mt-4">
                  <ul class="pagination justify-content-center" id="pagination">
                    <!-- 分页内容将通过JavaScript动态生成 -->
                  </ul>
                </nav>
              </div>
            </div>
          </main>
      </div>
    </div>

    <!-- 创建通知模态框 -->
    <div class="modal fade" id="createNotificationModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-plus me-2"></i>创建新通知
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form id="notificationForm">
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="notificationTitle" class="form-label">通知标题</label>
                    <input type="text" class="form-control" id="notificationTitle" required>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="mb-3">
                    <label for="notificationType" class="form-label">通知类型</label>
                    <select class="form-select" id="notificationType" required>
                      <option value="">请选择类型</option>
                      <option value="system">系统通知</option>
                      <option value="feature">功能更新</option>
                      <option value="maintenance">维护通知</option>
                      <option value="general">一般通知</option>
                    </select>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="mb-3">
                    <label for="notificationPriority" class="form-label">优先级</label>
                    <select class="form-select" id="notificationPriority" required>
                      <option value="medium">中等</option>
                      <option value="high">高</option>
                      <option value="low">低</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="mb-3">
                <label for="notificationContent" class="form-label">通知内容</label>
                <textarea class="form-control" id="notificationContent" rows="4" required></textarea>
              </div>
              <div class="mb-3">
                <label for="targetUsers" class="form-label">目标用户</label>
                <select class="form-select" id="targetUsers">
                  <option value="all">所有用户</option>
                  <option value="premium">高级用户</option>
                  <option value="enterprise">企业用户</option>
                  <option value="basic">基础用户</option>
                </select>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" onclick="createNotification()">
              <i class="fas fa-paper-plane me-1"></i>发送通知
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
      let currentPage = 1;
      const pageSize = 10;

      // 页面加载时初始化
      document.addEventListener('DOMContentLoaded', function () {
        loadNotificationStats();
        loadNotifications();
      });

      // 加载通知统计
      function loadNotificationStats() {
        fetch('/api/notifications/stats')
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              const stats = data.data;
              document.getElementById('totalNotifications').textContent = stats.total;
              document.getElementById('activeNotifications').textContent = stats.active;
              document.getElementById('systemNotifications').textContent = stats.byType.system;
              document.getElementById('highPriorityNotifications').textContent = stats.byPriority.high;
            }
          })
          .catch(error => {
            console.error('加载统计失败:', error);
          });
      }

      // 加载通知列表
      function loadNotifications(page = 1) {
        currentPage = page;

        const search = document.getElementById('searchInput').value;
        const type = document.getElementById('typeFilter').value;
        const status = document.getElementById('statusFilter').value;

        const params = new URLSearchParams({
          page: page,
          limit: pageSize
        });

        if (search) params.append('search', search);
        if (type) params.append('type', type);
        if (status) params.append('status', status);

        fetch(`/api/notifications?${params}`)
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              renderNotifications(data.data.notifications);
              renderPagination(data.data.total, data.data.page, data.data.limit);
            } else {
              showAlert('加载失败: ' + data.message, 'danger');
            }
          })
          .catch(error => {
            console.error('加载通知失败:', error);
            showAlert('网络错误，请稍后重试', 'danger');
          });
      }

      // 渲染通知列表
      function renderNotifications(notifications) {
        const container = document.getElementById('notificationsList');

        if (notifications.length === 0) {
          container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无通知数据</p>
                    </div>
                `;
          return;
        }

        const html = notifications.map(notification => `
                <div class="notification-card card mb-3 ${notification.type}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-2">
                                    ${notification.title}
                                    <span class="badge bg-secondary ms-2">${getTypeLabel(notification.type)}</span>
                                    <span class="badge priority-${notification.priority} ms-1">${getPriorityLabel(notification.priority)}</span>
                                </h6>
                                <p class="card-text text-muted">${notification.content}</p>
                                <div class="d-flex align-items-center text-muted small">
                                    <i class="fas fa-clock me-1"></i>
                                    ${new Date(notification.createdAt).toLocaleString()}
                                    <span class="mx-2">|</span>
                                    <i class="fas fa-user me-1"></i>
                                    ${notification.createdBy}
                                    <span class="mx-2">|</span>
                                    <span class="badge ${notification.status === 'active' ? 'bg-success' : 'bg-secondary'}">
                                        ${notification.status === 'active' ? '活跃' : '停用'}
                                    </span>
                                </div>
                            </div>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="editNotification(${notification.id})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteNotification(${notification.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

        container.innerHTML = html;
      }

      // 渲染分页
      function renderPagination(total, page, limit) {
        const totalPages = Math.ceil(total / limit);
        const pagination = document.getElementById('pagination');

        if (totalPages <= 1) {
          pagination.innerHTML = '';
          return;
        }

        let html = '';

        // 上一页
        html += `
                <li class="page-item ${page <= 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="loadNotifications(${page - 1})">上一页</a>
                </li>
            `;

        // 页码
        for (let i = Math.max(1, page - 2); i <= Math.min(totalPages, page + 2); i++) {
          html += `
                    <li class="page-item ${i === page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadNotifications(${i})">${i}</a>
                    </li>
                `;
        }

        // 下一页
        html += `
                <li class="page-item ${page >= totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="loadNotifications(${page + 1})">下一页</a>
                </li>
            `;

        pagination.innerHTML = html;
      }

      // 创建通知
      function createNotification() {
        const title = document.getElementById('notificationTitle').value;
        const content = document.getElementById('notificationContent').value;
        const type = document.getElementById('notificationType').value;
        const priority = document.getElementById('notificationPriority').value;
        const targetUsers = document.getElementById('targetUsers').value;

        if (!title || !content || !type) {
          showAlert('请填写所有必填字段', 'warning');
          return;
        }

        fetch('/api/notifications', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            title, content, type, priority, targetUsers
          })
        })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              showAlert('通知创建成功', 'success');
              bootstrap.Modal.getInstance(document.getElementById('createNotificationModal')).hide();
              document.getElementById('notificationForm').reset();
              loadNotifications();
              loadNotificationStats();
            } else {
              showAlert('创建失败: ' + data.message, 'danger');
            }
          })
          .catch(error => {
            console.error('创建通知失败:', error);
            showAlert('网络错误，请稍后重试', 'danger');
          });
      }

      // 删除通知
      function deleteNotification(id) {
        if (!confirm('确定要删除这个通知吗？')) {
          return;
        }

        fetch(`/api/notifications/${id}`, {
          method: 'DELETE'
        })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              showAlert('通知删除成功', 'success');
              loadNotifications();
              loadNotificationStats();
            } else {
              showAlert('删除失败: ' + data.message, 'danger');
            }
          })
          .catch(error => {
            console.error('删除通知失败:', error);
            showAlert('网络错误，请稍后重试', 'danger');
          });
      }

      // 工具函数
      function getTypeLabel(type) {
        const labels = {
          'system': '系统',
          'feature': '功能',
          'maintenance': '维护',
          'general': '一般'
        };
        return labels[type] || type;
      }

      function getPriorityLabel(priority) {
        const labels = {
          'high': '高',
          'medium': '中',
          'low': '低'
        };
        return labels[priority] || priority;
      }

      function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

        document.querySelector('main').insertBefore(alertDiv, document.querySelector('main').firstChild);

        setTimeout(() => {
          alertDiv.remove();
        }, 5000);
      }
    </script>
</body>

</html>