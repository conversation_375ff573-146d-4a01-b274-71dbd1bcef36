<%- include('../layouts/main', { body: `
<!-- 操作按钮 -->
<div class="row mb-3">
    <div class="col-md-6">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
            <i class="bi bi-cloud-upload me-2"></i>上传图标
        </button>
    </div>
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="搜索图标..." id="searchInput">
            <button class="btn btn-outline-secondary" type="button">
                <i class="bi bi-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- 图标网格 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-image me-2"></i>图标库
        </h5>
    </div>
    <div class="card-body">
        <div class="row" id="iconGrid">
            <!-- Bootstrap Icons 示例 -->
            <div class="col-md-2 col-sm-3 col-4 mb-3">
                <div class="card text-center icon-card">
                    <div class="card-body">
                        <i class="bi bi-house-door" style="font-size: 2rem;"></i>
                        <p class="card-text small mt-2">house-door</p>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="copyIcon('bi-house-door')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteIcon('house-door')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-2 col-sm-3 col-4 mb-3">
                <div class="card text-center icon-card">
                    <div class="card-body">
                        <i class="bi bi-people" style="font-size: 2rem;"></i>
                        <p class="card-text small mt-2">people</p>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="copyIcon('bi-people')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteIcon('people')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-2 col-sm-3 col-4 mb-3">
                <div class="card text-center icon-card">
                    <div class="card-body">
                        <i class="bi bi-gear" style="font-size: 2rem;"></i>
                        <p class="card-text small mt-2">gear</p>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="copyIcon('bi-gear')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteIcon('gear')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-2 col-sm-3 col-4 mb-3">
                <div class="card text-center icon-card">
                    <div class="card-body">
                        <i class="bi bi-heart-pulse" style="font-size: 2rem;"></i>
                        <p class="card-text small mt-2">heart-pulse</p>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="copyIcon('bi-heart-pulse')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteIcon('heart-pulse')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-2 col-sm-3 col-4 mb-3">
                <div class="card text-center icon-card">
                    <div class="card-body">
                        <i class="bi bi-clipboard-data" style="font-size: 2rem;"></i>
                        <p class="card-text small mt-2">clipboard-data</p>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="copyIcon('bi-clipboard-data')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteIcon('clipboard-data')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-2 col-sm-3 col-4 mb-3">
                <div class="card text-center icon-card">
                    <div class="card-body">
                        <i class="bi bi-egg-fried" style="font-size: 2rem;"></i>
                        <p class="card-text small mt-2">egg-fried</p>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="copyIcon('bi-egg-fried')">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteIcon('egg-fried')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.icon-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.icon-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.icon-card .btn-group {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.icon-card:hover .btn-group {
    opacity: 1;
}
</style>

<script>
// 搜索功能
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const iconCards = document.querySelectorAll('.icon-card');
    
    iconCards.forEach(card => {
        const iconName = card.querySelector('.card-text').textContent.toLowerCase();
        const cardContainer = card.closest('.col-md-2, .col-sm-3, .col-4');
        
        if (iconName.includes(searchTerm)) {
            cardContainer.style.display = '';
        } else {
            cardContainer.style.display = 'none';
        }
    });
});

// 复制图标类名
function copyIcon(iconClass) {
    navigator.clipboard.writeText(iconClass).then(() => {
        showMessage(\`图标类名 "\${iconClass}" 已复制到剪贴板\`, 'success');
    }).catch(() => {
        showMessage('复制失败', 'danger');
    });
}

// 删除图标
function deleteIcon(iconName) {
    if (confirm(\`确定要删除图标 "\${iconName}" 吗？\`)) {
        showMessage('删除功能开发中...', 'info');
    }
}
</script>
` }) %>
