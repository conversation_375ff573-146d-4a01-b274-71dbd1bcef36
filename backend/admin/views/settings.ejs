<% layout('layout') -%>

<div class="row">
    <div class="col-md-3">
        <div class="list-group">
            <a href="#basic" class="list-group-item list-group-item-action active" data-bs-toggle="tab">
                <i class="bi bi-gear me-2"></i>基本设置
            </a>
            <a href="#users" class="list-group-item list-group-item-action" data-bs-toggle="tab">
                <i class="bi bi-people me-2"></i>用户权限
            </a>
            <a href="#notifications" class="list-group-item list-group-item-action" data-bs-toggle="tab">
                <i class="bi bi-bell me-2"></i>通知设置
            </a>
            <a href="#backup" class="list-group-item list-group-item-action" data-bs-toggle="tab">
                <i class="bi bi-archive me-2"></i>数据备份
            </a>
            <a href="#logs" class="list-group-item list-group-item-action" data-bs-toggle="tab">
                <i class="bi bi-file-text me-2"></i>系统日志
            </a>
        </div>
    </div>
    <div class="col-md-9">
        <div class="tab-content">
            <!-- 基本设置 -->
            <div class="tab-pane fade show active" id="basic">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">基本设置</h5>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="systemName" class="form-label">系统名称</label>
                                        <input type="text" class="form-control" id="systemName" value="智慧养鹅管理系统">
                                    </div>
                                    <div class="mb-3">
                                        <label for="adminEmail" class="form-label">管理员邮箱</label>
                                        <input type="email" class="form-control" id="adminEmail" value="<EMAIL>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="timezone" class="form-label">时区</label>
                                        <select class="form-select" id="timezone">
                                            <option value="Asia/Shanghai" selected>Asia/Shanghai</option>
                                            <option value="UTC">UTC</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">系统语言</label>
                                        <select class="form-select" id="language">
                                            <option value="zh-CN" selected>简体中文</option>
                                            <option value="en-US">English</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="theme" class="form-label">主题</label>
                                        <select class="form-select" id="theme">
                                            <option value="light" selected>浅色主题</option>
                                            <option value="dark">深色主题</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="pageSize" class="form-label">每页显示条数</label>
                                        <select class="form-select" id="pageSize">
                                            <option value="10">10</option>
                                            <option value="20" selected>20</option>
                                            <option value="50">50</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <button type="button" class="btn btn-primary">保存设置</button>
                                    <button type="button" class="btn btn-secondary">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 用户权限 -->
            <div class="tab-pane fade" id="users">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">用户权限设置</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>角色</th>
                                        <th>用户管理</th>
                                        <th>生产管理</th>
                                        <th>健康管理</th>
                                        <th>系统设置</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>管理员</td>
                                        <td><i class="bi bi-check-circle text-success"></i></td>
                                        <td><i class="bi bi-check-circle text-success"></i></td>
                                        <td><i class="bi bi-check-circle text-success"></i></td>
                                        <td><i class="bi bi-check-circle text-success"></i></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">编辑</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>经理</td>
                                        <td><i class="bi bi-check-circle text-success"></i></td>
                                        <td><i class="bi bi-check-circle text-success"></i></td>
                                        <td><i class="bi bi-check-circle text-success"></i></td>
                                        <td><i class="bi bi-x-circle text-danger"></i></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">编辑</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>用户</td>
                                        <td><i class="bi bi-x-circle text-danger"></i></td>
                                        <td><i class="bi bi-check-circle text-success"></i></td>
                                        <td><i class="bi bi-check-circle text-success"></i></td>
                                        <td><i class="bi bi-x-circle text-danger"></i></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">编辑</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 通知设置 -->
            <div class="tab-pane fade" id="notifications">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">通知设置</h5>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                    <label class="form-check-label" for="emailNotifications">
                                        邮件通知
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="smsNotifications">
                                    <label class="form-check-label" for="smsNotifications">
                                        短信通知
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="systemAlerts" checked>
                                    <label class="form-check-label" for="systemAlerts">
                                        系统警报
                                    </label>
                                </div>
                            </div>
                            <button type="button" class="btn btn-primary">保存设置</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 数据备份 -->
            <div class="tab-pane fade" id="backup">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">数据备份</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>自动备份设置</h6>
                                <form>
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="autoBackup" checked>
                                            <label class="form-check-label" for="autoBackup">
                                                启用自动备份
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="backupFrequency" class="form-label">备份频率</label>
                                        <select class="form-select" id="backupFrequency">
                                            <option value="daily" selected>每日</option>
                                            <option value="weekly">每周</option>
                                            <option value="monthly">每月</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="backupTime" class="form-label">备份时间</label>
                                        <input type="time" class="form-control" id="backupTime" value="02:00">
                                    </div>
                                </form>
                            </div>
                            <div class="col-md-6">
                                <h6>手动备份</h6>
                                <p class="text-muted">立即创建数据库备份</p>
                                <button type="button" class="btn btn-success">
                                    <i class="bi bi-download me-1"></i>立即备份
                                </button>
                                <hr>
                                <h6>备份历史</h6>
                                <div class="list-group">
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>backup_2023_12_01.sql</strong>
                                            <br><small class="text-muted">2023-12-01 02:00:00</small>
                                        </div>
                                        <div>
                                            <button class="btn btn-sm btn-outline-primary">下载</button>
                                            <button class="btn btn-sm btn-outline-danger">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统日志 -->
            <div class="tab-pane fade" id="logs">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">系统日志</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <select class="form-select">
                                    <option value="all">所有日志</option>
                                    <option value="error">错误日志</option>
                                    <option value="warning">警告日志</option>
                                    <option value="info">信息日志</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                </button>
                                <button class="btn btn-outline-primary">
                                    <i class="bi bi-download me-1"></i>导出
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>级别</th>
                                        <th>消息</th>
                                        <th>用户</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2023-12-01 10:30:15</td>
                                        <td><span class="badge bg-success">INFO</span></td>
                                        <td>用户登录成功</td>
                                        <td>admin</td>
                                    </tr>
                                    <tr>
                                        <td>2023-12-01 10:25:32</td>
                                        <td><span class="badge bg-warning">WARN</span></td>
                                        <td>数据库连接超时</td>
                                        <td>system</td>
                                    </tr>
                                    <tr>
                                        <td>2023-12-01 10:20:45</td>
                                        <td><span class="badge bg-danger">ERROR</span></td>
                                        <td>文件上传失败</td>
                                        <td>user123</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 标签页切换功能
document.addEventListener('DOMContentLoaded', function() {
    const tabLinks = document.querySelectorAll('.list-group-item[data-bs-toggle="tab"]');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除所有活动状态
            tabLinks.forEach(l => l.classList.remove('active'));
            tabPanes.forEach(p => p.classList.remove('show', 'active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            const targetId = this.getAttribute('href').substring(1);
            const targetPane = document.getElementById(targetId);
            if (targetPane) {
                targetPane.classList.add('show', 'active');
            }
        });
    });
});
</script>
