<% layout('layout') -%>

<div class="row">
    <div class="col-md-3">
        <div class="card stat-card border-primary mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">用户总数</h5>
                        <h2>128</h2>
                    </div>
                    <div class="stat-icon bg-primary text-white">
                        <i class="bi bi-people fs-4"></i>
                    </div>
                </div>
                <p class="card-text text-muted">较上月增长 12%</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card border-success mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">健康记录</h5>
                        <h2>1,245</h2>
                    </div>
                    <div class="stat-icon bg-success text-white">
                        <i class="bi bi-heart-pulse fs-4"></i>
                    </div>
                </div>
                <p class="card-text text-muted">较上月增长 8%</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card border-info mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">生产记录</h5>
                        <h2>867</h2>
                    </div>
                    <div class="stat-icon bg-info text-white">
                        <i class="bi bi-egg fs-4"></i>
                    </div>
                </div>
                <p class="card-text text-muted">较上月增长 15%</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card border-warning mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">在线用户</h5>
                        <h2>24</h2>
                    </div>
                    <div class="stat-icon bg-warning text-white">
                        <i class="bi bi-person-circle fs-4"></i>
                    </div>
                </div>
                <p class="card-text text-muted">当前在线</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">数据趋势</h5>
            </div>
            <div class="card-body">
                <canvas id="trendChart" height="100"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">系统状态</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        数据库连接
                        <span class="badge bg-success rounded-pill">正常</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        API服务
                        <span class="badge bg-success rounded-pill">正常</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        存储空间
                        <span class="badge bg-warning rounded-pill">85%</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        内存使用
                        <span class="badge bg-success rounded-pill">42%</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        CPU使用率
                        <span class="badge bg-success rounded-pill">18%</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">最新用户</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>养殖场</th>
                                <th>注册时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>zhangsan</td>
                                <td>张三养殖场</td>
                                <td>2023-06-15</td>
                            </tr>
                            <tr>
                                <td>lisi</td>
                                <td>李四养鹅场</td>
                                <td>2023-06-14</td>
                            </tr>
                            <tr>
                                <td>wangwu</td>
                                <td>王五养殖场</td>
                                <td>2023-06-12</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">系统日志</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item">
                        <small class="text-muted">2023-06-15 14:30</small><br>
                        用户 zhangsan 登录系统
                    </li>
                    <li class="list-group-item">
                        <small class="text-muted">2023-06-15 13:45</small><br>
                        新增健康记录 #1245
                    </li>
                    <li class="list-group-item">
                        <small class="text-muted">2023-06-15 12:20</small><br>
                        系统备份完成
                    </li>
                    <li class="list-group-item">
                        <small class="text-muted">2023-06-15 10:15</small><br>
                        用户 lisi 更新养殖场信息
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// 初始化图表
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('trendChart').getContext('2d');
    const trendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '用户增长',
                data: [65, 78, 90, 102, 115, 128],
                borderColor: '#0066cc',
                backgroundColor: 'rgba(0, 102, 204, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: '健康记录',
                data: [220, 340, 560, 780, 1020, 1245],
                borderColor: '#00cc99',
                backgroundColor: 'rgba(0, 204, 153, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-size: .875rem;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
        }
        
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .sidebar .nav-link {
            font-weight: 500;
            color: #333;
        }
        
        .sidebar .nav-link.active {
            color: #0066cc;
        }
        
        .sidebar-heading {
            font-size: .75rem;
            text-transform: uppercase;
        }
        
        .navbar-brand {
            padding-top: .75rem;
            padding-bottom: .75rem;
            font-size: 1rem;
            background-color: rgba(0, 0, 0, .25);
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .navbar-brand:hover {
            background-color: rgba(0, 0, 0, .35);
        }
        
        .stat-card {
            border-radius: 10px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
        }
        
        /* 图标管理样式 */
        .icon-preview {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 5px;
            margin-bottom: 15px;
            transition: all 0.3s;
        }
        
        .icon-item:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-color: #0066cc;
        }
        
        .icon-name {
            font-size: 0.8rem;
            margin-top: 8px;
            color: #666;
        }
    </style>
</head>
<body>
    <header class="navbar navbar-dark sticky-top bg-primary flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="/admin">智慧养鹅管理系统</a>
        <button class="navbar-toggler d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="navbar-nav">
            <div class="nav-item text-nowrap">
                <a class="nav-link px-3" href="#">退出登录</a>
            </div>
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'home') ? 'active' : '' %>" href="/admin">
                                <i class="bi bi-speedometer2 me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'users') ? 'active' : '' %>" href="/admin/users">
                                <i class="bi bi-people me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'health') ? 'active' : '' %>" href="/admin/health">
                                <i class="bi bi-heart-pulse me-2"></i>
                                健康管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'production') ? 'active' : '' %>" href="/admin/production">
                                <i class="bi bi-egg me-2"></i>
                                生产管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'icons') ? 'active' : '' %>" href="/admin/icons">
                                <i class="bi bi-image me-2"></i>
                                图标管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= (typeof page !== 'undefined' && page === 'settings') ? 'active' : '' %>" href="/admin/settings">
                                <i class="bi bi-gear me-2"></i>
                                系统设置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><%= title %></h1>
                </div>
                
                <%- typeof body !== 'undefined' ? body : '' %>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</body>
</html>