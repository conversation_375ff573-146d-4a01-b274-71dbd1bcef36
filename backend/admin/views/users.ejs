<div class="row mb-3">
    <div class="col-md-6">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="bi bi-plus-circle me-1"></i> 添加用户
        </button>
    </div>
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="搜索用户...">
            <button class="btn btn-outline-secondary" type="button">
                <i class="bi bi-search"></i>
            </button>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>姓名</th>
                        <th>养殖场</th>
                        <th>手机号</th>
                        <th>邮箱</th>
                        <th>角色</th>
                        <th>注册时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>admin</td>
                        <td>管理员</td>
                        <td>系统管理</td>
                        <td>13800138000</td>
                        <td><EMAIL></td>
                        <td><span class="badge bg-danger">管理员</span></td>
                        <td>2023-01-01</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary">编辑</button>
                            <button class="btn btn-sm btn-outline-danger">删除</button>
                        </td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>zhangsan</td>
                        <td>张三</td>
                        <td>张三养殖场</td>
                        <td>13800138001</td>
                        <td><EMAIL></td>
                        <td><span class="badge bg-success">用户</span></td>
                        <td>2023-06-15</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary">编辑</button>
                            <button class="btn btn-sm btn-outline-danger">删除</button>
                        </td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>lisi</td>
                        <td>李四</td>
                        <td>李四养鹅场</td>
                        <td>13800138002</td>
                        <td><EMAIL></td>
                        <td><span class="badge bg-success">用户</span></td>
                        <td>2023-06-14</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary">编辑</button>
                            <button class="btn btn-sm btn-outline-danger">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <nav>
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1">上一页</a>
                </li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item"><a class="page-link" href="#">2</a></li>
                <li class="page-item"><a class="page-link" href="#">3</a></li>
                <li class="page-item">
                    <a class="page-link" href="#">下一页</a>
                </li>
            </ul>
        </nav>
    </div>
</div>

<!-- 添加用户模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">添加用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="name" class="form-label">姓名</label>
                        <input type="text" class="form-control" id="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="farmName" class="form-label">养殖场名称</label>
                        <input type="text" class="form-control" id="farmName">
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">手机号</label>
                        <input type="tel" class="form-control" id="phone">
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="email">
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">角色</label>
                        <select class="form-select" id="role">
                            <option value="user">用户</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary">添加</button>
            </div>
        </div>
    </div>
</div>