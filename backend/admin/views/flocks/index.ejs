<!-- 鹅群管理页面 -->
<div class="enterprise-page-container">
    <!-- 页面头部 -->
    <div class="page-header-modern">
        <div class="page-header-content">
            <div class="page-title-section">
                <h1 class="page-title-modern">
                    <i class="bi bi-collection"></i>
                    鹅群管理
                </h1>
                <p class="page-subtitle-modern">管理鹅群信息、监控健康状态和生产性能</p>
                <div class="page-breadcrumb">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/dashboard">首页</a></li>
                            <li class="breadcrumb-item active">鹅群管理</li>
                        </ol>
                    </nav>
                </div>
            </div>
            <div class="page-actions-modern">
                <button type="button" class="btn btn-outline-primary" onclick="exportFlocks()">
                    <i class="bi bi-download"></i>
                    导出数据
                </button>
                <button type="button" class="btn btn-primary" onclick="showCreateFlockModal()">
                    <i class="bi bi-plus-lg"></i>
                    新建鹅群
                </button>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards-container">
        <div class="row g-3">
            <div class="col-xl-3 col-md-6">
                <div class="stats-card primary">
                    <div class="stats-card-body">
                        <div class="stats-icon">
                            <i class="bi bi-collection"></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" id="totalFlocks">0</div>
                            <div class="stats-label">总鹅群数</div>
                            <div class="stats-change positive">
                                <i class="bi bi-arrow-up"></i> +3 本月
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card success">
                    <div class="stats-card-body">
                        <div class="stats-icon">
                            <i class="bi bi-heart-pulse"></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" id="totalGeese">0</div>
                            <div class="stats-label">总鹅只数</div>
                            <div class="stats-change positive">
                                <i class="bi bi-arrow-up"></i> +156 本周
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card warning">
                    <div class="stats-card-body">
                        <div class="stats-icon">
                            <i class="bi bi-shield-check"></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" id="healthyFlocks">0</div>
                            <div class="stats-label">健康鹅群</div>
                            <div class="stats-change positive">
                                <i class="bi bi-arrow-up"></i> 95.2%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card info">
                    <div class="stats-card-body">
                        <div class="stats-icon">
                            <i class="bi bi-egg"></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" id="avgProduction">0</div>
                            <div class="stats-label">平均产蛋率</div>
                            <div class="stats-change positive">
                                <i class="bi bi-arrow-up"></i> +2.3% 昨日
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-toolbar-modern">
        <div class="filter-section">
            <div class="search-box-modern">
                <i class="bi bi-search"></i>
                <input type="text" id="searchInput" placeholder="搜索鹅群名称、批次号..." class="form-control">
                <button type="button" class="search-clear" onclick="clearSearch()" style="display: none;">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="filter-group-modern">
                <div class="filter-item">
                    <label>品种</label>
                    <select id="breedFilter" class="form-select">
                        <option value="">全部品种</option>
                        <option value="white_goose">白鹅</option>
                        <option value="grey_goose">灰鹅</option>
                        <option value="embden">埃姆登鹅</option>
                        <option value="toulouse">图卢兹鹅</option>
                        <option value="chinese">中国鹅</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>状态</label>
                    <select id="statusFilter" class="form-select">
                        <option value="">全部状态</option>
                        <option value="active">活跃</option>
                        <option value="inactive">非活跃</option>
                        <option value="sold">已售出</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>年龄组</label>
                    <select id="ageGroupFilter" class="form-select">
                        <option value="">全部年龄</option>
                        <option value="young">幼鹅</option>
                        <option value="adult">成鹅</option>
                        <option value="breeding">种鹅</option>
                        <option value="retired">淘汰</option>
                    </select>
                </div>
                <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                    <i class="bi bi-arrow-clockwise"></i>
                    重置
                </button>
            </div>
        </div>
        
        <!-- 批量操作工具栏 -->
        <div class="batch-actions-modern" id="batchActions" style="display: none;">
            <div class="batch-info">
                <span class="selected-count">已选择 <span id="selectedCount">0</span> 项</span>
                <button type="button" class="btn-link" onclick="clearSelection()">清除选择</button>
            </div>
            <div class="batch-buttons">
                <button type="button" class="btn btn-outline-primary" onclick="batchExport()">
                    <i class="bi bi-download"></i>
                    导出选中
                </button>
                <button type="button" class="btn btn-outline-warning" onclick="batchUpdateStatus()">
                    <i class="bi bi-pencil-square"></i>
                    批量修改状态
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="batchDeleteFlocks()">
                    <i class="bi bi-trash"></i>
                    批量删除
                </button>
            </div>
        </div>
    </div>

    <!-- 数据表格 -->
    <div class="data-table-modern">
        <div class="table-header">
            <div class="table-title">
                <h5>鹅群列表</h5>
                <span class="table-count">共 <span id="totalCount">0</span> 条记录</span>
            </div>
            <div class="table-tools">
                <div class="view-toggle">
                    <button type="button" class="btn btn-sm btn-outline-secondary active" data-view="table">
                        <i class="bi bi-table"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" data-view="card">
                        <i class="bi bi-grid-3x3-gap"></i>
                    </button>
                </div>
                <div class="table-settings">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="showColumnSettings()">
                        <i class="bi bi-gear"></i>
                        列设置
                    </button>
                </div>
            </div>
        </div>
        
        <div class="table-container-modern">
            <div class="table-responsive">
                <table class="table table-hover enterprise-table" id="flocksTable">
                    <thead>
                        <tr>
                            <th width="50">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th class="sortable" data-sort="name">
                                鹅群名称
                                <i class="bi bi-arrow-down-up sort-icon"></i>
                            </th>
                            <th>批次号</th>
                            <th>品种</th>
                            <th class="sortable" data-sort="totalCount">
                                总数量
                                <i class="bi bi-arrow-down-up sort-icon"></i>
                            </th>
                            <th class="sortable" data-sort="currentCount">
                                当前数量
                                <i class="bi bi-arrow-down-up sort-icon"></i>
                            </th>
                            <th>性别分布</th>
                            <th>年龄组</th>
                            <th>健康状态</th>
                            <th>生产率</th>
                            <th>存活率</th>
                            <th>状态</th>
                            <th class="sortable" data-sort="establishedDate">
                                建群日期
                                <i class="bi bi-arrow-down-up sort-icon"></i>
                            </th>
                            <th width="140">操作</th>
                        </tr>
                    </thead>
                    <tbody id="flocksTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 加载状态 -->
            <div class="loading-state-modern" id="loadingState">
                <div class="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
                <p>正在加载鹅群数据...</p>
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state-modern" id="emptyState" style="display: none;">
                <div class="empty-icon">
                    <i class="bi bi-collection"></i>
                </div>
                <h3>暂无鹅群数据</h3>
                <p>还没有任何鹅群，点击上方按钮创建第一个鹅群</p>
                <button type="button" class="btn btn-primary" onclick="showCreateFlockModal()">
                    <i class="bi bi-plus-lg"></i>
                    创建鹅群
                </button>
            </div>
            
            <!-- 错误状态 -->
            <div class="error-state-modern" id="errorState" style="display: none;">
                <div class="error-icon">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <h3>加载失败</h3>
                <p id="errorMessage">数据加载失败，请稍后重试</p>
                <button type="button" class="btn btn-outline-primary" onclick="loadFlocks()">
                    <i class="bi bi-arrow-clockwise"></i>
                    重新加载
                </button>
            </div>
        </div>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container-modern">
        <div class="pagination-info">
            <span id="paginationInfo">显示第 1-10 条，共 0 条记录</span>
        </div>
        <nav aria-label="鹅群列表分页">
            <ul class="pagination pagination-modern" id="pagination">
                <!-- 分页按钮将通过JavaScript动态生成 -->
            </ul>
        </nav>
        <div class="page-size-selector">
            <label>每页显示</label>
            <select id="pageSizeSelect" class="form-select">
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
            <label>条</label>
        </div>
    </div>
</div>

<style>
/* 企业级鹅群管理页面样式 */
.enterprise-page-container {
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header-modern {
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 24px;
    color: white;
    position: relative;
    overflow: hidden;
}

.page-header-modern::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.stats-cards-container {
    margin-bottom: 24px;
}

.stats-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
    transition: transform 0.2s, box-shadow 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stats-card.primary {
    border-left-color: #3b82f6;
}

.stats-card.success {
    border-left-color: #10b981;
}

.stats-card.warning {
    border-left-color: #f59e0b;
}

.stats-card.info {
    border-left-color: #06b6d4;
}

.stats-card-body {
    display: flex;
    align-items: center;
    gap: 16px;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stats-card.primary .stats-icon {
    background: #3b82f6;
}

.stats-card.success .stats-icon {
    background: #10b981;
}

.stats-card.warning .stats-icon {
    background: #f59e0b;
}

.stats-card.info .stats-icon {
    background: #06b6d4;
}

.stats-content {
    flex: 1;
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 4px;
}

.stats-label {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 8px;
}

.stats-change {
    font-size: 0.75rem;
    font-weight: 500;
}

.stats-change.positive {
    color: #10b981;
}

.stats-change.negative {
    color: #ef4444;
}

.stats-change.neutral {
    color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .enterprise-page-container {
        padding: 16px;
    }
    
    .page-header-modern {
        padding: 24px;
    }
    
    .stats-card-body {
        flex-direction: column;
        text-align: center;
    }
    
    .stats-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .stats-number {
        font-size: 1.5rem;
    }
}
</style>

<script src="/js/flocks-enterprise.js"></script>
