<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>备份管理 - 智慧养鹅管理系统</title>

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

  <style>
    .backup-card {
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .backup-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .backup-status.completed {
      color: #28a745;
    }

    .backup-status.failed {
      color: #dc3545;
    }

    .backup-status.running {
      color: #ffc107;
    }

    .stats-card {
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }

    .stats-card:hover {
      transform: translateY(-2px);
    }

    .progress-wrapper {
      position: relative;
      margin: 10px 0;
    }

    .config-section {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
    }
  </style>
</head>

<body class="bg-light">
  <%- include('partials/header') %>

    <div class="container-fluid">
      <div class="row">
        <%- include('partials/sidebar') %>

          <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div
              class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
              <h1 class="h2">
                <i class="fas fa-hdd me-2"></i>备份管理
              </h1>
              <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                  <button type="button" class="btn btn-primary" onclick="createBackup()">
                    <i class="fas fa-plus me-1"></i>创建备份
                  </button>
                  <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal"
                    data-bs-target="#configModal">
                    <i class="fas fa-cog me-1"></i>配置
                  </button>
                </div>
              </div>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
              <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card border-left-primary h-100 py-2">
                  <div class="card-body">
                    <div class="row no-gutters align-items-center">
                      <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                          总备份数</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalBackups">-</div>
                      </div>
                      <div class="col-auto">
                        <i class="fas fa-hdd fa-2x text-gray-300"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card border-left-success h-100 py-2">
                  <div class="card-body">
                    <div class="row no-gutters align-items-center">
                      <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                          成功备份</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="completedBackups">-</div>
                      </div>
                      <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card border-left-info h-100 py-2">
                  <div class="card-body">
                    <div class="row no-gutters align-items-center">
                      <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                          总大小</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalSize">-</div>
                      </div>
                      <div class="col-auto">
                        <i class="fas fa-database fa-2x text-gray-300"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stats-card border-left-warning h-100 py-2">
                  <div class="card-body">
                    <div class="row no-gutters align-items-center">
                      <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                          最近备份</div>
                        <div class="h6 mb-0 font-weight-bold text-gray-800" id="lastBackup">-</div>
                      </div>
                      <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 筛选和搜索 -->
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="mb-0">
                  <i class="fas fa-filter me-2"></i>筛选与操作
                </h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-3">
                    <label for="typeFilter" class="form-label">备份类型</label>
                    <select class="form-select" id="typeFilter">
                      <option value="">全部类型</option>
                      <option value="auto">自动备份</option>
                      <option value="manual">手动备份</option>
                    </select>
                  </div>
                  <div class="col-md-3">
                    <label for="statusFilter" class="form-label">状态</label>
                    <select class="form-select" id="statusFilter">
                      <option value="">全部状态</option>
                      <option value="completed">已完成</option>
                      <option value="failed">失败</option>
                      <option value="running">进行中</option>
                    </select>
                  </div>
                  <div class="col-md-4 d-flex align-items-end gap-2">
                    <button type="button" class="btn btn-outline-secondary" onclick="loadBackups()">
                      <i class="fas fa-search"></i> 筛选
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="loadBackups()">
                      <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 备份列表 -->
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                  <i class="fas fa-list me-2"></i>备份列表
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                  <button type="button" class="btn btn-outline-danger" onclick="cleanupOldBackups()">
                    <i class="fas fa-trash-alt"></i> 清理旧备份
                  </button>
                </div>
              </div>
              <div class="card-body">
                <div id="backupsList">
                  <div class="text-center py-5">
                    <div class="spinner-border" role="status">
                      <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载备份列表...</p>
                  </div>
                </div>

                <!-- 分页 -->
                <nav aria-label="备份分页" class="mt-4">
                  <ul class="pagination justify-content-center" id="pagination">
                    <!-- 分页内容将通过JavaScript动态生成 -->
                  </ul>
                </nav>
              </div>
            </div>
          </main>
      </div>
    </div>

    <!-- 备份配置模态框 -->
    <div class="modal fade" id="configModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-cog me-2"></i>备份配置
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div class="config-section">
              <h6 class="fw-bold mb-3">
                <i class="fas fa-robot me-2"></i>自动备份设置
              </h6>
              <div class="row">
                <div class="col-md-6">
                  <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="autoBackupEnabled" checked>
                    <label class="form-check-label" for="autoBackupEnabled">
                      启用自动备份
                    </label>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="backupSchedule" class="form-label">备份计划</label>
                    <select class="form-select" id="backupSchedule">
                      <option value="0 2 * * *">每天凌晨2点</option>
                      <option value="0 1 * * 0">每周日凌晨1点</option>
                      <option value="0 3 1 * *">每月1日凌晨3点</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="retentionDays" class="form-label">保留天数</label>
                    <input type="number" class="form-control" id="retentionDays" value="30" min="1" max="365">
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-check form-switch mb-3" style="margin-top: 32px;">
                    <input class="form-check-input" type="checkbox" id="compressBackup" checked>
                    <label class="form-check-label" for="compressBackup">
                      压缩备份文件
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div class="config-section">
              <h6 class="fw-bold mb-3">
                <i class="fas fa-envelope me-2"></i>通知设置
              </h6>
              <div class="row">
                <div class="col-md-6">
                  <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="notificationEnabled" checked>
                    <label class="form-check-label" for="notificationEnabled">
                      启用通知
                    </label>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="notificationEmail" class="form-label">通知邮箱</label>
                    <input type="email" class="form-control" id="notificationEmail" value="<EMAIL>">
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6">
                  <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="notifyOnSuccess" checked>
                    <label class="form-check-label" for="notifyOnSuccess">
                      备份成功时通知
                    </label>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="notifyOnFailure" checked>
                    <label class="form-check-label" for="notifyOnFailure">
                      备份失败时通知
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" onclick="saveConfig()">
              <i class="fas fa-save me-1"></i>保存配置
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 恢复确认模态框 -->
    <div class="modal fade" id="restoreModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header bg-warning text-dark">
            <h5 class="modal-title">
              <i class="fas fa-exclamation-triangle me-2"></i>确认数据恢复
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div class="alert alert-warning">
              <strong>警告：</strong>数据恢复操作将覆盖当前数据库的所有数据，此操作不可撤销！
            </div>
            <p>备份文件：<span id="restoreBackupName" class="fw-bold"></span></p>
            <div class="mb-3">
              <label for="confirmPassword" class="form-label">请输入管理员密码确认：</label>
              <input type="password" class="form-control" id="confirmPassword" placeholder="输入密码...">
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-danger" onclick="confirmRestore()">
              <i class="fas fa-undo me-1"></i>确认恢复
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
      let currentPage = 1;
      let currentRestoreId = null;
      const pageSize = 10;

      // 页面加载时初始化
      document.addEventListener('DOMContentLoaded', function () {
        loadBackupStats();
        loadBackups();
        loadConfig();
      });

      // 加载备份统计
      function loadBackupStats() {
        fetch('/api/backup/stats')
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              const stats = data.data;
              document.getElementById('totalBackups').textContent = stats.total;
              document.getElementById('completedBackups').textContent = stats.completed;
              document.getElementById('totalSize').textContent = stats.totalSize;
              document.getElementById('lastBackup').textContent = stats.lastBackup ?
                new Date(stats.lastBackup).toLocaleDateString() : '无';
            }
          })
          .catch(error => {
            console.error('加载统计失败:', error);
          });
      }

      // 加载备份列表
      function loadBackups(page = 1) {
        currentPage = page;

        const type = document.getElementById('typeFilter').value;
        const status = document.getElementById('statusFilter').value;

        const params = new URLSearchParams({
          page: page,
          limit: pageSize
        });

        if (type) params.append('type', type);
        if (status) params.append('status', status);

        fetch(`/api/backup/list?${params}`)
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              renderBackups(data.data.backups);
              renderPagination(data.data.total, data.data.page, data.data.limit);
            } else {
              showAlert('加载失败: ' + data.message, 'danger');
            }
          })
          .catch(error => {
            console.error('加载备份失败:', error);
            showAlert('网络错误，请稍后重试', 'danger');
          });
      }

      // 渲染备份列表
      function renderBackups(backups) {
        const container = document.getElementById('backupsList');

        if (backups.length === 0) {
          container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-hdd fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无备份数据</p>
                    </div>
                `;
          return;
        }

        const html = backups.map(backup => `
                <div class="backup-card card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-2">
                                    <i class="fas fa-file-archive me-2"></i>
                                    ${backup.name}
                                    <span class="badge ${backup.type === 'auto' ? 'bg-primary' : 'bg-secondary'} ms-2">
                                        ${backup.type === 'auto' ? '自动' : '手动'}
                                    </span>
                                </h6>
                                <div class="row text-muted small mb-2">
                                    <div class="col-md-3">
                                        <i class="fas fa-calendar me-1"></i>
                                        ${new Date(backup.startTime).toLocaleString()}
                                    </div>
                                    <div class="col-md-2">
                                        <i class="fas fa-database me-1"></i>
                                        ${backup.size}
                                    </div>
                                    <div class="col-md-2">
                                        <i class="fas fa-clock me-1"></i>
                                        ${backup.duration || '进行中...'}
                                    </div>
                                    <div class="col-md-2">
                                        <span class="backup-status ${backup.status}">
                                            <i class="fas ${getStatusIcon(backup.status)} me-1"></i>
                                            ${getStatusLabel(backup.status)}
                                        </span>
                                    </div>
                                    <div class="col-md-3">
                                        <i class="fas fa-user me-1"></i>
                                        ${backup.createdBy}
                                    </div>
                                </div>
                                ${backup.description ? `<p class="card-text small text-muted">${backup.description}</p>` : ''}
                                ${backup.status === 'running' ? `
                                    <div class="progress-wrapper">
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                                 role="progressbar" style="width: 65%"></div>
                                        </div>
                                        <small class="text-muted">备份进行中...</small>
                                    </div>
                                ` : ''}
                            </div>
                            <div class="btn-group">
                                ${backup.status === 'completed' ? `
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadBackup(${backup.id})" title="下载">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-warning" onclick="showRestoreModal(${backup.id}, '${backup.name}')" title="恢复">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                ` : ''}
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteBackup(${backup.id})" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

        container.innerHTML = html;
      }

      // 渲染分页
      function renderPagination(total, page, limit) {
        const totalPages = Math.ceil(total / limit);
        const pagination = document.getElementById('pagination');

        if (totalPages <= 1) {
          pagination.innerHTML = '';
          return;
        }

        let html = '';

        // 上一页
        html += `
                <li class="page-item ${page <= 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="loadBackups(${page - 1})">上一页</a>
                </li>
            `;

        // 页码
        for (let i = Math.max(1, page - 2); i <= Math.min(totalPages, page + 2); i++) {
          html += `
                    <li class="page-item ${i === page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadBackups(${i})">${i}</a>
                    </li>
                `;
        }

        // 下一页
        html += `
                <li class="page-item ${page >= totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="loadBackups(${page + 1})">下一页</a>
                </li>
            `;

        pagination.innerHTML = html;
      }

      // 创建备份
      function createBackup() {
        if (!confirm('确定要创建新的数据库备份吗？')) {
          return;
        }

        fetch('/api/backup/create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            description: '手动创建的备份'
          })
        })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              showAlert('备份任务已启动', 'success');
              loadBackups();
              loadBackupStats();
            } else {
              showAlert('创建备份失败: ' + data.message, 'danger');
            }
          })
          .catch(error => {
            console.error('创建备份失败:', error);
            showAlert('网络错误，请稍后重试', 'danger');
          });
      }

      // 下载备份
      function downloadBackup(id) {
        window.open(`/api/backup/${id}/download`, '_blank');
      }

      // 显示恢复模态框
      function showRestoreModal(id, name) {
        currentRestoreId = id;
        document.getElementById('restoreBackupName').textContent = name;
        document.getElementById('confirmPassword').value = '';
        new bootstrap.Modal(document.getElementById('restoreModal')).show();
      }

      // 确认恢复
      function confirmRestore() {
        const password = document.getElementById('confirmPassword').value;

        if (!password) {
          showAlert('请输入确认密码', 'warning');
          return;
        }

        fetch(`/api/backup/${currentRestoreId}/restore`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            confirmPassword: password
          })
        })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              showAlert('数据库恢复成功', 'success');
              bootstrap.Modal.getInstance(document.getElementById('restoreModal')).hide();
            } else {
              showAlert('恢复失败: ' + data.message, 'danger');
            }
          })
          .catch(error => {
            console.error('恢复失败:', error);
            showAlert('网络错误，请稍后重试', 'danger');
          });
      }

      // 删除备份
      function deleteBackup(id) {
        if (!confirm('确定要删除这个备份吗？此操作不可撤销。')) {
          return;
        }

        fetch(`/api/backup/${id}`, {
          method: 'DELETE'
        })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              showAlert('备份删除成功', 'success');
              loadBackups();
              loadBackupStats();
            } else {
              showAlert('删除失败: ' + data.message, 'danger');
            }
          })
          .catch(error => {
            console.error('删除备份失败:', error);
            showAlert('网络错误，请稍后重试', 'danger');
          });
      }

      // 加载配置
      function loadConfig() {
        fetch('/api/backup/config')
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              const config = data.data;
              document.getElementById('autoBackupEnabled').checked = config.autoBackup.enabled;
              document.getElementById('backupSchedule').value = config.autoBackup.schedule;
              document.getElementById('retentionDays').value = config.autoBackup.retention;
              document.getElementById('compressBackup').checked = config.autoBackup.compress;
              document.getElementById('notificationEnabled').checked = config.notification.enabled;
              document.getElementById('notificationEmail').value = config.notification.email;
              document.getElementById('notifyOnSuccess').checked = config.notification.onSuccess;
              document.getElementById('notifyOnFailure').checked = config.notification.onFailure;
            }
          })
          .catch(error => {
            console.error('加载配置失败:', error);
          });
      }

      // 保存配置
      function saveConfig() {
        const config = {
          autoBackup: {
            enabled: document.getElementById('autoBackupEnabled').checked,
            schedule: document.getElementById('backupSchedule').value,
            retention: parseInt(document.getElementById('retentionDays').value),
            compress: document.getElementById('compressBackup').checked
          },
          notification: {
            enabled: document.getElementById('notificationEnabled').checked,
            email: document.getElementById('notificationEmail').value,
            onSuccess: document.getElementById('notifyOnSuccess').checked,
            onFailure: document.getElementById('notifyOnFailure').checked
          }
        };

        fetch('/api/backup/config', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(config)
        })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              showAlert('配置保存成功', 'success');
              bootstrap.Modal.getInstance(document.getElementById('configModal')).hide();
            } else {
              showAlert('保存失败: ' + data.message, 'danger');
            }
          })
          .catch(error => {
            console.error('保存配置失败:', error);
            showAlert('网络错误，请稍后重试', 'danger');
          });
      }

      // 工具函数
      function getStatusIcon(status) {
        const icons = {
          'completed': 'fa-check-circle',
          'failed': 'fa-times-circle',
          'running': 'fa-spinner fa-spin'
        };
        return icons[status] || 'fa-question-circle';
      }

      function getStatusLabel(status) {
        const labels = {
          'completed': '已完成',
          'failed': '失败',
          'running': '进行中'
        };
        return labels[status] || status;
      }

      function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

        document.querySelector('main').insertBefore(alertDiv, document.querySelector('main').firstChild);

        setTimeout(() => {
          alertDiv.remove();
        }, 5000);
      }
    </script>
</body>

</html>