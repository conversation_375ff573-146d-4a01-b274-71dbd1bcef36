<!-- 用户管理页面 -->
<div class="enterprise-page-container">
    <!-- 页面头部 -->
    <div class="page-header-modern">
        <div class="page-header-content">
            <div class="page-title-section">
                <h1 class="page-title-modern">
                    <i class="bi bi-people-fill"></i>
                    用户管理
                </h1>
                <p class="page-subtitle-modern">管理系统用户、角色权限和用户组织架构</p>
                <div class="page-breadcrumb">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/dashboard">首页</a></li>
                            <li class="breadcrumb-item active">用户管理</li>
                        </ol>
                    </nav>
                </div>
            </div>
            <div class="page-actions-modern">
                <button type="button" class="btn btn-outline-primary" onclick="exportUsers()">
                    <i class="bi bi-download"></i>
                    导出用户
                </button>
                <button type="button" class="btn btn-primary" onclick="showCreateUserModal()">
                    <i class="bi bi-plus-lg"></i>
                    新增用户
                </button>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards-container">
        <div class="row g-3">
            <div class="col-xl-3 col-md-6">
                <div class="stats-card primary">
                    <div class="stats-card-body">
                        <div class="stats-icon">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" id="totalUsers">0</div>
                            <div class="stats-label">总用户数</div>
                            <div class="stats-change positive">
                                <i class="bi bi-arrow-up"></i> +12 本月
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card success">
                    <div class="stats-card-body">
                        <div class="stats-icon">
                            <i class="bi bi-person-check"></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" id="activeUsers">0</div>
                            <div class="stats-label">活跃用户</div>
                            <div class="stats-change positive">
                                <i class="bi bi-arrow-up"></i> +8.5% 昨日
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card warning">
                    <div class="stats-card-body">
                        <div class="stats-icon">
                            <i class="bi bi-shield-check"></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" id="adminUsers">0</div>
                            <div class="stats-label">管理员</div>
                            <div class="stats-change neutral">
                                <i class="bi bi-dash"></i> 无变化
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card info">
                    <div class="stats-card-body">
                        <div class="stats-icon">
                            <i class="bi bi-clock-history"></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-number" id="onlineUsers">0</div>
                            <div class="stats-label">在线用户</div>
                            <div class="stats-change positive">
                                <i class="bi bi-arrow-up"></i> 实时更新
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-toolbar-modern">
        <div class="filter-section">
            <div class="search-box-modern">
                <i class="bi bi-search"></i>
                <input type="text" id="searchInput" placeholder="搜索用户名、邮箱、姓名、手机号..." class="form-control">
                <button type="button" class="search-clear" onclick="clearSearch()" style="display: none;">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="filter-group-modern">
                <div class="filter-item">
                    <label>角色</label>
                    <select id="roleFilter" class="form-select">
                        <option value="">全部角色</option>
                        <option value="admin">管理员</option>
                        <option value="manager">经理</option>
                        <option value="user">普通用户</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>状态</label>
                    <select id="statusFilter" class="form-select">
                        <option value="">全部状态</option>
                        <option value="active">活跃</option>
                        <option value="inactive">非活跃</option>
                        <option value="suspended">已暂停</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>创建时间</label>
                    <select id="dateFilter" class="form-select">
                        <option value="">全部时间</option>
                        <option value="today">今天</option>
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                        <option value="quarter">本季度</option>
                    </select>
                </div>
                <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                    <i class="bi bi-arrow-clockwise"></i>
                    重置
                </button>
            </div>
        </div>

        <!-- 批量操作工具栏 -->
        <div class="batch-actions-modern" id="batchActions" style="display: none;">
            <div class="batch-info">
                <span class="selected-count">已选择 <span id="selectedCount">0</span> 项</span>
                <button type="button" class="btn-link" onclick="clearSelection()">清除选择</button>
            </div>
            <div class="batch-buttons">
                <button type="button" class="btn btn-outline-primary" onclick="batchExport()">
                    <i class="bi bi-download"></i>
                    导出选中
                </button>
                <button type="button" class="btn btn-outline-warning" onclick="batchUpdateStatus()">
                    <i class="bi bi-pencil-square"></i>
                    批量修改状态
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="batchDeleteUsers()">
                    <i class="bi bi-trash"></i>
                    批量删除
                </button>
            </div>
        </div>
    </div>

    <!-- 数据表格 -->
    <div class="data-table-modern">
        <div class="table-header">
            <div class="table-title">
                <h5>用户列表</h5>
                <span class="table-count">共 <span id="totalCount">0</span> 条记录</span>
            </div>
            <div class="table-tools">
                <div class="view-toggle">
                    <button type="button" class="btn btn-sm btn-outline-secondary active" data-view="table">
                        <i class="bi bi-table"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" data-view="card">
                        <i class="bi bi-grid-3x3-gap"></i>
                    </button>
                </div>
                <div class="table-settings">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="showColumnSettings()">
                        <i class="bi bi-gear"></i>
                        列设置
                    </button>
                </div>
            </div>
        </div>

        <div class="table-container-modern">
            <div class="table-responsive">
                <table class="table table-hover enterprise-table" id="usersTable">
                    <thead>
                        <tr>
                            <th width="50">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th width="80">头像</th>
                            <th class="sortable" data-sort="username">
                                用户名
                                <i class="bi bi-arrow-down-up sort-icon"></i>
                            </th>
                            <th class="sortable" data-sort="email">
                                邮箱
                                <i class="bi bi-arrow-down-up sort-icon"></i>
                            </th>
                            <th>真实姓名</th>
                            <th>手机号</th>
                            <th>养殖场</th>
                            <th>角色</th>
                            <th>状态</th>
                            <th class="sortable" data-sort="lastLoginAt">
                                最后登录
                                <i class="bi bi-arrow-down-up sort-icon"></i>
                            </th>
                            <th class="sortable" data-sort="createdAt">
                                创建时间
                                <i class="bi bi-arrow-down-up sort-icon"></i>
                            </th>
                            <th width="140">操作</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 加载状态 -->
            <div class="loading-state-modern" id="loadingState">
                <div class="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
                <p>正在加载用户数据...</p>
            </div>

            <!-- 空状态 -->
            <div class="empty-state-modern" id="emptyState" style="display: none;">
                <div class="empty-icon">
                    <i class="bi bi-people"></i>
                </div>
                <h3>暂无用户数据</h3>
                <p>还没有任何用户，点击上方按钮创建第一个用户</p>
                <button type="button" class="btn btn-primary" onclick="showCreateUserModal()">
                    <i class="bi bi-plus-lg"></i>
                    创建用户
                </button>
            </div>

            <!-- 错误状态 -->
            <div class="error-state-modern" id="errorState" style="display: none;">
                <div class="error-icon">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <h3>加载失败</h3>
                <p id="errorMessage">数据加载失败，请稍后重试</p>
                <button type="button" class="btn btn-outline-primary" onclick="loadUsers()">
                    <i class="bi bi-arrow-clockwise"></i>
                    重新加载
                </button>
            </div>
        </div>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container-modern">
        <div class="pagination-info">
            <span id="paginationInfo">显示第 1-10 条，共 0 条记录</span>
        </div>
        <nav aria-label="用户列表分页">
            <ul class="pagination pagination-modern" id="pagination">
                <!-- 分页按钮将通过JavaScript动态生成 -->
            </ul>
        </nav>
        <div class="page-size-selector">
            <label>每页显示</label>
            <select id="pageSizeSelect" class="form-select">
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
            <label>条</label>
        </div>
    </div>
</div>

<!-- 创建/编辑用户模态框 -->
<div class="modal fade" id="userModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content modern-modal">
            <div class="modal-header">
                <h5 class="modal-title" id="userModalTitle">
                    <i class="bi bi-person-plus"></i>
                    新增用户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="userForm" novalidate>
                    <input type="hidden" id="userId" name="id">

                    <!-- 基本信息 -->
                    <div class="form-section">
                        <h6 class="form-section-title">基本信息</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        用户名 <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                    <div class="invalid-feedback">请输入用户名</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        邮箱 <span class="text-danger">*</span>
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                    <div class="invalid-feedback">请输入有效的邮箱地址</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="realName" class="form-label">真实姓名</label>
                                    <input type="text" class="form-control" id="realName" name="name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">手机号</label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                    <div class="invalid-feedback">请输入有效的手机号</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="farmName" class="form-label">养殖场名称</label>
                                    <input type="text" class="form-control" id="farmName" name="farmName">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 账户设置 -->
                    <div class="form-section">
                        <h6 class="form-section-title">账户设置</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        密码 <span class="text-danger" id="passwordRequired">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" name="password">
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                            <i class="bi bi-eye" id="passwordToggleIcon"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">密码至少8位，包含字母和数字</div>
                                    <div class="invalid-feedback">请输入符合要求的密码</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">
                                        角色 <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="role" name="role" required>
                                        <option value="">请选择角色</option>
                                        <option value="admin">管理员</option>
                                        <option value="manager">经理</option>
                                        <option value="user">普通用户</option>
                                    </select>
                                    <div class="invalid-feedback">请选择用户角色</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">状态</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active">活跃</option>
                                        <option value="inactive">非活跃</option>
                                        <option value="suspended">已暂停</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveUser()">
                    <span class="btn-loading" style="display: none;">
                        <span class="spinner-border spinner-border-sm me-2"></span>
                        保存中...
                    </span>
                    <span class="btn-text">保存</span>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* 企业级用户管理页面样式 */
.enterprise-page-container {
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 24px;
    color: white;
    position: relative;
    overflow: hidden;
}

.page-header-modern::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.page-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
    z-index: 2;
}

.page-title-modern {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 16px;
}

.page-subtitle-modern {
    margin: 8px 0 16px 0;
    opacity: 0.9;
    font-size: 1rem;
}

.page-breadcrumb {
    margin-top: 8px;
}

.page-breadcrumb .breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.page-breadcrumb .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.page-breadcrumb .breadcrumb-item.active {
    color: white;
}

.page-actions-modern {
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.page-actions-modern .btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 20px;
}

.page-actions-modern .btn-outline-primary {
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.page-actions-modern .btn-outline-primary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.page-actions-modern .btn-primary {
    background: white;
    color: #667eea;
    border: none;
}

.page-actions-modern .btn-primary:hover {
    background: #f8f9ff;
    color: #5a67d8;
}
</style>

<script src="/js/users-enterprise.js"></script>
` }) %>
