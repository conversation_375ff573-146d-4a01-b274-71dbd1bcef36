<!-- 欢迎横幅 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="welcome-banner">
            <div class="welcome-content">
                <div class="welcome-text">
                    <h2 class="welcome-title">欢迎回来，<%= user.username %>！</h2>
                    <p class="welcome-subtitle">今天是 <%= new Date().toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' }) %></p>
                </div>
                <div class="welcome-stats">
                    <div class="stat-item">
                        <div class="stat-value">1,248</div>
                        <div class="stat-label">鹅群总数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">98.5%</div>
                        <div class="stat-label">健康率</div>
                    </div>
                </div>
            </div>
            <div class="welcome-decoration">
                <i class="bi bi-egg-fried"></i>
            </div>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="modern-stat-card primary">
            <div class="stat-icon">
                <i class="bi bi-egg-fried"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">1,248</div>
                <div class="stat-label">鹅群总数</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i> +12 本周
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="modern-stat-card success">
            <div class="stat-icon">
                <i class="bi bi-clipboard-data"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">3,456</div>
                <div class="stat-label">今日产蛋</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i> +8.5% 昨日
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="modern-stat-card warning">
            <div class="stat-icon">
                <i class="bi bi-heart-pulse"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">98.5%</div>
                <div class="stat-label">健康率</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i> +0.3% 本月
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="modern-stat-card info">
            <div class="stat-icon">
                <i class="bi bi-currency-yen"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">¥45,678</div>
                <div class="stat-label">月收入</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i> +15.2% 上月
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表和活动 -->
<div class="row mb-4">
    <!-- 生产趋势图表 -->
    <div class="col-xl-8 col-lg-7 mb-4">
        <div class="modern-chart-card">
            <div class="chart-header">
                <div class="chart-title-section">
                    <h5 class="chart-title">生产趋势分析</h5>
                    <p class="chart-subtitle">最近30天的产蛋量变化</p>
                </div>
                <div class="chart-controls">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary active">30天</button>
                        <button type="button" class="btn btn-sm btn-outline-primary">7天</button>
                        <button type="button" class="btn btn-sm btn-outline-primary">今日</button>
                    </div>
                </div>
            </div>
            <div class="chart-body">
                <canvas id="productionChart"></canvas>
            </div>
        </div>
    </div>

    <!-- 实时监控 -->
    <div class="col-xl-4 col-lg-5 mb-4">
        <div class="modern-chart-card">
            <div class="chart-header">
                <h5 class="chart-title">实时监控</h5>
                <div class="status-indicator online">
                    <span class="indicator-dot"></span>
                    系统正常
                </div>
            </div>
            <div class="chart-body">
                <div class="monitoring-grid">
                    <div class="monitor-item">
                        <div class="monitor-icon temperature">
                            <i class="bi bi-thermometer-half"></i>
                        </div>
                        <div class="monitor-info">
                            <div class="monitor-label">环境温度</div>
                            <div class="monitor-value">24.5°C</div>
                        </div>
                    </div>
                    <div class="monitor-item">
                        <div class="monitor-icon humidity">
                            <i class="bi bi-droplet"></i>
                        </div>
                        <div class="monitor-info">
                            <div class="monitor-label">湿度</div>
                            <div class="monitor-value">65%</div>
                        </div>
                    </div>
                    <div class="monitor-item">
                        <div class="monitor-icon feed">
                            <i class="bi bi-cup-straw"></i>
                        </div>
                        <div class="monitor-info">
                            <div class="monitor-label">饲料余量</div>
                            <div class="monitor-value">78%</div>
                        </div>
                    </div>
                    <div class="monitor-item">
                        <div class="monitor-icon water">
                            <i class="bi bi-droplet-fill"></i>
                        </div>
                        <div class="monitor-info">
                            <div class="monitor-label">水源状态</div>
                            <div class="monitor-value">正常</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 健康状态和最近活动 -->
<div class="row mb-4">
    <!-- 健康状态分布 -->
    <div class="col-lg-6 mb-4">
        <div class="modern-chart-card">
            <div class="chart-header">
                <h5 class="chart-title">健康状态分布</h5>
                <div class="health-summary">
                    <span class="health-total">1,248只鹅</span>
                </div>
            </div>
            <div class="chart-body">
                <div class="health-chart-container">
                    <canvas id="healthChart"></canvas>
                    <div class="health-center-info">
                        <div class="health-percentage">98.5%</div>
                        <div class="health-label">健康率</div>
                    </div>
                </div>
                <div class="health-legend">
                    <div class="legend-item">
                        <div class="legend-color healthy"></div>
                        <span class="legend-label">健康 (1,229)</span>
                        <span class="legend-percentage">98.5%</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color warning"></div>
                        <span class="legend-label">警告 (15)</span>
                        <span class="legend-percentage">1.2%</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color critical"></div>
                        <span class="legend-label">严重 (4)</span>
                        <span class="legend-percentage">0.3%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近活动 -->
    <div class="col-lg-6 mb-4">
        <div class="modern-chart-card">
            <div class="chart-header">
                <h5 class="chart-title">最近活动</h5>
                <a href="/activities" class="view-all-link">查看全部</a>
            </div>
            <div class="chart-body">
                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-icon success">
                            <i class="bi bi-plus-circle"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">新增生产记录</div>
                            <div class="activity-description">A区产蛋量：456个</div>
                            <div class="activity-time">2分钟前</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon warning">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">健康预警</div>
                            <div class="activity-description">B区发现3只鹅体温异常</div>
                            <div class="activity-time">15分钟前</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon info">
                            <i class="bi bi-gear"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">系统维护</div>
                            <div class="activity-description">自动备份数据完成</div>
                            <div class="activity-time">1小时前</div>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon primary">
                            <i class="bi bi-person-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">新用户注册</div>
                            <div class="activity-description">饲养员张三加入系统</div>
                            <div class="activity-time">2小时前</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="quick-actions-card">
            <div class="quick-actions-header">
                <h5 class="quick-actions-title">快速操作</h5>
                <p class="quick-actions-subtitle">常用功能快速入口</p>
            </div>
            <div class="quick-actions-grid">
                <a href="/production/create" class="quick-action-item">
                    <div class="quick-action-icon primary">
                        <i class="bi bi-plus-circle"></i>
                    </div>
                    <div class="quick-action-content">
                        <div class="quick-action-title">添加生产记录</div>
                        <div class="quick-action-desc">记录今日产蛋情况</div>
                    </div>
                </a>
                <a href="/health/create" class="quick-action-item">
                    <div class="quick-action-icon success">
                        <i class="bi bi-heart-pulse"></i>
                    </div>
                    <div class="quick-action-content">
                        <div class="quick-action-title">健康检查</div>
                        <div class="quick-action-desc">记录鹅群健康状态</div>
                    </div>
                </a>
                <a href="/users/create" class="quick-action-item">
                    <div class="quick-action-icon info">
                        <i class="bi bi-person-plus"></i>
                    </div>
                    <div class="quick-action-content">
                        <div class="quick-action-title">添加用户</div>
                        <div class="quick-action-desc">新增系统用户</div>
                    </div>
                </a>
                <a href="/settings" class="quick-action-item">
                    <div class="quick-action-icon warning">
                        <i class="bi bi-gear"></i>
                    </div>
                    <div class="quick-action-content">
                        <div class="quick-action-title">系统设置</div>
                        <div class="quick-action-desc">配置系统参数</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<style>
/* 现代化样式 */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --card-hover-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    --border-radius: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 欢迎横幅 */
.welcome-banner {
    background: var(--primary-gradient);
    border-radius: var(--border-radius);
    padding: 2rem;
    color: white;
    position: relative;
    overflow: hidden;
    margin-bottom: 2rem;
}

.welcome-banner::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.welcome-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.welcome-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.welcome-subtitle {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
}

.welcome-stats {
    display: flex;
    gap: 2rem;
}

.welcome-stats .stat-item {
    text-align: center;
}

.welcome-stats .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
}

.welcome-stats .stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
}

.welcome-decoration {
    position: absolute;
    right: 2rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 4rem;
    opacity: 0.2;
}

/* 现代化统计卡片 */
.modern-stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modern-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-hover-shadow);
}

.modern-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.modern-stat-card.primary::before {
    background: var(--primary-gradient);
}

.modern-stat-card.success::before {
    background: var(--success-gradient);
}

.modern-stat-card.warning::before {
    background: var(--warning-gradient);
}

.modern-stat-card.info::before {
    background: var(--info-gradient);
}

.modern-stat-card .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.modern-stat-card.primary .stat-icon {
    background: var(--primary-gradient);
}

.modern-stat-card.success .stat-icon {
    background: var(--success-gradient);
}

.modern-stat-card.warning .stat-icon {
    background: var(--warning-gradient);
}

.modern-stat-card.info .stat-icon {
    background: var(--info-gradient);
}

.modern-stat-card .stat-content {
    flex: 1;
}

.modern-stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.modern-stat-card .stat-label {
    color: #718096;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.modern-stat-card .stat-change {
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.modern-stat-card .stat-change.positive {
    color: #38a169;
}

.modern-stat-card .stat-change.negative {
    color: #e53e3e;
}

/* 现代化图表卡片 */
.modern-chart-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.modern-chart-card:hover {
    box-shadow: var(--card-hover-shadow);
}

.chart-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.chart-subtitle {
    color: #718096;
    font-size: 0.875rem;
    margin: 0.25rem 0 0 0;
}

.chart-body {
    padding: 1.5rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-indicator.online {
    color: #38a169;
}

.indicator-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}
</style>

/* 监控网格 */
.monitoring-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.monitor-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 12px;
    transition: var(--transition);
}

.monitor-item:hover {
    background: #edf2f7;
}

.monitor-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
}

.monitor-icon.temperature {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.monitor-icon.humidity {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.monitor-icon.feed {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
}

.monitor-icon.water {
    background: linear-gradient(135deg, #81ecec 0%, #00b894 100%);
}

.monitor-info {
    flex: 1;
}

.monitor-label {
    font-size: 0.875rem;
    color: #718096;
    margin-bottom: 0.25rem;
}

.monitor-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: #2d3748;
}

/* 健康图表 */
.health-chart-container {
    position: relative;
    height: 200px;
    margin-bottom: 1.5rem;
}

.health-center-info {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.health-percentage {
    font-size: 1.5rem;
    font-weight: 700;
    color: #38a169;
}

.health-label {
    font-size: 0.875rem;
    color: #718096;
}

.health-legend {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.legend-color.healthy {
    background: #38a169;
}

.legend-color.warning {
    background: #d69e2e;
}

.legend-color.critical {
    background: #e53e3e;
}

.legend-label {
    flex: 1;
    font-weight: 500;
    color: #2d3748;
}

.legend-percentage {
    font-weight: 600;
    color: #718096;
}

/* 活动列表 */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 12px;
    transition: var(--transition);
}

.activity-item:hover {
    background: #edf2f7;
}

.activity-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.activity-icon.success {
    background: #38a169;
}

.activity-icon.warning {
    background: #d69e2e;
}

.activity-icon.info {
    background: #3182ce;
}

.activity-icon.primary {
    background: #667eea;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.activity-description {
    font-size: 0.875rem;
    color: #718096;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.75rem;
    color: #a0aec0;
}

.view-all-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
}

.view-all-link:hover {
    color: #5a67d8;
}

/* 快速操作 */
.quick-actions-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
}

.quick-actions-header {
    margin-bottom: 1.5rem;
}

.quick-actions-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.quick-actions-subtitle {
    color: #718096;
    font-size: 0.875rem;
    margin: 0.25rem 0 0 0;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.quick-action-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem;
    background: #f7fafc;
    border-radius: 12px;
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

.quick-action-item:hover {
    background: #edf2f7;
    transform: translateY(-2px);
    color: inherit;
    text-decoration: none;
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.quick-action-icon.primary {
    background: var(--primary-gradient);
}

.quick-action-icon.success {
    background: var(--success-gradient);
}

.quick-action-icon.info {
    background: var(--info-gradient);
}

.quick-action-icon.warning {
    background: var(--warning-gradient);
}

.quick-action-content {
    flex: 1;
}

.quick-action-title {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.quick-action-desc {
    font-size: 0.875rem;
    color: #718096;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .welcome-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .welcome-stats {
        gap: 1rem;
    }

    .monitoring-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}

<script>
// 模拟数据
const mockData = {
    productionTrend: [
        { label: '1月', eggs: 2800 },
        { label: '2月', eggs: 3200 },
        { label: '3月', eggs: 3600 },
        { label: '4月', eggs: 3400 },
        { label: '5月', eggs: 3800 },
        { label: '6月', eggs: 4200 },
        { label: '7月', eggs: 3900 }
    ],
    healthStatus: {
        healthy: 1229,
        warning: 15,
        critical: 4
    }
};

// 生产趋势图表
const productionCtx = document.getElementById('productionChart').getContext('2d');
const productionChart = new Chart(productionCtx, {
    type: 'line',
    data: {
        labels: mockData.productionTrend.map(item => item.label),
        datasets: [{
            label: '产蛋数量',
            data: mockData.productionTrend.map(item => item.eggs),
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: '#667eea',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 6,
            pointHoverRadius: 8
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.05)',
                    drawBorder: false
                },
                ticks: {
                    color: '#718096',
                    font: {
                        size: 12
                    }
                }
            },
            x: {
                grid: {
                    display: false
                },
                ticks: {
                    color: '#718096',
                    font: {
                        size: 12
                    }
                }
            }
        },
        interaction: {
            intersect: false,
            mode: 'index'
        }
    }
});

// 健康状态饼图
const healthCtx = document.getElementById('healthChart').getContext('2d');
const healthChart = new Chart(healthCtx, {
    type: 'doughnut',
    data: {
        labels: ['健康', '警告', '严重'],
        datasets: [{
            data: [mockData.healthStatus.healthy, mockData.healthStatus.warning, mockData.healthStatus.critical],
            backgroundColor: ['#38a169', '#d69e2e', '#e53e3e'],
            borderWidth: 0,
            cutout: '70%'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// 图表控制按钮
document.querySelectorAll('.chart-controls .btn').forEach(btn => {
    btn.addEventListener('click', function() {
        document.querySelectorAll('.chart-controls .btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
    });
});

// 数字动画效果
function animateNumbers() {
    const numbers = document.querySelectorAll('.stat-number');
    numbers.forEach(number => {
        const target = parseInt(number.textContent.replace(/[^\d]/g, ''));
        if (target) {
            let current = 0;
            const increment = target / 50;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                number.textContent = number.textContent.replace(/[\d,]+/, Math.floor(current).toLocaleString());
            }, 20);
        }
    });
}

// 页面加载完成后执行动画
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(animateNumbers, 500);
});
</script>
