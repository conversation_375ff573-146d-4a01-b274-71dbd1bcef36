<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>
    <%= title %> - 智慧养鹅管理系统
  </title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
  <style>
    .article-card {
      transition: all 0.3s ease;
      border-radius: 10px;
    }

    .article-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .article-status {
      position: absolute;
      top: 10px;
      right: 10px;
    }

    .article-meta {
      font-size: 0.875rem;
      color: #6c757d;
    }

    .article-excerpt {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .featured-badge {
      position: absolute;
      top: -5px;
      left: -5px;
      z-index: 1;
    }

    .search-filters {
      background: #f8f9fa;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 20px;
    }
  </style>
</head>

<body class="bg-light">
  <div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
          <h1 class="h3 mb-0">
            <i class="bi bi-file-text-fill text-primary me-2"></i>
            文章管理
          </h1>
          <div>
            <a href="/admin/help-center" class="btn btn-outline-secondary me-2">
              <i class="bi bi-arrow-left me-1"></i>返回首页
            </a>
            <a href="/admin/help-center/articles/create" class="btn btn-primary">
              <i class="bi bi-plus-circle me-1"></i>创建文章
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters">
      <form method="GET" action="/admin/help-center/articles">
        <div class="row g-3">
          <div class="col-md-4">
            <label for="keyword" class="form-label">关键词搜索</label>
            <input type="text" class="form-control" id="keyword" name="keyword" value="<%= filters.keyword || '' %>"
              placeholder="搜索标题、摘要或内容...">
          </div>
          <div class="col-md-3">
            <label for="category_id" class="form-label">分类筛选</label>
            <select class="form-select" id="category_id" name="category_id">
              <option value="">全部分类</option>
              <% categories.forEach(category=> { %>
                <option value="<%= category.id %>" <% if (filters.category_id==category.id) { %>selected<% } %>>
                    <%= category.icon %>
                      <%= category.name %>
                </option>
                <% }) %>
            </select>
          </div>
          <div class="col-md-3">
            <label for="status" class="form-label">发布状态</label>
            <select class="form-select" id="status" name="status">
              <option value="">全部状态</option>
              <option value="published" <% if (filters.status==='published' ) { %>selected<% } %>>已发布</option>
              <option value="draft" <% if (filters.status==='draft' ) { %>selected<% } %>>草稿</option>
            </select>
          </div>
          <div class="col-md-2">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary flex-fill">
                <i class="bi bi-search"></i>
              </button>
              <a href="/admin/help-center/articles" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-clockwise"></i>
              </a>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- 文章列表 -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
              <i class="bi bi-list-ul me-2"></i>
              文章列表
            </h5>
            <div class="d-flex gap-2">
              <small class="text-muted">
                共 <%= articles.length %> 篇文章
              </small>
            </div>
          </div>
          <div class="card-body">
            <% if (articles && articles.length> 0) { %>
              <div class="row">
                <% articles.forEach(article=> { %>
                  <div class="col-xl-4 col-lg-6 mb-4">
                    <div class="card article-card h-100 position-relative">
                      <!-- 推荐标记 -->
                      <% if (article.is_featured) { %>
                        <div class="featured-badge">
                          <span class="badge bg-warning">
                            <i class="bi bi-star-fill"></i>
                          </span>
                        </div>
                        <% } %>

                          <!-- 状态标记 -->
                          <div class="article-status">
                            <% if (article.is_published) { %>
                              <span class="badge bg-success">已发布</span>
                              <% } else { %>
                                <span class="badge bg-secondary">草稿</span>
                                <% } %>
                          </div>

                          <div class="card-body">
                            <h5 class="card-title mb-2">
                              <a href="/admin/help-center/articles/<%= article.id %>/edit" class="text-decoration-none">
                                <%= article.title %>
                              </a>
                            </h5>

                            <% if (article.summary) { %>
                              <p class="card-text article-excerpt text-muted">
                                <%= article.summary %>
                              </p>
                              <% } %>

                                <div class="article-meta">
                                  <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-light text-dark">
                                      <%= article.category_icon %>
                                        <%= article.category_name || '未分类' %>
                                    </span>
                                    <small>
                                      <i class="bi bi-eye me-1"></i>
                                      <%= article.view_count || 0 %>
                                    </small>
                                  </div>

                                  <div class="d-flex justify-content-between align-items-center">
                                    <small>
                                      <i class="bi bi-calendar me-1"></i>
                                      <%= new Date(article.created_at).toLocaleDateString() %>
                                    </small>
                                    <div class="d-flex gap-1">
                                      <small class="text-success">
                                        <i class="bi bi-hand-thumbs-up me-1"></i>
                                        <%= article.helpful_count || 0 %>
                                      </small>
                                      <small class="text-danger">
                                        <i class="bi bi-hand-thumbs-down me-1"></i>
                                        <%= article.unhelpful_count || 0 %>
                                      </small>
                                    </div>
                                  </div>
                                </div>
                          </div>

                          <div class="card-footer bg-transparent">
                            <div class="d-flex gap-2">
                              <a href="/admin/help-center/articles/<%= article.id %>/edit"
                                class="btn btn-sm btn-outline-primary flex-fill">
                                <i class="bi bi-pencil me-1"></i>编辑
                              </a>
                              <button type="button" class="btn btn-sm btn-outline-danger"
                                onclick="deleteArticle(<%= article.id %>, '<%= article.title %>')">
                                <i class="bi bi-trash"></i>
                              </button>
                              <button type="button" class="btn btn-sm btn-outline-secondary"
                                onclick="toggleFeatured(<%= article.id %>, <%= article.is_featured %>)">
                                <i class="bi bi-star<%= article.is_featured ? '-fill' : '' %>"></i>
                              </button>
                            </div>
                          </div>
                    </div>
                  </div>
                  <% }) %>
              </div>

              <!-- 分页 -->
              <% if (hasNextPage || currentPage> 1) { %>
                <nav aria-label="文章分页">
                  <ul class="pagination justify-content-center">
                    <% if (currentPage> 1) { %>
                      <li class="page-item">
                        <a class="page-link"
                          href="?page=<%= currentPage - 1 %>&<%= new URLSearchParams(filters).toString() %>">
                          <i class="bi bi-chevron-left"></i>
                        </a>
                      </li>
                      <% } %>

                        <li class="page-item active">
                          <span class="page-link">
                            <%= currentPage %>
                          </span>
                        </li>

                        <% if (hasNextPage) { %>
                          <li class="page-item">
                            <a class="page-link"
                              href="?page=<%= currentPage + 1 %>&<%= new URLSearchParams(filters).toString() %>">
                              <i class="bi bi-chevron-right"></i>
                            </a>
                          </li>
                          <% } %>
                  </ul>
                </nav>
                <% } %>
                  <% } else { %>
                    <div class="text-center py-5">
                      <i class="bi bi-inbox fs-1 text-muted"></i>
                      <h5 class="text-muted mt-3">暂无文章</h5>
                      <p class="text-muted">点击上方"创建文章"按钮来添加第一篇文章</p>
                      <a href="/admin/help-center/articles/create" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>创建文章
                      </a>
                    </div>
                    <% } %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 删除确认模态框 -->
  <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p>确定要删除文章 "<span id="articleTitle"></span>" 吗？</p>
          <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle me-2"></i>
            删除后无法恢复，请谨慎操作。
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // 删除文章
    function deleteArticle(id, title) {
      document.getElementById('articleTitle').textContent = title;
      const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
      modal.show();

      document.getElementById('confirmDeleteBtn').onclick = function () {
        fetch(`/admin/help-center/api/articles/${id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          }
        })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              location.reload();
            } else {
              alert('删除失败: ' + data.message);
            }
          })
          .catch(error => {
            console.error('Error:', error);
            alert('删除失败，请重试');
          });
        modal.hide();
      };
    }

    // 切换推荐状态
    function toggleFeatured(id, currentStatus) {
      const newStatus = !currentStatus;

      fetch(`/admin/help-center/api/articles/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ is_featured: newStatus })
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            location.reload();
          } else {
            alert('操作失败: ' + data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('操作失败，请重试');
        });
    }
  </script>
</body>

</html>