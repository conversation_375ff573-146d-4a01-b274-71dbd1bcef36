<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>
    <%= title %> - 智慧养鹅管理系统
  </title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
  <style>
    .stats-card {
      background: linear-gradient(45deg, #007bff, #6f42c1);
      color: white;
      border: none;
      border-radius: 15px;
      box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    .stats-card-2 {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
      border: none;
      border-radius: 15px;
      box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .stats-card-3 {
      background: linear-gradient(45deg, #ffc107, #fd7e14);
      color: white;
      border: none;
      border-radius: 15px;
      box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    }

    .stats-card-4 {
      background: linear-gradient(45deg, #dc3545, #e83e8c);
      color: white;
      border: none;
      border-radius: 15px;
      box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }

    .quick-action-btn {
      border-radius: 10px;
      transition: all 0.3s ease;
    }

    .quick-action-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .recent-item {
      border-left: 4px solid #007bff;
      background: #f8f9fa;
      border-radius: 0 8px 8px 0;
    }
  </style>
</head>

<body class="bg-light">
  <div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
          <h1 class="h3 mb-0">
            <i class="bi bi-question-circle-fill text-primary me-2"></i>
            帮助中心管理
          </h1>
          <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
              <li class="breadcrumb-item"><a href="/admin/dashboard">首页</a></li>
              <li class="breadcrumb-item active">帮助中心</li>
            </ol>
          </nav>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
      <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="card-title mb-0">总文章数</h5>
                <h2 class="mb-0">
                  <%= statistics.total_articles %>
                </h2>
              </div>
              <div class="text-end">
                <i class="bi bi-file-text fs-1"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card-2">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="card-title mb-0">总浏览量</h5>
                <h2 class="mb-0">
                  <%= statistics.total_views || 0 %>
                </h2>
              </div>
              <div class="text-end">
                <i class="bi bi-eye fs-1"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card-3">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="card-title mb-0">FAQ数量</h5>
                <h2 class="mb-0">
                  <%= statistics.total_faqs %>
                </h2>
              </div>
              <div class="text-end">
                <i class="bi bi-question-circle fs-1"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card-4">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="card-title mb-0">满意度</h5>
                <h2 class="mb-0">
                  <%= statistics.satisfaction_rate %>%
                </h2>
              </div>
              <div class="text-end">
                <i class="bi bi-heart fs-1"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="bi bi-lightning-fill text-warning me-2"></i>
              快速操作
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-3 mb-2">
                <a href="/admin/help-center/articles/create" class="btn btn-primary quick-action-btn w-100">
                  <i class="bi bi-plus-circle me-2"></i>创建文章
                </a>
              </div>
              <div class="col-md-3 mb-2">
                <a href="/admin/help-center/faqs/create" class="btn btn-success quick-action-btn w-100">
                  <i class="bi bi-plus-circle me-2"></i>创建FAQ
                </a>
              </div>
              <div class="col-md-3 mb-2">
                <a href="/admin/help-center/categories/create" class="btn btn-warning quick-action-btn w-100">
                  <i class="bi bi-plus-circle me-2"></i>创建分类
                </a>
              </div>
              <div class="col-md-3 mb-2">
                <a href="/admin/help-center/analytics" class="btn btn-info quick-action-btn w-100">
                  <i class="bi bi-graph-up me-2"></i>查看统计
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- 最近文章 -->
      <div class="col-md-4 mb-4">
        <div class="card h-100">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
              <i class="bi bi-file-text text-primary me-2"></i>
              最近文章
            </h5>
            <a href="/admin/help-center/articles" class="btn btn-sm btn-outline-primary">查看全部</a>
          </div>
          <div class="card-body">
            <% if (recentArticles && recentArticles.length> 0) { %>
              <% recentArticles.forEach(article=> { %>
                <div class="recent-item p-3 mb-3">
                  <h6 class="mb-1">
                    <a href="/admin/help-center/articles/<%= article.id %>/edit" class="text-decoration-none">
                      <%= article.title %>
                    </a>
                  </h6>
                  <small class="text-muted">
                    <i class="bi bi-eye me-1"></i>
                    <%= article.view_count || 0 %> 次浏览
                      <span class="ms-2">
                        <i class="bi bi-calendar me-1"></i>
                        <%= new Date(article.created_at).toLocaleDateString() %>
                      </span>
                  </small>
                </div>
                <% }) %>
                  <% } else { %>
                    <div class="text-center text-muted py-4">
                      <i class="bi bi-inbox fs-1"></i>
                      <p class="mb-0">暂无文章</p>
                    </div>
                    <% } %>
          </div>
        </div>
      </div>

      <!-- 最近FAQ -->
      <div class="col-md-4 mb-4">
        <div class="card h-100">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
              <i class="bi bi-question-circle text-success me-2"></i>
              最近FAQ
            </h5>
            <a href="/admin/help-center/faqs" class="btn btn-sm btn-outline-success">查看全部</a>
          </div>
          <div class="card-body">
            <% if (recentFAQs && recentFAQs.length> 0) { %>
              <% recentFAQs.forEach(faq=> { %>
                <div class="recent-item p-3 mb-3">
                  <h6 class="mb-1">
                    <a href="/admin/help-center/faqs/<%= faq.id %>/edit" class="text-decoration-none">
                      <%= faq.question %>
                    </a>
                  </h6>
                  <small class="text-muted">
                    <i class="bi bi-hand-thumbs-up me-1"></i>
                    <%= faq.helpful_count || 0 %> 有帮助
                      <span class="ms-2">
                        <i class="bi bi-calendar me-1"></i>
                        <%= new Date(faq.created_at).toLocaleDateString() %>
                      </span>
                  </small>
                </div>
                <% }) %>
                  <% } else { %>
                    <div class="text-center text-muted py-4">
                      <i class="bi bi-inbox fs-1"></i>
                      <p class="mb-0">暂无FAQ</p>
                    </div>
                    <% } %>
          </div>
        </div>
      </div>

      <!-- 最近教程 -->
      <div class="col-md-4 mb-4">
        <div class="card h-100">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
              <i class="bi bi-play-circle text-warning me-2"></i>
              最近教程
            </h5>
            <a href="/admin/help-center/tutorials" class="btn btn-sm btn-outline-warning">查看全部</a>
          </div>
          <div class="card-body">
            <% if (recentTutorials && recentTutorials.length> 0) { %>
              <% recentTutorials.forEach(tutorial=> { %>
                <div class="recent-item p-3 mb-3">
                  <h6 class="mb-1">
                    <a href="/admin/help-center/tutorials/<%= tutorial.id %>/edit" class="text-decoration-none">
                      <%= tutorial.title %>
                    </a>
                  </h6>
                  <small class="text-muted">
                    <i class="bi bi-eye me-1"></i>
                    <%= tutorial.view_count || 0 %> 次观看
                      <span class="ms-2">
                        <i class="bi bi-clock me-1"></i>
                        <%= tutorial.duration || '未设置' %>
                      </span>
                  </small>
                </div>
                <% }) %>
                  <% } else { %>
                    <div class="text-center text-muted py-4">
                      <i class="bi bi-inbox fs-1"></i>
                      <p class="mb-0">暂无教程</p>
                    </div>
                    <% } %>
          </div>
        </div>
      </div>
    </div>

    <!-- 管理导航 -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="bi bi-gear-fill text-secondary me-2"></i>
              管理功能
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-2 col-sm-4 col-6 mb-3">
                <a href="/admin/help-center/categories"
                  class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center">
                  <i class="bi bi-folder fs-3 mb-2"></i>
                  <span>分类管理</span>
                </a>
              </div>
              <div class="col-md-2 col-sm-4 col-6 mb-3">
                <a href="/admin/help-center/articles"
                  class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center">
                  <i class="bi bi-file-text fs-3 mb-2"></i>
                  <span>文章管理</span>
                </a>
              </div>
              <div class="col-md-2 col-sm-4 col-6 mb-3">
                <a href="/admin/help-center/faqs"
                  class="btn btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center">
                  <i class="bi bi-question-circle fs-3 mb-2"></i>
                  <span>FAQ管理</span>
                </a>
              </div>
              <div class="col-md-2 col-sm-4 col-6 mb-3">
                <a href="/admin/help-center/tutorials"
                  class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center">
                  <i class="bi bi-play-circle fs-3 mb-2"></i>
                  <span>教程管理</span>
                </a>
              </div>
              <div class="col-md-2 col-sm-4 col-6 mb-3">
                <a href="/admin/help-center/feedback"
                  class="btn btn-outline-danger w-100 h-100 d-flex flex-column justify-content-center">
                  <i class="bi bi-chat-left-text fs-3 mb-2"></i>
                  <span>反馈管理</span>
                </a>
              </div>
              <div class="col-md-2 col-sm-4 col-6 mb-3">
                <a href="/admin/help-center/settings"
                  class="btn btn-outline-secondary w-100 h-100 d-flex flex-column justify-content-center">
                  <i class="bi bi-gear fs-3 mb-2"></i>
                  <span>设置管理</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>