<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>
    <%= title %> - 智慧养鹅管理系统
  </title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
  <style>
    .emoji-selector {
      display: grid;
      grid-template-columns: repeat(8, 1fr);
      gap: 10px;
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #ddd;
      padding: 15px;
      border-radius: 8px;
      background: #f8f9fa;
    }

    .emoji-item {
      font-size: 1.5rem;
      text-align: center;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      transition: background-color 0.2s;
    }

    .emoji-item:hover {
      background-color: #007bff;
      color: white;
    }

    .emoji-item.selected {
      background-color: #007bff;
      color: white;
    }

    .icon-preview {
      font-size: 3rem;
      text-align: center;
      padding: 20px;
      border: 2px dashed #ddd;
      border-radius: 8px;
      background: #f8f9fa;
    }
  </style>
</head>

<body class="bg-light">
  <div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
          <h1 class="h3 mb-0">
            <i class="bi bi-folder<%= isEdit ? '-open' : '-plus' %> text-primary me-2"></i>
            <%= title %>
          </h1>
          <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
              <li class="breadcrumb-item"><a href="/admin/help-center">帮助中心</a></li>
              <li class="breadcrumb-item"><a href="/admin/help-center/categories">分类管理</a></li>
              <li class="breadcrumb-item active">
                <%= isEdit ? '编辑分类' : '创建分类' %>
              </li>
            </ol>
          </nav>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-lg-8">
        <!-- 分类表单 -->
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="bi bi-form text-primary me-2"></i>
              分类信息
            </h5>
          </div>
          <div class="card-body">
            <form id="categoryForm">
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="name" class="form-label">
                      分类名称 <span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" id="name" name="name"
                      value="<%= category ? category.name : '' %>" required maxlength="100">
                    <div class="form-text">分类的显示名称，建议使用简洁明了的词语</div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="sort_order" class="form-label">排序顺序</label>
                    <input type="number" class="form-control" id="sort_order" name="sort_order"
                      value="<%= category ? category.sort_order : 0 %>" min="0" max="999">
                    <div class="form-text">数字越小越靠前，0为最高优先级</div>
                  </div>
                </div>
              </div>

              <div class="mb-3">
                <label for="description" class="form-label">分类描述</label>
                <textarea class="form-control" id="description" name="description" rows="3"
                  maxlength="500"><%= category ? category.description : '' %></textarea>
                <div class="form-text">简要描述该分类的用途和包含的内容类型</div>
              </div>

              <div class="mb-3">
                <label class="form-label">分类图标</label>
                <div class="row">
                  <div class="col-md-4">
                    <div class="icon-preview" id="iconPreview">
                      <%= category ? category.icon : '📁' %>
                    </div>
                    <input type="hidden" id="icon" name="icon" value="<%= category ? category.icon : '📁' %>">
                  </div>
                  <div class="col-md-8">
                    <div class="form-text mb-2">点击下方图标选择分类图标：</div>
                    <div class="emoji-selector">
                      <div class="emoji-item" data-emoji="📁">📁</div>
                      <div class="emoji-item" data-emoji="👤">👤</div>
                      <div class="emoji-item" data-emoji="🏥">🏥</div>
                      <div class="emoji-item" data-emoji="🏭">🏭</div>
                      <div class="emoji-item" data-emoji="⚙️">⚙️</div>
                      <div class="emoji-item" data-emoji="🔧">🔧</div>
                      <div class="emoji-item" data-emoji="❓">❓</div>
                      <div class="emoji-item" data-emoji="📚">📚</div>
                      <div class="emoji-item" data-emoji="📖">📖</div>
                      <div class="emoji-item" data-emoji="📝">📝</div>
                      <div class="emoji-item" data-emoji="💡">💡</div>
                      <div class="emoji-item" data-emoji="🎯">🎯</div>
                      <div class="emoji-item" data-emoji="🚀">🚀</div>
                      <div class="emoji-item" data-emoji="🔥">🔥</div>
                      <div class="emoji-item" data-emoji="⭐">⭐</div>
                      <div class="emoji-item" data-emoji="🎨">🎨</div>
                      <div class="emoji-item" data-emoji="🌟">🌟</div>
                      <div class="emoji-item" data-emoji="💎">💎</div>
                      <div class="emoji-item" data-emoji="🔐">🔐</div>
                      <div class="emoji-item" data-emoji="📊">📊</div>
                      <div class="emoji-item" data-emoji="📈">📈</div>
                      <div class="emoji-item" data-emoji="💰">💰</div>
                      <div class="emoji-item" data-emoji="🎪">🎪</div>
                      <div class="emoji-item" data-emoji="🎭">🎭</div>
                    </div>
                  </div>
                </div>
              </div>

              <% if (isEdit) { %>
                <div class="mb-3">
                  <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <% if (category &&
                      category.is_active) { %>checked<% } %>>
                      <label class="form-check-label" for="is_active">
                        启用分类
                      </label>
                      <div class="form-text">禁用后，该分类在前端将不会显示</div>
                  </div>
                </div>
                <% } %>

                  <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                      <i class="bi bi-check-circle me-1"></i>
                      <%= isEdit ? '更新分类' : '创建分类' %>
                    </button>
                    <a href="/admin/help-center/categories" class="btn btn-outline-secondary">
                      <i class="bi bi-arrow-left me-1"></i>
                      返回列表
                    </a>
                  </div>
            </form>
          </div>
        </div>
      </div>

      <div class="col-lg-4">
        <!-- 预览和提示 -->
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="bi bi-eye text-info me-2"></i>
              预览效果
            </h5>
          </div>
          <div class="card-body">
            <div class="border rounded p-3 text-center">
              <div class="fs-1 mb-2" id="previewIcon">
                <%= category ? category.icon : '📁' %>
              </div>
              <h6 class="mb-1" id="previewName">
                <%= category ? category.name : '分类名称' %>
              </h6>
              <small class="text-muted" id="previewDesc">
                <%= category ? category.description : '分类描述' %>
              </small>
            </div>
          </div>
        </div>

        <!-- 使用指南 -->
        <div class="card mt-3">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="bi bi-lightbulb text-warning me-2"></i>
              使用指南
            </h5>
          </div>
          <div class="card-body">
            <div class="small">
              <h6>分类命名建议：</h6>
              <ul class="mb-3">
                <li>使用简洁明了的词语</li>
                <li>避免过长的名称</li>
                <li>保持风格一致</li>
              </ul>

              <h6>排序规则：</h6>
              <ul class="mb-3">
                <li>数字越小越靠前</li>
                <li>相同数字按创建时间排序</li>
                <li>建议按重要程度设置</li>
              </ul>

              <h6>图标选择：</h6>
              <ul class="mb-0">
                <li>选择具有代表性的图标</li>
                <li>保持视觉风格统一</li>
                <li>考虑用户理解度</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 图标选择功能
      const emojiItems = document.querySelectorAll('.emoji-item');
      const iconInput = document.getElementById('icon');
      const iconPreview = document.getElementById('iconPreview');
      const previewIcon = document.getElementById('previewIcon');

      // 设置当前选中的图标
      const currentIcon = iconInput.value;
      emojiItems.forEach(item => {
        if (item.dataset.emoji === currentIcon) {
          item.classList.add('selected');
        }

        item.addEventListener('click', function () {
          // 移除所有选中状态
          emojiItems.forEach(i => i.classList.remove('selected'));
          // 添加当前选中状态
          this.classList.add('selected');

          const emoji = this.dataset.emoji;
          iconInput.value = emoji;
          iconPreview.textContent = emoji;
          previewIcon.textContent = emoji;
        });
      });

      // 实时预览
      const nameInput = document.getElementById('name');
      const descInput = document.getElementById('description');
      const previewName = document.getElementById('previewName');
      const previewDesc = document.getElementById('previewDesc');

      nameInput.addEventListener('input', function () {
        previewName.textContent = this.value || '分类名称';
      });

      descInput.addEventListener('input', function () {
        previewDesc.textContent = this.value || '分类描述';
      });

      // 表单提交
      document.getElementById('categoryForm').addEventListener('submit', function (e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = {};

        for (let [key, value] of formData.entries()) {
          if (key === 'is_active') {
            data[key] = true;
          } else {
            data[key] = value;
          }
        }

        // 如果没有勾选is_active，设置为false
        if (!formData.has('is_active')) {
          data.is_active = <% if (!isEdit) { %> true <% } else { %> false <% } %>;
        }

        const url = <%= isEdit ? `'/admin/help-center/api/categories/${category.id}'` : "'/admin/help-center/api/categories'" %>;
        const method = <%= isEdit ? "'PUT'" : "'POST'" %>;

        fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data)
        })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              alert('<%= isEdit ? "分类更新成功" : "分类创建成功" %>');
              window.location.href = '/admin/help-center/categories';
            } else {
              alert('操作失败: ' + data.message);
            }
          })
          .catch(error => {
            console.error('Error:', error);
            alert('操作失败，请重试');
          });
      });
    });
  </script>
</body>

</html>