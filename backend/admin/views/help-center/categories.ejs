<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>
    <%= title %> - 智慧养鹅管理系统
  </title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
  <style>
    .drag-handle {
      cursor: move;
    }

    .category-icon {
      font-size: 2rem;
    }

    .category-card {
      border-radius: 10px;
      transition: all 0.3s ease;
    }

    .category-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
  </style>
</head>

<body class="bg-light">
  <div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
          <h1 class="h3 mb-0">
            <i class="bi bi-folder-fill text-primary me-2"></i>
            分类管理
          </h1>
          <div>
            <a href="/admin/help-center" class="btn btn-outline-secondary me-2">
              <i class="bi bi-arrow-left me-1"></i>返回首页
            </a>
            <a href="/admin/help-center/categories/create" class="btn btn-primary">
              <i class="bi bi-plus-circle me-1"></i>创建分类
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- 分类列表 -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="bi bi-list-ul me-2"></i>
              分类列表
            </h5>
          </div>
          <div class="card-body">
            <% if (categories && categories.length> 0) { %>
              <div class="row" id="categories-container">
                <% categories.forEach((category, index)=> { %>
                  <div class="col-xl-3 col-lg-4 col-md-6 mb-4" data-category-id="<%= category.id %>">
                    <div class="card category-card h-100">
                      <div class="card-body text-center">
                        <div class="drag-handle position-absolute top-0 end-0 p-2">
                          <i class="bi bi-grip-vertical text-muted"></i>
                        </div>

                        <div class="category-icon mb-3">
                          <%= category.icon || '📁' %>
                        </div>

                        <h5 class="card-title mb-2">
                          <%= category.name %>
                        </h5>

                        <% if (category.description) { %>
                          <p class="card-text text-muted small mb-3">
                            <%= category.description %>
                          </p>
                          <% } %>

                            <div class="row text-center mb-3">
                              <div class="col-6">
                                <small class="text-muted">文章</small>
                                <div class="fw-bold text-primary">
                                  <%= category.article_count || 0 %>
                                </div>
                              </div>
                              <div class="col-6">
                                <small class="text-muted">FAQ</small>
                                <div class="fw-bold text-success">
                                  <%= category.faq_count || 0 %>
                                </div>
                              </div>
                            </div>

                            <div class="d-flex gap-2 justify-content-center">
                              <a href="/admin/help-center/categories/<%= category.id %>/edit"
                                class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-pencil"></i>
                              </a>
                              <button type="button" class="btn btn-sm btn-outline-danger"
                                onclick="deleteCategory(<%= category.id %>, '<%= category.name %>')">
                                <i class="bi bi-trash"></i>
                              </button>
                            </div>
                      </div>

                      <div class="card-footer text-center bg-light">
                        <small class="text-muted">
                          排序: <%= category.sort_order || 0 %>
                            <span class="ms-2">
                              状态:
                              <% if (category.is_active) { %>
                                <span class="badge bg-success">启用</span>
                                <% } else { %>
                                  <span class="badge bg-secondary">禁用</span>
                                  <% } %>
                            </span>
                        </small>
                      </div>
                    </div>
                  </div>
                  <% }) %>
              </div>
              <% } else { %>
                <div class="text-center py-5">
                  <i class="bi bi-inbox fs-1 text-muted"></i>
                  <h5 class="text-muted mt-3">暂无分类</h5>
                  <p class="text-muted">点击上方"创建分类"按钮来添加第一个分类</p>
                  <a href="/admin/help-center/categories/create" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>创建分类
                  </a>
                </div>
                <% } %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 删除确认模态框 -->
  <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p>确定要删除分类 "<span id="categoryName"></span>" 吗？</p>
          <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle me-2"></i>
            删除分类后，该分类下的所有文章和FAQ将被转移到"未分类"中。
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
  <script>
    // 初始化拖拽排序
    document.addEventListener('DOMContentLoaded', function () {
      const container = document.getElementById('categories-container');
      if (container) {
        new Sortable(container, {
          animation: 150,
          handle: '.drag-handle',
          onEnd: function (evt) {
            updateCategorySortOrder();
          }
        });
      }
    });

    // 删除分类
    function deleteCategory(id, name) {
      document.getElementById('categoryName').textContent = name;
      const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
      modal.show();

      document.getElementById('confirmDeleteBtn').onclick = function () {
        fetch(`/admin/help-center/api/categories/${id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          }
        })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              location.reload();
            } else {
              alert('删除失败: ' + data.message);
            }
          })
          .catch(error => {
            console.error('Error:', error);
            alert('删除失败，请重试');
          });
        modal.hide();
      };
    }

    // 更新分类排序
    function updateCategorySortOrder() {
      const categories = document.querySelectorAll('[data-category-id]');
      const updates = [];

      categories.forEach((element, index) => {
        const categoryId = element.getAttribute('data-category-id');
        updates.push({
          id: categoryId,
          sort_order: index + 1
        });
      });

      // 批量更新排序
      Promise.all(updates.map(update =>
        fetch(`/admin/help-center/api/categories/${update.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ sort_order: update.sort_order })
        })
      )).then(responses => {
        console.log('排序更新成功');
      }).catch(error => {
        console.error('排序更新失败:', error);
      });
    }
  </script>
</body>

</html>