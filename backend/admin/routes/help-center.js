const express = require('express');
const AdminHelpCenterController = require('../controllers/help-center.controller');

// ======================== 页面路由 ========================

// 帮助中心管理主页
router.get('/', AdminHelpCenterController.index);

// 分类管理
router.get('/categories', AdminHelpCenterController.categories);
router.get('/categories/create', AdminHelpCenterController.createCategoryPage);
router.get('/categories/:id/edit', AdminHelpCenterController.editCategoryPage);

// 文章管理
router.get('/articles', AdminHelpCenterController.articles);
router.get('/articles/create', AdminHelpCenterController.createArticlePage);
router.get('/articles/:id/edit', AdminHelpCenterController.editArticlePage);

// FAQ管理
router.get('/faqs', AdminHelpCenterController.faqs);
router.get('/faqs/create', AdminHelpCenterController.createFAQPage);
router.get('/faqs/:id/edit', AdminHelpCenterController.editFAQPage);

// 教程管理
router.get('/tutorials', AdminHelpCenterController.tutorials);
router.get('/tutorials/create', AdminHelpCenterController.createTutorialPage);
router.get('/tutorials/:id/edit', AdminHelpCenterController.editTutorialPage);

// 反馈管理
router.get('/feedback', AdminHelpCenterController.feedback);

// 设置管理
router.get('/settings', AdminHelpCenterController.settings);

// 统计报告
router.get('/analytics', AdminHelpCenterController.analytics);

// ======================== API路由 ========================

// 分类API
router.post('/api/categories', AdminHelpCenterController.handleCategoryAPI);
router.put('/api/categories/:id', AdminHelpCenterController.handleCategoryAPI);
router.delete('/api/categories/:id', AdminHelpCenterController.handleCategoryAPI);

// 文章API
router.post('/api/articles', AdminHelpCenterController.handleArticleAPI);
router.put('/api/articles/:id', AdminHelpCenterController.handleArticleAPI);
router.delete('/api/articles/:id', AdminHelpCenterController.handleArticleAPI);

// FAQ API
router.post('/api/faqs', AdminHelpCenterController.handleFAQAPI);
router.put('/api/faqs/:id', AdminHelpCenterController.handleFAQAPI);
router.delete('/api/faqs/:id', AdminHelpCenterController.handleFAQAPI);

// 设置API
router.put('/api/settings', AdminHelpCenterController.handleSettingsAPI);

module.exports = router;