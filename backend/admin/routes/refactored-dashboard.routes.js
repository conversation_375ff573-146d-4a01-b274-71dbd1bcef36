/**
 * 重构后的仪表板路由
 * 使用新的中间件和助手函数
 */

const express = require("express");
const { requireAuth, cache } = require("../middleware/common");
const apiService = require("../utils/apiService");

// 应用认证中间件
router.use(requireAuth);

/**
 * 仪表板首页
 */
router.get(
  "/",
  asyncHandler(async (req, res) => {
    // 获取仪表板统计数据，带5分钟缓存
    const dashboardData = await getDashboardData(req.session.token);

    renderPage(res, "dashboard/index", {
      title: "仪表板 - 智慧养鹅管理系统",
      data: dashboardData,
      user: req.session.user,
      currentPage: "dashboard",
    });
  }),
);

/**
 * API路由 - 获取仪表板数据（带缓存）
 */
router.get(
  "/api/dashboard-data",
  cache(300, (req) => `dashboard:${req.session.user.id}`), // 5分钟缓存
  asyncHandler(async (req, res) => {
    const { successResponse } = require("../utils/response-helper");

/**
 * API路由 - 获取生产趋势数据
 */
router.get(
  "/api/production-trend",
  asyncHandler(async (req, res) => {
    const {
      successResponse,
      errorResponse,
    } = require("../utils/response-helper");

    try {
      const { days = 7 } = req.query;
      const productionResult = await apiService.getProductionRecords(
        req.session.token,
        {
          limit: 100,
          days: parseInt(days),
        },
      );

      if (productionResult.success) {
        const records = productionResult.data.records || [];
        const trendData = generateProductionTrend(records, parseInt(days));
        successResponse(res, trendData, "获取生产趋势成功");
      } else {
        errorResponse(res, productionResult.message || "获取生产趋势失败");
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取生产趋势失败:", error); } catch(_) {}

      errorResponse(res, "获取生产趋势失败", 500);
    }
  }),
);

/**
 * API路由 - 获取健康状态统计
 */
router.get(
  "/api/health-stats",
  asyncHandler(async (req, res) => {
    const {
      successResponse,
      errorResponse,
    } = require("../utils/response-helper");

    try {
      const healthResult = await apiService.getHealthRecords(
        req.session.token,
        { limit: 100 },
      );

      if (healthResult.success) {
        const records = healthResult.data || [];
        const healthStats = generateHealthStatus(records);
        successResponse(res, healthStats, "获取健康统计成功");
      } else {
        errorResponse(res, healthResult.message || "获取健康统计失败");
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取健康统计失败:", error); } catch(_) {}

      errorResponse(res, "获取健康统计失败", 500);
    }
  }),
);

/**
 * API路由 - 获取最近活动
 */
router.get(
  "/api/recent-activities",
  asyncHandler(async (req, res) => {
    const { successResponse } = require("../utils/response-helper");

    const activities = generateRecentActivities(parseInt(limit));
    successResponse(res, activities, "获取最近活动成功");
  }),
);

/**
 * API路由 - 刷新仪表板数据
 */
router.post(
  "/api/refresh-dashboard",
  asyncHandler(async (req, res) => {
    const { successResponse } = require("../utils/response-helper");

    // 清除缓存
    const { clearCache } = require("../middleware/common");

    const dashboardData = await getDashboardData(req.session.token);
    successResponse(res, dashboardData, "仪表板数据已刷新");
  }),
);

/**
 * 获取仪表板数据的统一方法
 */
async function getDashboardData(token) {
  let dashboardData = {
    totalUsers: 0,
    totalProduction: 0,
    totalHealth: 0,
    recentActivities: [],
    productionTrend: [],
    healthStatus: {
      healthy: 0,
      warning: 0,
      critical: 0,
    },
  };

  try {
    // 并行获取各种统计数据
    const [statsResult, productionResult, healthResult] =
      await Promise.allSettled([
        apiService.getDashboardStats(token),
        apiService.getProductionRecords(token, { limit: 10 }),
        apiService.getHealthRecords(token, { limit: 10 }),
      ]);

    // 处理统计数据
    if (statsResult.status === "fulfilled" && statsResult.value.success) {
      dashboardData = { ...dashboardData, ...statsResult.value.data };
    }

    // 处理生产记录数据
    if (
      productionResult.status === "fulfilled" &&
      productionResult.value.success
    ) {
      const records = productionResult.value.data.records || [];
      dashboardData.totalProduction =
        productionResult.value.data.total || records.length;
      dashboardData.productionTrend = generateProductionTrend(records);
    }

    // 处理健康记录数据
    if (healthResult.status === "fulfilled" && healthResult.value.success) {
      const records = healthResult.value.data || [];
      dashboardData.totalHealth = records.length;
      dashboardData.healthStatus = generateHealthStatus(records);
    }

    // 生成最近活动
    dashboardData.recentActivities = generateRecentActivities();
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取仪表板数据失败:", error); } catch(_) {}

    // 使用默认数据，不阻止页面渲染
  }

  return dashboardData;
}

/**
 * 生成生产趋势数据
 */
function generateProductionTrend(records, days = 7) {
  const trend = [];
  const today = new Date();

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split("T")[0];

    // 查找该日期的记录
    const dayRecords = records.filter((record) => {
      const recordDate = new Date(record.createdAt || record.记录日期);
      return recordDate.toISOString().split("T")[0] === dateStr;
    });

    const totalEggs = dayRecords.reduce(
      (sum, record) => sum + (record.产蛋数量 || record.eggCount || 0),
      0,
    );
    const totalFeed = dayRecords.reduce(
      (sum, record) => sum + (record.饲料消耗 || record.feedConsumption || 0),
      0,
    );

    trend.push({
      date: dateStr,
      eggs: totalEggs,
      feed: totalFeed,
      label: date.toLocaleDateString("zh-CN", {
        month: "short",
        day: "numeric",
      }),
      weekday: date.toLocaleDateString("zh-CN", { weekday: "short" }),
    });
  }

  return trend;
}

/**
 * 生成健康状态统计
 */
function generateHealthStatus(records) {
  const status = {
    healthy: 0,
    warning: 0,
    critical: 0,
    total: records.length,
  };

  records.forEach((record) => {
    const healthStatus = record.健康状态 || record.healthStatus || "healthy";

    switch (healthStatus.toLowerCase()) {
      case "健康":
      case "healthy":
      case "good":
        status.healthy++;
        break;
      case "警告":
      case "warning":
      case "attention":
        status.warning++;
        break;
      case "严重":
      case "critical":
      case "danger":
        status.critical++;
        break;
      default:
        status.healthy++;
    }
  });

  // 计算百分比
  const total = status.total || 1;
  status.healthyPercent = Math.round((status.healthy / total) * 100);
  status.warningPercent = Math.round((status.warning / total) * 100);
  status.criticalPercent = Math.round((status.critical / total) * 100);

  return status;
}

/**
 * 生成最近活动数据
 */
function generateRecentActivities(limit = 10) {
  const activities = [
    {
      type: "production",
      icon: "bi-clipboard-data",
      title: "新增生产记录",
      description: "记录了今日的产蛋数量",
      time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      color: "success",
      user: "系统管理员",
    },
    {
      type: "health",
      icon: "bi-heart-pulse",
      title: "健康检查完成",
      description: "完成了鹅群的健康状态检查",
      time: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      color: "info",
      user: "兽医小李",
    },
    {
      type: "user",
      icon: "bi-person-plus",
      title: "新用户注册",
      description: "有新的用户加入系统",
      time: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      color: "primary",
      user: "系统",
    },
    {
      type: "system",
      icon: "bi-gear",
      title: "系统更新",
      description: "系统配置已更新",
      time: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      color: "warning",
      user: "系统管理员",
    },
    {
      type: "alert",
      icon: "bi-exclamation-triangle",
      title: "健康预警",
      description: "发现部分鹅群健康状况需要关注",
      time: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
      color: "danger",
      user: "系统监控",
    },
  ];

  // 按时间排序并限制数量
  return activities
    .sort((a, b) => new Date(b.time) - new Date(a.time))
    .slice(0, limit)
    .map((activity) => ({
      ...activity,
      timeAgo: getTimeAgo(activity.time),
    }));
}

/**
 * 获取相对时间描述
 */
function getTimeAgo(dateString) {
  const now = new Date();
  const date = new Date(dateString);
  const diffMs = now - date;
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMins < 1) return "刚刚";
  if (diffMins < 60) return `${diffMins}分钟前`;
  if (diffHours < 24) return `${diffHours}小时前`;
  if (diffDays < 30) return `${diffDays}天前`;

  return date.toLocaleDateString("zh-CN");
}

module.exports = router;
