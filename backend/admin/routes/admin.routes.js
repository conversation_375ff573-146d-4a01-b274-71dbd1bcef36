const express = require('express');
const adminController = require('../controllers/admin.controller');
const notificationController = require('../controllers/notification.controller');
const backupController = require('../controllers/backup.controller');

// 首页路由
router.get('/', (req, res, next) => {
  try {
    res.render('index', {
      title: '智慧养鹅管理系统',
      page: 'home',
      layout: 'layout'
    });
  } catch (err) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('渲染首页时出错', { error: err.message, stack: err.stack }); } catch(_) {}
    next(err);
  }
});

// 仪表板路由
router.get('/dashboard', adminController.getDashboardData);

// 用户管理路由
router.get('/users', adminController.getUsersList);

// 健康管理路由
router.get('/health', adminController.getHealthList);

// 生产管理路由
router.get('/production', adminController.getProductionList);

// 商品管理路由
const shopRoutes = require('./shop');

// 帮助中心管理路由
const helpCenterRoutes = require('./help-center');

// 设置路由
router.get('/settings', (req, res) => {
  try {
    res.render('settings', {
      title: '系统设置 - 智慧养鹅管理系统',
      page: 'settings',
      layout: 'layout'
    });
  } catch (err) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('渲染系统设置页面时出错', { error: err.message, stack: err.stack, name: err.name }); } catch(_) {}
    next(err); // 将错误传递给错误处理中间件
  }
});

// 图标管理路由
router.get('/icons', (req, res) => {
  try {
    res.render('icons', {
      title: '图标管理 - 智慧养鹅管理系统',
      page: 'icons',
      layout: 'layout'
    });
  } catch (err) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('渲染图标管理时出错', { error: err.message, stack: err.stack }); } catch(_) {}
    throw err;
  }
});

// API路由 - 用户管理
router.post('/api/users', adminController.createUser);
router.put('/api/users/:id', adminController.updateUser);
router.delete('/api/users/:id', adminController.deleteUser);

// API路由 - 生产记录管理
router.post('/api/production', adminController.createProductionRecord);
router.put('/api/production/:id', adminController.updateProductionRecord);
router.delete('/api/production/:id', adminController.deleteProductionRecord);

// API路由 - 健康记录管理
router.post('/api/health', adminController.createHealthRecord);

// 通知管理路由
router.get('/notifications', (req, res) => {
  try {
    res.render('notifications', {
      title: '通知管理 - 智慧养鹅管理系统',
      page: 'notifications',
      layout: 'layout'
    });
  } catch (err) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('渲染通知管理页面时出错', { error: err.message, stack: err.stack }); } catch(_) {}
    next(err);
  }
});

// 备份管理路由
router.get('/backup', (req, res) => {
  try {
    res.render('backup', {
      title: '系统备份 - 智慧养鹅管理系统',
      page: 'backup',
      layout: 'layout'
    });
  } catch (err) {
    try { const { Logger } = require('../../middleware/errorHandler'); Logger.error('渲染备份管理页面时出错', { error: err.message, stack: err.stack }); } catch(_) {}
    next(err);
  }
});

// API路由 - 通知管理
router.get('/api/notifications', notificationController.getNotifications);
router.post('/api/notifications', notificationController.createNotification);
router.put('/api/notifications/:id', notificationController.updateNotification);
router.delete(
  '/api/notifications/:id',
  notificationController.deleteNotification
);
router.post(
  '/api/notifications/bulk',
  notificationController.sendBulkNotification
);
router.get(
  '/api/notifications/stats',
  notificationController.getNotificationStats
);

// API路由 - 备份管理
router.get('/api/backup/list', backupController.getBackupList);
router.post('/api/backup/create', backupController.createManualBackup);
router.delete('/api/backup/:id', backupController.deleteBackup);
router.get('/api/backup/:id/download', backupController.downloadBackup);
router.post('/api/backup/:id/restore', backupController.restoreDatabase);
router.get('/api/backup/config', backupController.getBackupConfig);
router.put('/api/backup/config', backupController.updateBackupConfig);
router.get('/api/backup/stats', backupController.getBackupStats);

module.exports = router;
