const express = require('express');

// 简单的健康管理API路由
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: '健康管理API',
    data: []
  });
});

router.post('/', (req, res) => {
  res.json({
    success: true,
    message: '创建健康记录成功',
    data: null
  });
});

router.put('/:id', (req, res) => {
  res.json({
    success: true,
    message: '更新健康记录成功',
    data: null
  });
});

router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: '删除健康记录成功',
    data: null
  });
});

module.exports = router;
