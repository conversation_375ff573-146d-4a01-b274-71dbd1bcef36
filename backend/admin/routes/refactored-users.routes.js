/**
 * 重构后的用户路由
 * 使用新的中间件和控制器
 */

const express = require('express');
const usersController = require('../controllers/refactored-users.controller');
const { renderPage, asyncHandler } = require('../utils/response-helper');

// 应用通用中间件
router.use(requireAuth);

/**
 * 用户管理页面
 */
router.get(
  '/',
  asyncHandler(async (req, res) => {
    renderPage(res, 'users/index', {
      title: '用户管理 - 智慧养鹅管理系统',
      user: req.session.user,
      currentPage: 'users'
    });
  })
);

// API路由 - 应用API限流
router.use('/api/*', rateLimit(60, 60000)); // 每分钟60次请求

/**
 * API路由 - 获取用户列表
 */
router.get(
  '/api/users',
  asyncHandler(usersController.getList.bind(usersController))
);

/**
 * API路由 - 获取用户统计
 */
router.get(
  '/api/users/stats',
  asyncHandler(usersController.getStats.bind(usersController))
);

/**
 * API路由 - 搜索用户
 */
router.get(
  '/api/users/search',
  asyncHandler(usersController.search.bind(usersController))
);

/**
 * API路由 - 获取角色选项
 */
router.get(
  '/api/users/role-options',
  asyncHandler(usersController.getRoleOptions.bind(usersController))
);

/**
 * API路由 - 创建用户
 * 需要管理员或管理者权限
 */
router.post(
  '/api/users',
  requireRole(['admin', 'manager']),
  auditLog('CREATE', 'USER'),
  asyncHandler(usersController.create.bind(usersController))
);

/**
 * API路由 - 获取用户详情
 */
router.get(
  '/api/users/:id',
  asyncHandler(usersController.getById.bind(usersController))
);

/**
 * API路由 - 更新用户
 * 需要管理员或管理者权限
 */
router.put(
  '/api/users/:id',
  requireRole(['admin', 'manager']),
  auditLog('UPDATE', 'USER'),
  asyncHandler(usersController.update.bind(usersController))
);

/**
 * API路由 - 删除用户
 * 需要管理员权限
 */
router.delete(
  '/api/users/:id',
  requireRole(['admin']),
  auditLog('DELETE', 'USER'),
  asyncHandler(usersController.delete.bind(usersController))
);

/**
 * API路由 - 批量删除用户
 * 需要管理员权限
 */
router.post(
  '/api/users/batch/delete',
  requireRole(['admin']),
  auditLog('BATCH_DELETE', 'USER'),
  asyncHandler(usersController.batchDelete.bind(usersController))
);

/**
 * API路由 - 批量更新用户状态
 * 需要管理员或管理者权限
 */
router.post(
  '/api/users/batch/update-status',
  requireRole(['admin', 'manager']),
  auditLog('BATCH_UPDATE_STATUS', 'USER'),
  asyncHandler(usersController.batchUpdateStatus.bind(usersController))
);

/**
 * API路由 - 重置用户密码
 * 需要管理员或管理者权限
 */
router.post(
  '/api/users/:id/reset-password',
  requireRole(['admin', 'manager']),
  auditLog('RESET_PASSWORD', 'USER'),
  asyncHandler(usersController.resetPassword.bind(usersController))
);

/**
 * API路由 - 切换用户状态
 * 需要管理员或管理者权限
 */
router.post(
  '/api/users/:id/toggle-status',
  requireRole(['admin', 'manager']),
  auditLog('TOGGLE_STATUS', 'USER'),
  asyncHandler(usersController.toggleStatus.bind(usersController))
);

/**
 * API路由 - 获取用户活动日志
 * 需要管理员权限
 */
router.get(
  '/api/users/:id/activity-log',
  requireRole(['admin']),
  asyncHandler(usersController.getActivityLog.bind(usersController))
);

/**
 * API路由 - 导出用户数据
 * 需要管理员或管理者权限
 */
router.get(
  '/api/users/export',
  requireRole(['admin', 'manager']),
  auditLog('EXPORT', 'USER'),
  asyncHandler(usersController.export.bind(usersController))
);

module.exports = router;
