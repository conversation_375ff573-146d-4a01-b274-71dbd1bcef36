const express = require('express');
const apiService = require('../utils/apiService');

/**
 * 生产记录列表页面
 */
router.get('/', async (req, res) => {
  try {
    if (!req.session.user || !req.session.token) {
      return res.redirect('/login');
    }

    let records = [];
    let total = 0;
    let error = null;

    try {
      const result = await apiService.getProductionRecords(req.session.token);
      if (result.success) {
        records = result.data.records || [];
        total = result.data.total || records.length;
      } else {
        error = result.message;
      }
    } catch (err) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取生产记录失败:', err); } catch(_) {}

      error = '获取生产记录失败，请稍后再试';
    }

    res.render('production/index', {
      title: '生产管理 - 智慧养鹅管理系统',
      records: records,
      total: total,
      error: error
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('生产管理路由错误:', error); } catch(_) {}

    res.status(500).render('error', {
      title: '服务器错误 - 智慧养鹅管理系统',
      error: {
        status: 500,
        message: '获取生产数据失败',
        stack: process.env.NODE_ENV === 'development' ? error.stack : ''
      }
    });
  }
});

/**
 * 创建生产记录页面
 */
router.get('/create', (req, res) => {
  if (!req.session.user || !req.session.token) {
    return res.redirect('/login');
  }

  res.render('production/create', {
    title: '添加生产记录 - 智慧养鹅管理系统'
  });
});

/**
 * 编辑生产记录页面
 */
router.get('/edit/:id', (req, res) => {
  if (!req.session.user || !req.session.token) {
    return res.redirect('/login');
  }

  const recordId = req.params.id;
  // 这里应该调用API获取单个记录信息
  // 暂时使用模拟数据
  const record = {
    id: recordId,
    产蛋数量: 120,
    饲料消耗: 45.5,
    温度: 25.8,
    湿度: 62.0,
    备注: '正常生产',
    记录日期: new Date().toISOString().split('T')[0]
  };

  res.render('production/edit', {
    title: '编辑生产记录 - 智慧养鹅管理系统',
    record: record
  });
});

module.exports = router;
