const express = require('express');
const apiService = require('../utils/apiService');

/**
 * 健康记录列表页面
 */
router.get('/', async (req, res) => {
  try {
    if (!req.session.user || !req.session.token) {
      return res.redirect('/login');
    }

    let records = [];
    let error = null;

    try {
      const result = await apiService.getHealthRecords(req.session.token);
      if (result.success) {
        records = result.data || [];
      } else {
        error = result.message;
      }
    } catch (err) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取健康记录失败:', err); } catch(_) {}

      error = '获取健康记录失败，请稍后再试';
    }

    res.render('health/index', {
      title: '健康管理 - 智慧养鹅管理系统',
      records: records,
      error: error
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('健康管理路由错误:', error); } catch(_) {}

    res.status(500).render('error', {
      title: '服务器错误 - 智慧养鹅管理系统',
      error: {
        status: 500,
        message: '获取健康数据失败',
        stack: process.env.NODE_ENV === 'development' ? error.stack : ''
      }
    });
  }
});

/**
 * 创建健康记录页面
 */
router.get('/create', (req, res) => {
  if (!req.session.user || !req.session.token) {
    return res.redirect('/login');
  }

  res.render('health/create', {
    title: '添加健康记录 - 智慧养鹅管理系统'
  });
});

module.exports = router;
