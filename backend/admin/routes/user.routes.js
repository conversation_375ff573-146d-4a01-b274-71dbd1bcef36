const express = require('express');

// 简单的用户管理API路由
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: '用户管理API',
    data: []
  });
});

router.post('/', (req, res) => {
  res.json({
    success: true,
    message: '创建用户成功',
    data: null
  });
});

router.put('/:id', (req, res) => {
  res.json({
    success: true,
    message: '更新用户成功',
    data: null
  });
});

router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: '删除用户成功',
    data: null
  });
});

module.exports = router;
