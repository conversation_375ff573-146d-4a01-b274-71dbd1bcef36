const express = require('express');

// 简单的生产管理API路由
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: '生产管理API',
    data: []
  });
});

router.post('/', (req, res) => {
  res.json({
    success: true,
    message: '创建生产记录成功',
    data: null
  });
});

router.put('/:id', (req, res) => {
  res.json({
    success: true,
    message: '更新生产记录成功',
    data: null
  });
});

router.delete('/:id', (req, res) => {
  res.json({
    success: true,
    message: '删除生产记录成功',
    data: null
  });
});

module.exports = router;
