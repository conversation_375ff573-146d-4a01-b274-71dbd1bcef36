/**
 * 基础控制器类
 * 提供通用的CRUD操作和工具方法
 */

const {
  successResponse,
  errorResponse,
  getPaginationConfig,
  validateParams
} = require('../utils/response-helper');
const apiService = require('../utils/apiService');

class BaseController {
  constructor(resourceName, apiEndpoint) {
    this.resourceName = resourceName;
    this.apiEndpoint = apiEndpoint;
  }

  /**
   * 获取资源列表
   */
  async getList(req, res) {
    try {
      const pagination = getPaginationConfig(req.query);
      const { search, sort, order, ...filters } = req.query;

      const params = {
        ...filters,
        page: pagination.page,
        limit: pagination.limit,
        search,
        sort,
        order
      };

      const result = await apiService.get(
        this.apiEndpoint,
        req.session.token,
        params
      );

      if (result.success) {
        const metadata = pagination.getMetadata(
          result.data.total || result.data.length
        );
        successResponse(
          res,
          result.data,
          `获取${this.resourceName}列表成功`,
          metadata
        );
      } else {
        errorResponse(
          res,
          result.message || `获取${this.resourceName}列表失败`
        );
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error(`获取${this.resourceName}列表失败:`, error); } catch(_) {}

      errorResponse(res, `获取${this.resourceName}列表失败`, 500);
    }
  }

  /**
   * 获取单个资源详情
   */
  async getById(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return errorResponse(res, 'ID参数缺失', 400);
      }

      const result = await apiService.get(
        `${this.apiEndpoint}/${id}`,
        req.session.token
      );

      if (result.success) {
        successResponse(res, result.data, `获取${this.resourceName}详情成功`);
      } else {
        errorResponse(
          res,
          result.message || `获取${this.resourceName}详情失败`
        );
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error(`获取${this.resourceName}详情失败:`, error); } catch(_) {}

      errorResponse(res, `获取${this.resourceName}详情失败`, 500);
    }
  }

  /**
   * 创建资源
   */
  async create(req, res) {
    try {
      const validation = this.validateCreateData
        ? this.validateCreateData(req.body)
        : { isValid: true };

      if (!validation.isValid) {
        return errorResponse(res, '数据验证失败', 400, validation.errors);
      }

      const result = await apiService.post(
        this.apiEndpoint,
        req.session.token,
        req.body
      );

      if (result.success) {
        successResponse(res, result.data, `创建${this.resourceName}成功`, {
          created: true
        });
      } else {
        errorResponse(res, result.message || `创建${this.resourceName}失败`);
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error(`创建${this.resourceName}失败:`, error); } catch(_) {}

      errorResponse(res, `创建${this.resourceName}失败`, 500);
    }
  }

  /**
   * 更新资源
   */
  async update(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return errorResponse(res, 'ID参数缺失', 400);
      }

      const validation = this.validateUpdateData
        ? this.validateUpdateData(req.body)
        : { isValid: true };

      if (!validation.isValid) {
        return errorResponse(res, '数据验证失败', 400, validation.errors);
      }

      const result = await apiService.put(
        `${this.apiEndpoint}/${id}`,
        req.session.token,
        req.body
      );

      if (result.success) {
        successResponse(res, result.data, `更新${this.resourceName}成功`, {
          updated: true
        });
      } else {
        errorResponse(res, result.message || `更新${this.resourceName}失败`);
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error(`更新${this.resourceName}失败:`, error); } catch(_) {}

      errorResponse(res, `更新${this.resourceName}失败`, 500);
    }
  }

  /**
   * 删除资源
   */
  async delete(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return errorResponse(res, 'ID参数缺失', 400);
      }

      const result = await apiService.delete(
        `${this.apiEndpoint}/${id}`,
        req.session.token
      );

      if (result.success) {
        successResponse(res, null, `删除${this.resourceName}成功`, {
          deleted: true
        });
      } else {
        errorResponse(res, result.message || `删除${this.resourceName}失败`);
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error(`删除${this.resourceName}失败:`, error); } catch(_) {}

      errorResponse(res, `删除${this.resourceName}失败`, 500);
    }
  }

  /**
   * 批量删除资源
   */
  async batchDelete(req, res) {
    try {
      const { ids } = req.body;

      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return errorResponse(res, 'IDs参数缺失或格式错误', 400);
      }

      const result = await apiService.post(
        `${this.apiEndpoint}/batch/delete`,
        req.session.token,
        { ids }
      );

      if (result.success) {
        successResponse(res, result.data, `批量删除${this.resourceName}成功`, {
          deleted: true,
          count: ids.length
        });
      } else {
        errorResponse(
          res,
          result.message || `批量删除${this.resourceName}失败`
        );
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error(`批量删除${this.resourceName}失败:`, error); } catch(_) {}

      errorResponse(res, `批量删除${this.resourceName}失败`, 500);
    }
  }

  /**
   * 获取资源统计信息
   */
  async getStats(req, res) {
    try {
      const result = await apiService.get(
        `${this.apiEndpoint}/stats`,
        req.session.token,
        req.query
      );

      if (result.success) {
        successResponse(
          res,
          result.data,
          `获取${this.resourceName}统计信息成功`
        );
      } else {
        errorResponse(
          res,
          result.message || `获取${this.resourceName}统计信息失败`
        );
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error(`获取${this.resourceName}统计信息失败:`, error); } catch(_) {}

      errorResponse(res, `获取${this.resourceName}统计信息失败`, 500);
    }
  }

  /**
   * 导出资源数据
   */
  async export(req, res) {
    try {
      const { format = 'excel', ...params } = req.query;

      const result = await apiService.get(
        `${this.apiEndpoint}/export`,
        req.session.token,
        {
          format,
          ...params
        }
      );

      if (result.success) {
        successResponse(res, result.data, `导出${this.resourceName}数据成功`);
      } else {
        errorResponse(
          res,
          result.message || `导出${this.resourceName}数据失败`
        );
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error(`导出${this.resourceName}数据失败:`, error); } catch(_) {}

      errorResponse(res, `导出${this.resourceName}数据失败`, 500);
    }
  }

  /**
   * 搜索资源
   */
  async search(req, res) {
    try {
      const { q, ...filters } = req.query;

      if (!q || q.trim().length === 0) {
        return errorResponse(res, '搜索关键词不能为空', 400);
      }

      const pagination = getPaginationConfig(req.query);

      const params = {
        search: q.trim(),
        ...filters,
        page: pagination.page,
        limit: pagination.limit
      };

      const result = await apiService.get(
        `${this.apiEndpoint}/search`,
        req.session.token,
        params
      );

      if (result.success) {
        const metadata = pagination.getMetadata(
          result.data.total || result.data.length
        );
        successResponse(
          res,
          result.data,
          `搜索${this.resourceName}成功`,
          metadata
        );
      } else {
        errorResponse(res, result.message || `搜索${this.resourceName}失败`);
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error(`搜索${this.resourceName}失败:`, error); } catch(_) {}

      errorResponse(res, `搜索${this.resourceName}失败`, 500);
    }
  }

  /**
   * 获取资源选项（用于下拉框等）
   */
  async getOptions(req, res) {
    try {
      const result = await apiService.get(
        `${this.apiEndpoint}/options`,
        req.session.token,
        req.query
      );

      if (result.success) {
        successResponse(res, result.data, `获取${this.resourceName}选项成功`);
      } else {
        errorResponse(
          res,
          result.message || `获取${this.resourceName}选项失败`
        );
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error(`获取${this.resourceName}选项失败:`, error); } catch(_) {}

      errorResponse(res, `获取${this.resourceName}选项失败`, 500);
    }
  }

  /**
   * 批量更新资源状态
   */
  async batchUpdateStatus(req, res) {
    try {
      const { ids, status } = req.body;

      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return errorResponse(res, 'IDs参数缺失或格式错误', 400);
      }

      if (status === undefined) {
        return errorResponse(res, 'status参数缺失', 400);
      }

      const result = await apiService.post(
        `${this.apiEndpoint}/batch/status`,
        req.session.token,
        {
          ids,
          status
        }
      );

      if (result.success) {
        successResponse(
          res,
          result.data,
          `批量更新${this.resourceName}状态成功`,
          {
            updated: true,
            count: ids.length
          }
        );
      } else {
        errorResponse(
          res,
          result.message || `批量更新${this.resourceName}状态失败`
        );
      }
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error(`批量更新${this.resourceName}状态失败:`, error); } catch(_) {}

      errorResponse(res, `批量更新${this.resourceName}状态失败`, 500);
    }
  }
}

module.exports = BaseController;
