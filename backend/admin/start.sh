#!/bin/bash

# 智慧养鹅管理后台启动脚本

echo "🚀 正在启动智慧养鹅管理后台..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到Node.js，请先安装Node.js"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: 未找到npm，请先安装npm"
    exit 1
fi

# 检查package.json是否存在
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 未找到package.json文件"
    exit 1
fi

# 检查node_modules是否存在，如果不存在则安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 正在安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

# 设置环境变量
export NODE_ENV=development
export ADMIN_PORT=3003

# 启动应用
echo "🎯 启动管理后台服务..."
echo "📊 访问地址: http://localhost:3003"
echo "🔑 测试账号: demo / demo123"
echo ""

# 使用nodemon进行开发模式启动（如果可用）
if command -v nodemon &> /dev/null; then
    echo "🔄 使用nodemon启动（开发模式）"
    nodemon app.js
else
    echo "🔄 使用node启动"
    node app.js
fi
