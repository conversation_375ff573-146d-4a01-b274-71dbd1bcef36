/**
 * 改进的API服务工具类
 * 基于现有apiService的增强版本，添加更多通用方法
 */

const axios = require("axios");

/**
 * API服务工具类
 */
class ImprovedApiService {
  constructor() {
    this.baseURL = process.env.API_BASE_URL || "http://localhost:3000/api/v1";
    this.timeout = 10000;

    // 创建axios实例
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        "Content-Type": "application/json",
      },
    });

    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        try { 
          const { Logger } = require('../middleware/errorHandler'); 
          Logger.info(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        } catch(_) {}
        return config;
      },
      (error) => {
        try { const { Logger } = require('../middleware/errorHandler'); Logger.error("API Request Error:", error); } catch(_) {}

        return Promise.reject(error);
      },
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        try { const { Logger } = require('../middleware/errorHandler'); Logger.info(`API Response: ${response.status} ${response.config.url}`); } catch(_) {}

        return response;
      },
      (error) => {
        try { 
          const { Logger } = require('../middleware/errorHandler'); 
          Logger.error("API Response Error:", error.response?.status, error.response?.data);
        } catch(_) {}
        return Promise.reject(error);
      },
    );
  }

  /**
   * 通用GET请求
   * @param {string} endpoint API端点
   * @param {string} token 认证token
   * @param {Object} params 查询参数
   */
  async get(endpoint, token, params = {}) {
    try {
      const response = await this.client.get(endpoint, {
        headers: { Authorization: `Bearer ${token}` },
        params,
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 通用POST请求
   * @param {string} endpoint API端点
   * @param {string} token 认证token
   * @param {Object} data 请求数据
   */
  async post(endpoint, token, data = {}) {
    try {
      const response = await this.client.post(endpoint, data, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 通用PUT请求
   * @param {string} endpoint API端点
   * @param {string} token 认证token
   * @param {Object} data 请求数据
   */
  async put(endpoint, token, data = {}) {
    try {
      const response = await this.client.put(endpoint, data, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 通用PATCH请求
   * @param {string} endpoint API端点
   * @param {string} token 认证token
   * @param {Object} data 请求数据
   */
  async patch(endpoint, token, data = {}) {
    try {
      const response = await this.client.patch(endpoint, data, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 通用DELETE请求
   * @param {string} endpoint API端点
   * @param {string} token 认证token
   */
  async delete(endpoint, token) {
    try {
      const response = await this.client.delete(endpoint, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 批量请求
   * @param {Array} requests 请求配置数组
   * @param {string} token 认证token
   */
  async batchRequest(requests, token) {
    try {
      const promises = requests.map(({ method, endpoint, data = {} }) => {
        return this[method.toLowerCase()](endpoint, token, data);
      });

      return await Promise.allSettled(promises);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 文件上传
   * @param {string} endpoint 上传端点
   * @param {string} token 认证token
   * @param {FormData} formData 文件数据
   */
  async upload(endpoint, token, formData) {
    try {
      const response = await this.client.post(endpoint, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "multipart/form-data",
        },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 下载文件
   * @param {string} endpoint 下载端点
   * @param {string} token 认证token
   * @param {Object} params 查询参数
   */
  async download(endpoint, token, params = {}) {
    try {
      const response = await this.client.get(endpoint, {
        headers: { Authorization: `Bearer ${token}` },
        params,
        responseType: "blob",
      });
      return response;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 带重试的请求
   * @param {Function} requestFn 请求函数
   * @param {number} maxRetries 最大重试次数
   * @param {number} delay 重试延迟（毫秒）
   */
  async requestWithRetry(requestFn, maxRetries = 3, delay = 1000) {
    let lastError;

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error;

        if (i === maxRetries) break;

        // 只在网络错误或5xx错误时重试
        if (this.shouldRetry(error)) {
          try { const { Logger } = require('../middleware/errorHandler'); Logger.info(`请求失败，${delay}ms后进行第${i + 1}次重试...`); } catch(_) {}

          await this.sleep(delay);
          delay *= 2; // 指数退避
        } else {
          break;
        }
      }
    }

    throw lastError;
  }

  /**
   * 判断是否应该重试请求
   * @param {Error} error 错误对象
   */
  shouldRetry(error) {
    if (!error.response) return true; // 网络错误

    const status = error.response.status;
    return status >= 500 || status === 429; // 服务器错误或限流
  }

  /**
   * 睡眠函数
   * @param {number} ms 毫秒数
   */
  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 缓存请求结果
   * @param {string} key 缓存键
   * @param {Function} requestFn 请求函数
   * @param {number} ttl 缓存时间（秒）
   */
  async cached(key, requestFn, ttl = 300) {
    if (!this.cache) {
      this.cache = new Map();
    }

    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < ttl * 1000) {
      return cached.data;
    }

    const data = await requestFn();
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });

    return data;
  }

  /**
   * 清除缓存
   * @param {string} key 缓存键，不传则清除所有
   */
  clearCache(key = null) {
    if (!this.cache) return;

    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  /**
   * 设置请求超时时间
   * @param {number} timeout 超时时间（毫秒）
   */
  setTimeout(timeout) {
    this.timeout = timeout;
    this.client.defaults.timeout = timeout;
  }

  /**
   * 设置基础URL
   * @param {string} baseURL 基础URL
   */
  setBaseURL(baseURL) {
    this.baseURL = baseURL;
    this.client.defaults.baseURL = baseURL;
  }

  /**
   * 错误处理
   */
  handleError(error) {
    if (error.response) {
      // 服务器响应了错误状态码
      return {
        success: false,
        status: error.response.status,
        message: error.response.data?.message || "服务器错误",
        data: error.response.data,
      };
    } else if (error.request) {
      // 请求已发出但没有收到响应
      return {
        success: false,
        status: 0,
        message: "网络连接错误，请检查API服务是否正常运行",
        data: null,
      };
    } else {
      // 其他错误
      return {
        success: false,
        status: -1,
        message: error.message || "未知错误",
        data: null,
      };
    }
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    try {
      const response = await this.client.get("/health");
      return {
        success: true,
        data: response.data,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: this.handleError(error),
        timestamp: new Date().toISOString(),
      };
    }
  }
}

// 创建单例实例并导出原有的方法以保持兼容性
const improvedApiService = new ImprovedApiService();

// 兼容原有接口
improvedApiService.setAuthToken = function (token) {
  if (token) {
    this.client.defaults.headers.common["Authorization"] = `Bearer ${token}`;
  } else {
    delete this.client.defaults.headers.common["Authorization"];
  }
};

// 原有方法的兼容实现
improvedApiService.login = async function (credentials) {
  return this.post("/auth/login", null, credentials);
};

improvedApiService.getUserInfo = async function (token) {
  return this.get("/auth/me", token);
};

improvedApiService.getUsers = async function (token) {
  return this.get("/auth/users", token);
};

improvedApiService.getProductionRecords = async function (token, params = {}) {
  return this.get("/production-records", token, params);
};

improvedApiService.createProductionRecord = async function (token, data) {
  return this.post("/production-records", token, data);
};

improvedApiService.updateProductionRecord = async function (token, id, data) {
  return this.put(`/production-records/${id}`, token, data);
};

improvedApiService.deleteProductionRecord = async function (token, id) {
  return this.delete(`/production-records/${id}`, token);
};

improvedApiService.getHealthRecords = async function (token, params = {}) {
  return this.get("/health/records", token, params);
};

improvedApiService.createHealthRecord = async function (token, data) {
  return this.post("/health/records", token, data);
};

improvedApiService.getDashboardStats = async function (token) {
  try {
    const [usersRes, productionRes, healthRes] = await this.batchRequest(
      [
        { method: "GET", endpoint: "/auth/users" },
        { method: "GET", endpoint: "/production-records", data: { limit: 1 } },
        { method: "GET", endpoint: "/health/records", data: { limit: 1 } },
      ],
      token,
    );

    return {
      success: true,
      totalUsers:
        usersRes.status === "fulfilled" ? usersRes.value?.data?.length || 0 : 0,
      totalProduction:
        productionRes.status === "fulfilled"
          ? productionRes.value?.data?.total || 0
          : 0,
      totalHealth:
        healthRes.status === "fulfilled"
          ? healthRes.value?.data?.length || 0
          : 0,
      recentActivities: [],
    };
  } catch (error) {
    throw this.handleError(error);
  }
};

module.exports = improvedApiService;
