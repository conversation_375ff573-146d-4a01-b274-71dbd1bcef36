const axios = require("axios");

/**
 * API服务工具类
 * 用于与后端API进行通信
 */
class ApiService {
  constructor() {
    this.baseURL = process.env.API_BASE_URL || "http://localhost:3000/api/v1";
    this.timeout = 10000; // 10秒超时

    // 创建axios实例
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        "Content-Type": "application/json",
      },
    });

    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        try { 
          const { Logger } = require('../middleware/errorHandler'); 
          Logger.info(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        } catch(_) {}
        return config;
      },
      (error) => {
        try { const { Logger } = require('../middleware/errorHandler'); Logger.error("API Request Error:", error); } catch(_) {}

        return Promise.reject(error);
      },
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        try { const { Logger } = require('../middleware/errorHandler'); Logger.info(`API Response: ${response.status} ${response.config.url}`); } catch(_) {}

        return response;
      },
      (error) => {
        try { 
          const { Logger } = require('../middleware/errorHandler'); 
          Logger.error("API Response Error:", error.response?.status, error.response?.data);
        } catch(_) {}
        return Promise.reject(error);
      },
    );
  }

  /**
   * 设置认证token
   */
  setAuthToken(token) {
    if (token) {
      this.client.defaults.headers.common["Authorization"] = `Bearer ${token}`;
    } else {
      delete this.client.defaults.headers.common["Authorization"];
    }
  }

  /**
   * 用户认证相关API
   */
  async login(credentials) {
    try {
      const response = await this.client.post("/auth/login", credentials);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getUserInfo(token) {
    try {
      const response = await this.client.get("/auth/me", {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getUsers(token) {
    try {
      const response = await this.client.get("/auth/users", {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 生产记录相关API
   */
  async getProductionRecords(token, params = {}) {
    try {
      const response = await this.client.get("/production-records", {
        headers: { Authorization: `Bearer ${token}` },
        params,
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async createProductionRecord(token, data) {
    try {
      const response = await this.client.post("/production-records", data, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async updateProductionRecord(token, id, data) {
    try {
      const response = await this.client.put(
        `/production-records/${id}`,
        data,
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async deleteProductionRecord(token, id) {
    try {
      const response = await this.client.delete(`/production-records/${id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 健康记录相关API
   */
  async getHealthRecords(token, params = {}) {
    try {
      const response = await this.client.get("/health/records", {
        headers: { Authorization: `Bearer ${token}` },
        params,
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async createHealthRecord(token, data) {
    try {
      const response = await this.client.post("/health/records", data, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 统计数据API
   */
  async getDashboardStats(token) {
    try {
      const [usersRes, productionRes, healthRes] = await Promise.allSettled([
        this.getUsers(token),
        this.getProductionRecords(token, { limit: 1 }),
        this.getHealthRecords(token, { limit: 1 }),
      ]);

      return {
        totalUsers:
          usersRes.status === "fulfilled"
            ? usersRes.value?.data?.length || 0
            : 0,
        totalProduction:
          productionRes.status === "fulfilled"
            ? productionRes.value?.data?.total || 0
            : 0,
        totalHealth:
          healthRes.status === "fulfilled"
            ? healthRes.value?.data?.length || 0
            : 0,
        recentActivities: [],
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // ===== 商品管理相关方法 =====

  /**
   * 获取商品列表
   */
  async getProducts(token, params = {}) {
    try {
      const response = await this.client.get("/shop/products", {
        headers: { Authorization: `Bearer ${token}` },
        params: params,
      });
      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 获取商品详情
   */
  async getProductById(token, id) {
    try {
      const response = await this.client.get(`/shop/products/${id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 创建商品
   */
  async createProduct(token, productData) {
    try {
      const response = await this.client.post("/shop/products", productData, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 更新商品
   */
  async updateProduct(token, id, productData) {
    try {
      const response = await this.client.put(`/shop/products/${id}`, productData, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 删除商品
   */
  async deleteProduct(token, id) {
    try {
      const response = await this.client.delete(`/shop/products/${id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 获取商品分类
   */
  async getProductCategories(token) {
    try {
      const response = await this.client.get("/shop/categories", {
        headers: { Authorization: `Bearer ${token}` },
      });
      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 获取订单列表
   */
  async getOrders(token, params = {}) {
    try {
      const response = await this.client.get("/shop/orders", {
        headers: { Authorization: `Bearer ${token}` },
        params: params,
      });
      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 更新订单状态
   */
  async updateOrderStatus(token, orderId, status) {
    try {
      const response = await this.client.put(`/shop/orders/${orderId}/status`, 
        { status }, 
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 获取发票列表
   */
  async getInvoices(token, params = {}) {
    try {
      const response = await this.client.get("/shop/invoices", {
        headers: { Authorization: `Bearer ${token}` },
        params: params,
      });
      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 更新发票状态
   */
  async updateInvoiceStatus(token, invoiceId, status) {
    try {
      const response = await this.client.patch(`/shop/invoices/${invoiceId}/status`, 
        { status },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * 错误处理
   */
  handleError(error) {
    if (error.response) {
      // 服务器响应了错误状态码
      return {
        status: error.response.status,
        message: error.response.data?.message || "服务器错误",
        data: error.response.data,
      };
    } else if (error.request) {
      // 请求已发出但没有收到响应
      return {
        status: 0,
        message: "网络连接错误，请检查API服务是否正常运行",
        data: null,
      };
    } else {
      // 其他错误
      return {
        status: -1,
        message: error.message || "未知错误",
        data: null,
      };
    }
  }
}

// 创建单例实例
const apiService = new ApiService();

module.exports = apiService;
