# 智慧养鹅管理后台系统

一个基于Express.js和EJS的现代化管理后台系统，专为智慧养鹅项目设计。

## 🌟 特性

- **现代化界面**: 基于Bootstrap 5的响应式设计
- **安全认证**: 完整的用户登录和会话管理
- **数据可视化**: Chart.js图表展示生产趋势
- **模块化架构**: 清晰的MVC架构设计
- **API集成**: 与后端API无缝对接
- **错误处理**: 完善的错误处理和日志记录

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装和启动

1. **克隆项目**

   ```bash
   git clone <repository-url>
   cd backend/admin
   ```

2. **安装依赖**

   ```bash
   npm install
   ```

3. **启动服务**

   ```bash
   # 使用启动脚本（推荐）
   ./start.sh

   # 或直接使用npm
   npm start

   # 开发模式
   npm run dev
   ```

4. **访问系统**
   - 地址: http://localhost:3003
   - 测试账号: `demo` / `demo123`

## 📁 项目结构

```
backend/admin/
├── app.js                 # 主应用文件
├── package.json          # 项目配置
├── start.sh             # 启动脚本
├── routes/              # 路由文件
│   ├── index.js         # 主路由
│   ├── dashboard.js     # 仪表板路由
│   ├── users.js         # 用户管理路由
│   ├── production.js    # 生产管理路由
│   ├── health.js        # 健康管理路由
│   ├── settings.js      # 系统设置路由
│   ├── icons.js         # 图标管理路由
│   └── api.js           # API路由
├── views/               # EJS模板文件
│   ├── layouts/         # 布局模板
│   │   └── main.ejs     # 主布局
│   ├── auth/            # 认证页面
│   │   └── login.ejs    # 登录页面
│   ├── dashboard/       # 仪表板页面
│   ├── users/           # 用户管理页面
│   ├── production/      # 生产管理页面
│   ├── health/          # 健康管理页面
│   ├── settings/        # 系统设置页面
│   ├── icons/           # 图标管理页面
│   └── error.ejs        # 错误页面
├── middleware/          # 中间件
│   ├── auth.js          # 认证中间件
│   └── errorHandler.js  # 错误处理中间件
├── utils/               # 工具类
│   └── apiService.js    # API服务
└── public/              # 静态资源
    ├── css/
    ├── js/
    └── images/
```

## 🔧 配置

### 环境变量

- `NODE_ENV`: 运行环境 (development/production)
- `ADMIN_PORT`: 服务端口 (默认: 3003)
- `API_BASE_URL`: 后端API地址 (默认: http://localhost:3001/api/v1)

### 会话配置

在 `app.js` 中可以配置会话参数：

```javascript
app.use(
  session({
    secret: "zhihuiyange-admin-secret-key",
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: false, // 生产环境设为true
      maxAge: 24 * 60 * 60 * 1000, // 24小时
    },
  }),
);
```

## 📊 功能模块

### 1. 仪表板

- 系统概览统计
- 生产趋势图表
- 健康状态分布
- 最近活动记录

### 2. 用户管理

- 用户列表查看
- 用户添加/编辑
- 角色权限管理
- 用户状态控制

### 3. 生产管理

- 生产记录管理
- 数据统计分析
- 趋势图表展示
- 数据导出功能

### 4. 健康管理

- 健康记录管理
- 状态分类统计
- 异常情况监控
- 治疗方案记录

### 5. 系统设置

- 基本配置管理
- 用户权限设置
- 通知配置
- 数据备份
- 系统日志

### 6. 图标管理

- 图标库管理
- 图标上传
- 图标分类
- 使用统计

## 🔐 安全特性

- **Helmet**: HTTP安全头设置
- **CORS**: 跨域请求控制
- **会话管理**: 安全的用户会话
- **输入验证**: 表单数据验证
- **错误处理**: 安全的错误信息展示

## 🎨 界面特性

- **响应式设计**: 支持各种设备尺寸
- **现代化UI**: Bootstrap 5 + Bootstrap Icons
- **数据可视化**: Chart.js图表组件
- **交互体验**: 流畅的用户交互
- **主题支持**: 可扩展的主题系统

## 🔌 API集成

系统通过 `utils/apiService.js` 与后端API进行通信：

```javascript
// 获取用户列表
const users = await apiService.getUsers(token);

// 创建生产记录
const result = await apiService.createProductionRecord(token, data);
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**

   ```bash
   # 查看端口占用
   lsof -i :3003
   # 修改端口
   export ADMIN_PORT=3004
   ```

2. **依赖安装失败**

   ```bash
   # 清除缓存重新安装
   npm cache clean --force
   npm install
   ```

3. **API连接失败**
   - 检查后端API服务是否启动
   - 确认API_BASE_URL配置正确

## 📝 开发指南

### 添加新页面

1. 在 `routes/` 中创建路由文件
2. 在 `views/` 中创建EJS模板
3. 在 `app.js` 中注册路由

### 自定义样式

在主布局文件 `views/layouts/main.ejs` 中添加自定义CSS。

### API服务扩展

在 `utils/apiService.js` 中添加新的API方法。

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**智慧养鹅团队** © 2024
