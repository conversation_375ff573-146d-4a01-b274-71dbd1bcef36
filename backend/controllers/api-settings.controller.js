const AIConfig = require('../models/ai-config.model');
const { Op } = require('sequelize');

// 获取API设置列表
exports.getAPISettings = async (req, res) => {
  try {
    const configs = await AIConfig.findAll({
      order: [
        ['priority', 'DESC'],
        ['createdAt', 'ASC']
      ]
    });

    // 脱敏处理API密钥
    const sanitizedConfigs = configs.map((config) => {
      const configData = config.toJSON();
      if (configData.apiKey) {
        configData.apiKey = configData.apiKey.substring(0, 8) + '****';
      }
      return configData;
    });

    res.json({
      success: true,
      data: sanitizedConfigs
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取API设置列表失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 更新API设置
exports.updateAPISetting = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = { ...req.body };

    // 如果包含API密钥，进行加密
    if (updateData.apiKey && !updateData.apiKey.includes('****')) {
      // 这里应该进行实际的加密处理
      // 暂时跳过加密步骤
    } else if (updateData.apiKey && updateData.apiKey.includes('****')) {
      // 如果是脱敏的密钥，不更新
      delete updateData.apiKey;
    }

    // 如果设置为默认，先取消其他默认配置
    if (updateData.isDefault) {
      await AIConfig.update(
        { isDefault: false },
        { where: { isDefault: true, id: { [Op.ne]: id } } }
      );
    }

    updateData.updatedBy = req.user.id;

    const [updatedRows] = await AIConfig.update(updateData, {
      where: { id }
    });

    if (updatedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'API配置不存在'
      });
    }

    res.json({
      success: true,
      message: 'API设置更新成功'
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('更新API设置失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 测试API连接
exports.testAPIConnection = async (req, res) => {
  try {
    const { id } = req.params;
    const config = await AIConfig.findByPk(id);

    if (!config) {
      return res.status(404).json({
        success: false,
        message: 'API配置不存在'
      });
    }

    // 这里可以添加实际的API连接测试逻辑
    // 暂时返回模拟结果
    res.json({
      success: true,
      message: 'API连接测试成功',
      data: {
        provider: config.provider,
        status: 'connected',
        responseTime: Math.floor(Math.random() * 1000) + 200
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('测试API连接失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '测试API连接失败',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取默认API配置
exports.getDefaultAPIConfig = async (req, res) => {
  try {
    const config = await AIConfig.findOne({
      where: {
        isDefault: true,
        enabled: true
      }
    });

    if (!config) {
      return res.status(404).json({
        success: false,
        message: '未找到默认API配置'
      });
    }

    // 脱敏处理API密钥
    const configData = config.toJSON();
    if (configData.apiKey) {
      configData.apiKey = configData.apiKey.substring(0, 8) + '****';
    }

    res.json({
      success: true,
      data: configData
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取默认API配置失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};
