/**
 * 统一库存管理控制器
 * 使用UnifiedInventory模型处理所有库存相关的业务逻辑
 * 遵循开发规范，使用Sequelize ORM替代原生SQL
 */

const UnifiedInventory = require('../models/unified-inventory.model');
const { Op } = require('sequelize');
const { apiResponse } = require('../utils/apiResponse');

class InventoryController {
  /**
   * 获取库存总览
   */
  async getOverview(req, res) {
    try {
      const userId = req.user.id;

      // 使用UnifiedInventory模型查询库存统计
      const inventoryItems = await UnifiedInventory.findAll({
        where: { userId },
        attributes: [
          'currentStock',
          'minStock',
          'category',
          'status',
          'location',
          'updatedAt'
        ]
      });

      // 计算统计数据
      const totalItems = inventoryItems.length;
      const totalStock = inventoryItems.reduce(
        (sum, item) => sum + (parseFloat(item.currentStock) || 0),
        0
      );
      const lowStockItems = inventoryItems.filter(
        (item) =>
          parseFloat(item.currentStock) <= parseFloat(item.minStock || 0)
      ).length;
      const activeItems = inventoryItems.filter(
        (item) => item.status === 'active'
      ).length;

      // 按类别和位置统计
      const categoryCount = [
        ...new Set(inventoryItems.map((item) => item.category))
      ].length;
      const locationCount = [
        ...new Set(inventoryItems.map((item) => item.location).filter(Boolean))
      ].length;

      // 获取最新更新时间
      const updateTime =
        inventoryItems.length > 0
          ? Math.max(
            ...inventoryItems.map((item) =>
              new Date(item.updatedAt).getTime()
            )
          )
          : null;

      res.json({
        success: true,
        data: {
          totalCount: totalItems,
          totalStock: totalStock,
          healthyCount: activeItems,
          sickCount: totalItems - activeItems,
          quarantineCount: lowStockItems,
          healthyRate:
            totalItems > 0 ? ((activeItems / totalItems) * 100).toFixed(1) : 0,
          breedCount: categoryCount,
          areaCount: locationCount,
          updateTime: updateTime ? new Date(updateTime) : null
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取库存总览失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取库存总览失败',
        error: error.message
      });
    }
  }

  /**
   * 获取分类统计 - 使用UnifiedInventory模型
   */
  async getCategories(req, res) {
    try {
      const userId = req.user.id;
      const { groupBy = 'category' } = req.query;

      // 使用UnifiedInventory查询库存数据
      const inventoryItems = await UnifiedInventory.findAll({
        where: { userId },
        attributes: ['category', 'location', 'currentStock', 'status', 'name']
      });

      let groupedData = {};

      // 根据分组方式进行数据聚合
      inventoryItems.forEach((item) => {
        let groupKey;
        switch (groupBy) {
        case 'location':
          groupKey = item.location || '未分配';
          break;
        case 'status':
          groupKey = item.status || 'unknown';
          break;
        case 'category':
        default:
          groupKey = item.category || 'other';
          break;
        }

        if (!groupedData[groupKey]) {
          groupedData[groupKey] = {
            category: groupKey,
            totalItems: 0,
            totalStock: 0,
            activeItems: 0,
            inactiveItems: 0
          };
        }

        groupedData[groupKey].totalItems += 1;
        groupedData[groupKey].totalStock += parseFloat(item.currentStock) || 0;

        if (item.status === 'active') {
          groupedData[groupKey].activeItems += 1;
        } else {
          groupedData[groupKey].inactiveItems += 1;
        }
      });

      // 格式化分类数据
      const formattedCategories = Object.values(groupedData)
        .map((category) => {
          const activeRate =
            category.totalItems > 0
              ? ((category.activeItems / category.totalItems) * 100).toFixed(1)
              : 0;

          return {
            category: category.category,
            categoryText: this.getCategoryText(groupBy, category.category),
            totalCount: category.totalItems,
            totalStock: category.totalStock,
            healthyCount: category.activeItems,
            sickCount: category.inactiveItems,
            quarantineCount: 0, // 兼容原接口
            healthyRate: parseFloat(activeRate)
          };
        })
        .sort((a, b) => b.totalCount - a.totalCount);

      res.json({
        success: true,
        data: {
          groupBy: groupBy,
          categories: formattedCategories
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取分类统计失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取分类统计失败',
        error: error.message
      });
    }
  }

  /**
   * 获取健康状态分析
   */
  async getHealthAnalysis(req, res) {
    try {
      const userId = req.user.id;
      const connection = await mysql.createConnection(dbConfig);

      // 获取健康状态统计
      const healthQuery = `
        SELECT 
          health_status,
          SUM(count) as count,
          COUNT(DISTINCT area_location) as area_count
        FROM inventory_records 
        WHERE user_id = ? AND record_date = (
          SELECT MAX(record_date) FROM inventory_records WHERE user_id = ?
        )
        GROUP BY health_status
      `;

      const [healthStats] = await connection.execute(healthQuery, [
        userId,
        userId
      ]);

      // 获取按区域的健康状态分布
      const areaQuery = `
        SELECT 
          area_location,
          health_status,
          SUM(count) as count
        FROM inventory_records 
        WHERE user_id = ? AND record_date = (
          SELECT MAX(record_date) FROM inventory_records WHERE user_id = ?
        )
        GROUP BY area_location, health_status
        ORDER BY area_location, health_status
      `;

      const [areaStats] = await connection.execute(areaQuery, [userId, userId]);

      // 格式化健康状态数据
      const healthData = {
        healthy: 0,
        sick: 0,
        quarantine: 0
      };

      healthStats.forEach((stat) => {
        healthData[stat.health_status] = stat.count;
      });

      const totalCount =
        healthData.healthy + healthData.sick + healthData.quarantine;

      // 格式化区域健康状态数据
      const areaHealthData = {};
      areaStats.forEach((stat) => {
        if (!areaHealthData[stat.area_location]) {
          areaHealthData[stat.area_location] = {
            healthy: 0,
            sick: 0,
            quarantine: 0
          };
        }
        areaHealthData[stat.area_location][stat.health_status] = stat.count;
      });

      await connection.end();

      res.json({
        success: true,
        data: {
          overview: {
            totalCount: totalCount,
            healthyCount: healthData.healthy,
            healthyPercent:
              totalCount > 0
                ? ((healthData.healthy / totalCount) * 100).toFixed(1)
                : 0,
            sickCount: healthData.sick,
            sickPercent:
              totalCount > 0
                ? ((healthData.sick / totalCount) * 100).toFixed(1)
                : 0,
            quarantineCount: healthData.quarantine,
            quarantinePercent:
              totalCount > 0
                ? ((healthData.quarantine / totalCount) * 100).toFixed(1)
                : 0
          },
          areaDistribution: Object.keys(areaHealthData).map((area) => ({
            area: area,
            ...areaHealthData[area],
            total:
              areaHealthData[area].healthy +
              areaHealthData[area].sick +
              areaHealthData[area].quarantine
          }))
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取健康状态分析失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取健康状态分析失败',
        error: error.message
      });
    }
  }

  /**
   * 获取预警信息
   */
  async getAlerts(req, res) {
    try {
      const userId = req.user.id;
      const { level, unresolved } = req.query;
      const connection = await mysql.createConnection(dbConfig);

      // 构建查询条件
      let whereClause = 'WHERE user_id = ?';
      const queryParams = [userId];

      if (level) {
        whereClause += ' AND level = ?';
        queryParams.push(level);
      }

      if (unresolved === 'true') {
        whereClause += ' AND is_resolved = FALSE';
      }

      const query = `
        SELECT 
          id,
          alert_type,
          title,
          description,
          level,
          area_location,
          is_resolved,
          resolved_at,
          created_at,
          updated_at
        FROM inventory_alerts 
        ${whereClause}
        ORDER BY 
          CASE level 
            WHEN 'error' THEN 1 
            WHEN 'warning' THEN 2 
            WHEN 'info' THEN 3 
          END,
          created_at DESC
      `;

      const [alerts] = await connection.execute(query, queryParams);

      // 格式化预警数据
      const formattedAlerts = alerts.map((alert) => ({
        id: alert.id,
        alertType: alert.alert_type,
        alertTypeText: this.getAlertTypeText(alert.alert_type),
        title: alert.title,
        description: alert.description,
        level: alert.level,
        levelText: this.getLevelText(alert.level),
        areaLocation: alert.area_location,
        isResolved: Boolean(alert.is_resolved),
        resolvedAt: alert.resolved_at,
        createTime: alert.created_at,
        updateTime: alert.updated_at
      }));

      await connection.end();

      res.json({
        success: true,
        data: {
          alerts: formattedAlerts,
          summary: {
            total: formattedAlerts.length,
            unresolved: formattedAlerts.filter((a) => !a.isResolved).length,
            error: formattedAlerts.filter((a) => a.level === 'error').length,
            warning: formattedAlerts.filter((a) => a.level === 'warning')
              .length,
            info: formattedAlerts.filter((a) => a.level === 'info').length
          }
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取预警信息失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取预警信息失败',
        error: error.message
      });
    }
  }

  // 辅助方法
  getCategoryText(groupBy, category) {
    if (groupBy === 'age') {
      const ageMap = {
        gosling: '雏鹅',
        young: '青年鹅',
        adult: '成年鹅'
      };
      return ageMap[category] || category;
    }
    return category;
  }

  getAlertTypeText(alertType) {
    const typeMap = {
      density: '密度预警',
      health: '健康预警',
      vaccination: '疫苗预警',
      environment: '环境预警'
    };
    return typeMap[alertType] || alertType;
  }

  getLevelText(level) {
    const levelMap = {
      info: '信息',
      warning: '警告',
      error: '错误'
    };
    return levelMap[level] || level;
  }

  /**
   * 获取库存记录列表
   */
  async getRecords(req, res) {
    try {
      const userId = req.user.id;
      const {
        breed_name,
        age_category,
        area_location,
        health_status,
        page = 1,
        limit = 10
      } = req.query;

      const connection = await mysql.createConnection(dbConfig);

      // 构建查询条件
      let whereClause = 'WHERE user_id = ?';
      const queryParams = [userId];

      if (breed_name) {
        whereClause += ' AND breed_name = ?';
        queryParams.push(breed_name);
      }

      if (age_category) {
        whereClause += ' AND age_category = ?';
        queryParams.push(age_category);
      }

      if (area_location) {
        whereClause += ' AND area_location = ?';
        queryParams.push(area_location);
      }

      if (health_status) {
        whereClause += ' AND health_status = ?';
        queryParams.push(health_status);
      }

      // 分页
      const offset = (parseInt(page) - 1) * parseInt(limit);
      const limitClause = `LIMIT ${parseInt(limit)} OFFSET ${offset}`;

      // 查询库存记录
      const query = `
        SELECT * FROM inventory_records
        ${whereClause}
        ORDER BY record_date DESC, created_at DESC
        ${limitClause}
      `;

      const [records] = await connection.execute(query, queryParams);

      // 查询总数
      const countQuery = `SELECT COUNT(*) as total FROM inventory_records ${whereClause}`;
      const [countResult] = await connection.execute(countQuery, queryParams);
      const total = countResult[0].total;

      await connection.end();

      res.json({
        success: true,
        data: {
          records: records,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            totalPages: Math.ceil(total / parseInt(limit))
          }
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取库存记录列表失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取库存记录列表失败',
        error: error.message
      });
    }
  }

  /**
   * 创建库存记录
   */
  async createRecord(req, res) {
    try {
      const userId = req.user.id;
      const {
        breed_name,
        age_category,
        area_location,
        count,
        health_status = 'healthy',
        record_date
      } = req.body;

      const connection = await mysql.createConnection(dbConfig);

      const query = `
        INSERT INTO inventory_records (user_id, breed_name, age_category, area_location, count, health_status, record_date)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;

      const recordDate = record_date ? new Date(record_date) : new Date();

      const [result] = await connection.execute(query, [
        userId,
        breed_name,
        age_category,
        area_location,
        count,
        health_status,
        recordDate
      ]);

      // 获取创建的记录详情
      const [newRecord] = await connection.execute(
        'SELECT * FROM inventory_records WHERE id = ?',
        [result.insertId]
      );

      await connection.end();

      res.status(201).json({
        success: true,
        message: '库存记录创建成功',
        data: {
          id: result.insertId,
          ...newRecord[0]
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('创建库存记录失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '创建库存记录失败',
        error: error.message
      });
    }
  }

  /**
   * 获取库存统计信息
   */
  async getStatistics(req, res) {
    try {
      const userId = req.user.id;
      const connection = await mysql.createConnection(dbConfig);

      // 获取各种统计数据
      const queries = {
        // 总体统计
        overview: `
          SELECT
            SUM(count) as total_count,
            COUNT(DISTINCT breed_name) as breed_count,
            COUNT(DISTINCT area_location) as area_count,
            AVG(count) as avg_count_per_record
          FROM inventory_records
          WHERE user_id = ? AND record_date = (
            SELECT MAX(record_date) FROM inventory_records WHERE user_id = ?
          )
        `,
        // 健康状态统计
        health: `
          SELECT
            health_status,
            SUM(count) as count,
            ROUND(SUM(count) * 100.0 / (
              SELECT SUM(count) FROM inventory_records
              WHERE user_id = ? AND record_date = (
                SELECT MAX(record_date) FROM inventory_records WHERE user_id = ?
              )
            ), 1) as percentage
          FROM inventory_records
          WHERE user_id = ? AND record_date = (
            SELECT MAX(record_date) FROM inventory_records WHERE user_id = ?
          )
          GROUP BY health_status
        `,
        // 品种统计
        breeds: `
          SELECT
            breed_name,
            SUM(count) as count
          FROM inventory_records
          WHERE user_id = ? AND record_date = (
            SELECT MAX(record_date) FROM inventory_records WHERE user_id = ?
          )
          GROUP BY breed_name
          ORDER BY count DESC
          LIMIT 5
        `
      };

      const [overviewResult] = await connection.execute(queries.overview, [
        userId,
        userId
      ]);
      const [healthResult] = await connection.execute(queries.health, [
        userId,
        userId,
        userId,
        userId
      ]);
      const [breedsResult] = await connection.execute(queries.breeds, [
        userId,
        userId
      ]);

      await connection.end();

      res.json({
        success: true,
        data: {
          overview: overviewResult[0],
          healthDistribution: healthResult,
          topBreeds: breedsResult
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取库存统计失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取库存统计失败',
        error: error.message
      });
    }
  }

  /**
   * 解决预警
   */
  async resolveAlert(req, res) {
    try {
      const userId = req.user.id;
      const alertId = req.params.id;

      const connection = await mysql.createConnection(dbConfig);

      // 检查预警是否存在
      const [existingAlerts] = await connection.execute(
        'SELECT id FROM inventory_alerts WHERE id = ? AND user_id = ?',
        [alertId, userId]
      );

      if (existingAlerts.length === 0) {
        await connection.end();
        return res.status(404).json({
          success: false,
          message: '预警不存在'
        });
      }

      // 更新预警状态
      const query = `
        UPDATE inventory_alerts
        SET is_resolved = TRUE, resolved_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND user_id = ?
      `;

      await connection.execute(query, [alertId, userId]);
      await connection.end();

      res.json({
        success: true,
        message: '预警已解决'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('解决预警失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '解决预警失败',
        error: error.message
      });
    }
  }

  // =================== 统一库存管理核心CRUD方法 ===================

  /**
   * 获取库存项目列表 - 统一库存管理接口
   */
  async getInventoryItems(req, res) {
    try {
      const userId = req.user.id;
      const {
        page = 1,
        limit = 10,
        category,
        status = 'active',
        search,
        orderBy = 'updatedAt',
        order = 'DESC'
      } = req.query;

      // 构建查询条件
      const whereCondition = { userId };

      if (category && category !== 'all') {
        whereCondition.category = category;
      }

      if (status && status !== 'all') {
        whereCondition.status = status;
      }

      if (search) {
        whereCondition[Op.or] = [
          { name: { [Op.like]: `%${search}%` } },
          { description: { [Op.like]: `%${search}%` } }
        ];
      }

      // 分页查询
      const offset = (page - 1) * parseInt(limit);
      const { count, rows } = await UnifiedInventory.findAndCountAll({
        where: whereCondition,
        order: [[orderBy, order.toUpperCase()]],
        limit: parseInt(limit),
        offset: offset
      });

      res.json({
        success: true,
        data: {
          items: rows,
          pagination: {
            current: parseInt(page),
            pageSize: parseInt(limit),
            total: count,
            totalPages: Math.ceil(count / parseInt(limit))
          }
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取库存项目列表失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取库存项目列表失败',
        error: error.message
      });
    }
  }

  /**
   * 创建库存项目 - 统一库存管理接口
   */
  async createInventoryItem(req, res) {
    try {
      const userId = req.user.id;
      const itemData = {
        ...req.body,
        userId,
        createdBy: userId,
        updatedBy: userId
      };

      // 数据验证
      if (!itemData.name || !itemData.category) {
        return res.status(400).json({
          success: false,
          message: '物料名称和类别为必填项'
        });
      }

      // 创建库存项目
      const newItem = await UnifiedInventory.create(itemData);

      res.status(201).json({
        success: true,
        data: newItem,
        message: '库存项目创建成功'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('创建库存项目失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '创建库存项目失败',
        error: error.message
      });
    }
  }

  /**
   * 根据ID获取库存项目 - 统一库存管理接口
   */
  async getInventoryItemById(req, res) {
    try {
      const userId = req.user.id;
      const { id } = req.params;

      const item = await UnifiedInventory.findOne({
        where: { id, userId }
      });

      if (!item) {
        return res.status(404).json({
          success: false,
          message: '库存项目不存在'
        });
      }

      res.json({
        success: true,
        data: item
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取库存项目失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取库存项目失败',
        error: error.message
      });
    }
  }

  /**
   * 更新库存项目 - 统一库存管理接口
   */
  async updateInventoryItem(req, res) {
    try {
      const userId = req.user.id;
      const { id } = req.params;
      const updateData = {
        ...req.body,
        updatedBy: userId
      };

      const [updatedRowsCount] = await UnifiedInventory.update(updateData, {
        where: { id, userId }
      });

      if (updatedRowsCount === 0) {
        return res.status(404).json({
          success: false,
          message: '库存项目不存在或无权限修改'
        });
      }

      // 获取更新后的数据
      const updatedItem = await UnifiedInventory.findOne({
        where: { id, userId }
      });

      res.json({
        success: true,
        data: updatedItem,
        message: '库存项目更新成功'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('更新库存项目失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '更新库存项目失败',
        error: error.message
      });
    }
  }

  /**
   * 删除库存项目 - 统一库存管理接口
   */
  async deleteInventoryItem(req, res) {
    try {
      const userId = req.user.id;
      const { id } = req.params;

      const deletedRowsCount = await UnifiedInventory.destroy({
        where: { id, userId }
      });

      if (deletedRowsCount === 0) {
        return res.status(404).json({
          success: false,
          message: '库存项目不存在或无权限删除'
        });
      }

      res.json({
        success: true,
        message: '库存项目删除成功'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('删除库存项目失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '删除库存项目失赅',
        error: error.message
      });
    }
  }

  /**
   * 获取物料列表 - 为production.controller.js提供统一的materials API
   */
  async getMaterials(req, res) {
    try {
      const userId = req.user.id;
      const {
        category,
        search,
        status,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        page = 1,
        limit = 10
      } = req.query;

      // 构建查询条件
      const whereConditions = { userId };

      if (category) {
        whereConditions.category = category;
      }

      if (status) {
        whereConditions.status = status;
      }

      // 搜索条件
      if (search) {
        whereConditions[Op.or] = [
          { name: { [Op.like]: `%${search}%` } },
          { supplier: { [Op.like]: `%${search}%` } },
          { batchNumber: { [Op.like]: `%${search}%` } }
        ];
      }

      // 排序配置
      const validSortFields = [
        'name',
        'currentStock',
        'createdAt',
        'updatedAt'
      ];
      const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
      const order = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

      // 分页
      const offset = (parseInt(page) - 1) * parseInt(limit);

      // 查询数据
      const { count, rows: materials } = await UnifiedInventory.findAndCountAll(
        {
          where: whereConditions,
          limit: parseInt(limit),
          offset: offset,
          order: [[sortField, order]]
        }
      );

      // 格式化物料数据
      const formattedMaterials = materials.map((material) => ({
        id: material.id,
        name: material.name,
        category: material.category,
        categoryText: this.getCategoryText(material.category),
        specification: material.specification,
        unit: material.unit,
        supplier: material.supplier,
        currentStock: material.currentStock,
        minStock: material.minStock,
        maxStock: material.maxStock,
        unitPrice: material.unitPrice,
        totalValue: material.totalValue,
        status: material.status,
        statusText: this.getStatusText(material.status),
        location: material.location,
        batchNumber: material.batchNumber,
        purchaseDate: material.purchaseDate,
        expiryDate: material.expiryDate,
        createTime: material.createdAt,
        updateTime: material.updatedAt
      }));

      res.json({
        success: true,
        data: {
          materials: formattedMaterials,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count,
            totalPages: Math.ceil(count / parseInt(limit))
          }
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取物料列表失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取物料列表失败',
        error: error.message
      });
    }
  }

  /**
   * 创建物料
   */
  async createMaterial(req, res) {
    try {
      const userId = req.user.id;
      const materialData = {
        ...req.body,
        userId,
        createdBy: userId
      };

      const material = await UnifiedInventory.create(materialData);

      res.status(201).json({
        success: true,
        data: {
          material: {
            id: material.id,
            name: material.name,
            category: material.category,
            currentStock: material.currentStock,
            status: material.status,
            createTime: material.createdAt
          }
        },
        message: '物料创建成功'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('创建物料失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '创建物料失败',
        error: error.message
      });
    }
  }

  /**
   * 更新物料
   */
  async updateMaterial(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      const updateData = {
        ...req.body,
        updatedBy: userId
      };

      const [updateCount] = await UnifiedInventory.update(updateData, {
        where: { id, userId }
      });

      if (updateCount === 0) {
        return res.status(404).json({
          success: false,
          message: '物料不存在或无权限更新'
        });
      }

      const material = await UnifiedInventory.findOne({
        where: { id, userId }
      });

      res.json({
        success: true,
        data: { material },
        message: '物料更新成功'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('更新物料失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '更新物料失败',
        error: error.message
      });
    }
  }

  /**
   * 删除物料
   */
  async deleteMaterial(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const deleteCount = await UnifiedInventory.destroy({
        where: { id, userId }
      });

      if (deleteCount === 0) {
        return res.status(404).json({
          success: false,
          message: '物料不存在或无权限删除'
        });
      }

      res.json({
        success: true,
        message: '物料删除成功'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('删除物料失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '删除物料失败',
        error: error.message
      });
    }
  }

  /**
   * Helper方法
   */
  getCategoryText(category) {
    const categoryMap = {
      feed: '饴料',
      medicine: '药品',
      equipment: '设备',
      materials: '物料',
      other: '其他'
    };
    return categoryMap[category] || category;
  }

  getStatusText(status) {
    const statusMap = {
      normal: '正常',
      low_stock: '库存不足',
      out_of_stock: '缺货',
      expired: '过期',
      warning: '预警',
      inactive: '停用'
    };
    return statusMap[status] || status;
  }
}

module.exports = new InventoryController();
