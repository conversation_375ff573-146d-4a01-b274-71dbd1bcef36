/**
 * 任务管理控制器
 * 处理任务相关的业务逻辑
 */

const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USERNAME || 'zhihuiyange',
  password: process.env.DB_PASSWORD || 'zhihuiyange123',
  database: process.env.DB_NAME || 'zhihuiyange_local',
  charset: 'utf8mb4'
};

class TaskController {
  /**
   * 获取任务列表
   * 支持筛选、搜索、排序、分页
   */
  async getTasks(req, res) {
    try {
      const userId = req.user.id;
      const {
        status,
        priority,
        category,
        search,
        sortBy = 'created_at',
        sortOrder = 'desc',
        page = 1,
        limit = 10
      } = req.query;

      const connection = await mysql.createConnection(dbConfig);

      // 构建查询条件
      let whereClause = 'WHERE user_id = ?';
      const queryParams = [userId];

      if (status) {
        whereClause += ' AND status = ?';
        queryParams.push(status);
      }

      if (priority) {
        whereClause += ' AND priority = ?';
        queryParams.push(priority);
      }

      if (category) {
        whereClause += ' AND category = ?';
        queryParams.push(category);
      }

      if (search) {
        whereClause +=
          ' AND (title LIKE ? OR description LIKE ? OR assignee LIKE ?)';
        const searchPattern = `%${search}%`;
        queryParams.push(searchPattern, searchPattern, searchPattern);
      }

      // 构建排序
      const validSortFields = [
        'deadline',
        'priority',
        'created_at',
        'status',
        'title'
      ];
      const sortField = validSortFields.includes(sortBy)
        ? sortBy
        : 'created_at';
      const order = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

      // 优先级排序特殊处理
      let orderClause;
      if (sortField === 'priority') {
        orderClause = `ORDER BY FIELD(priority, 'high', 'medium', 'low') ${order}`;
      } else if (sortField === 'status') {
        orderClause = `ORDER BY FIELD(status, 'pending', 'processing', 'overdue', 'completed') ${order}`;
      } else {
        orderClause = `ORDER BY ${sortField} ${order}`;
      }

      // 分页
      const offset = (parseInt(page) - 1) * parseInt(limit);
      const limitClause = `LIMIT ${parseInt(limit)} OFFSET ${offset}`;

      // 查询任务列表
      const query = `
        SELECT 
          id,
          title,
          description,
          status,
          priority,
          category,
          assignee,
          deadline,
          created_at,
          updated_at
        FROM tasks 
        ${whereClause} 
        ${orderClause} 
        ${limitClause}
      `;

      const [tasks] = await connection.execute(query, queryParams);

      // 查询总数
      const countQuery = `SELECT COUNT(*) as total FROM tasks ${whereClause}`;
      const [countResult] = await connection.execute(countQuery, queryParams);
      const total = countResult[0].total;

      // 格式化任务数据
      const formattedTasks = tasks.map((task) => ({
        id: task.id,
        title: task.title,
        description: task.description,
        status: task.status,
        statusText: this.getStatusText(task.status),
        priority: task.priority,
        priorityText: this.getPriorityText(task.priority),
        category: task.category,
        assignee: task.assignee,
        deadline: task.deadline ? this.formatDeadline(task.deadline) : null,
        createTime: task.created_at,
        updateTime: task.updated_at
      }));

      await connection.end();

      res.json({
        success: true,
        data: {
          tasks: formattedTasks,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            totalPages: Math.ceil(total / parseInt(limit))
          }
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取任务列表失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取任务列表失败',
        error: error.message
      });
    }
  }

  /**
   * 创建新任务
   */
  async createTask(req, res) {
    try {
      const userId = req.user.id;
      const {
        title,
        description,
        priority = 'medium',
        category,
        assignee,
        deadline
      } = req.body;

      const connection = await mysql.createConnection(dbConfig);

      const query = `
        INSERT INTO tasks (user_id, title, description, priority, category, assignee, deadline)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;

      const [result] = await connection.execute(query, [
        userId,
        title,
        description,
        priority,
        category,
        assignee,
        deadline
      ]);

      // 获取创建的任务详情
      const [newTask] = await connection.execute(
        'SELECT * FROM tasks WHERE id = ?',
        [result.insertId]
      );

      await connection.end();

      res.status(201).json({
        success: true,
        message: '任务创建成功',
        data: {
          id: result.insertId,
          ...newTask[0]
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('创建任务失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '创建任务失败',
        error: error.message
      });
    }
  }

  /**
   * 获取任务统计信息
   */
  async getTaskStatistics(req, res) {
    try {
      const userId = req.user.id;
      const connection = await mysql.createConnection(dbConfig);

      const query = `
        SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
          SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
          SUM(CASE WHEN status = 'overdue' THEN 1 ELSE 0 END) as overdue
        FROM tasks 
        WHERE user_id = ?
      `;

      const [result] = await connection.execute(query, [userId]);
      await connection.end();

      res.json({
        success: true,
        data: result[0]
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取任务统计失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取任务统计失败',
        error: error.message
      });
    }
  }

  /**
   * 获取任务详情
   */
  async getTaskById(req, res) {
    try {
      const userId = req.user.id;
      const taskId = req.params.id;

      const connection = await mysql.createConnection(dbConfig);

      const query = `
        SELECT * FROM tasks 
        WHERE id = ? AND user_id = ?
      `;

      const [tasks] = await connection.execute(query, [taskId, userId]);

      if (tasks.length === 0) {
        await connection.end();
        return res.status(404).json({
          success: false,
          message: '任务不存在'
        });
      }

      await connection.end();

      const task = tasks[0];
      res.json({
        success: true,
        data: {
          ...task,
          statusText: this.getStatusText(task.status),
          priorityText: this.getPriorityText(task.priority)
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取任务详情失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取任务详情失败',
        error: error.message
      });
    }
  }

  /**
   * 更新任务状态
   */
  async updateTaskStatus(req, res) {
    try {
      const userId = req.user.id;
      const taskId = req.params.id;
      const { status } = req.body;

      const validStatuses = ['pending', 'processing', 'completed', 'overdue'];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({
          success: false,
          message: '无效的任务状态'
        });
      }

      const connection = await mysql.createConnection(dbConfig);

      // 检查任务是否存在
      const [existingTasks] = await connection.execute(
        'SELECT id FROM tasks WHERE id = ? AND user_id = ?',
        [taskId, userId]
      );

      if (existingTasks.length === 0) {
        await connection.end();
        return res.status(404).json({
          success: false,
          message: '任务不存在'
        });
      }

      // 更新状态
      const query = `
        UPDATE tasks 
        SET status = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ? AND user_id = ?
      `;

      await connection.execute(query, [status, taskId, userId]);
      await connection.end();

      res.json({
        success: true,
        message: '任务状态更新成功'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('更新任务状态失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '更新任务状态失败',
        error: error.message
      });
    }
  }

  // 辅助方法
  getStatusText(status) {
    const statusMap = {
      pending: '待处理',
      processing: '进行中',
      completed: '已完成',
      overdue: '已逾期'
    };
    return statusMap[status] || status;
  }

  getPriorityText(priority) {
    const priorityMap = {
      low: '低',
      medium: '中',
      high: '高'
    };
    return priorityMap[priority] || priority;
  }

  formatDeadline(deadline) {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diffTime = deadlineDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return (
        '今天 ' +
        deadlineDate.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      );
    } else if (diffDays === 1) {
      return (
        '明天 ' +
        deadlineDate.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      );
    } else if (diffDays === -1) {
      return (
        '昨天 ' +
        deadlineDate.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      );
    } else if (diffDays > 1) {
      return `${diffDays}天后`;
    } else {
      return `${Math.abs(diffDays)}天前`;
    }
  }

  /**
   * 更新任务信息
   */
  async updateTask(req, res) {
    try {
      const userId = req.user.id;
      const taskId = req.params.id;
      const { title, description, priority, category, assignee, deadline } =
        req.body;

      const connection = await mysql.createConnection(dbConfig);

      // 检查任务是否存在
      const [existingTasks] = await connection.execute(
        'SELECT id FROM tasks WHERE id = ? AND user_id = ?',
        [taskId, userId]
      );

      if (existingTasks.length === 0) {
        await connection.end();
        return res.status(404).json({
          success: false,
          message: '任务不存在'
        });
      }

      // 更新任务
      const query = `
        UPDATE tasks
        SET title = ?, description = ?, priority = ?, category = ?, assignee = ?, deadline = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND user_id = ?
      `;

      await connection.execute(query, [
        title,
        description,
        priority,
        category,
        assignee,
        deadline,
        taskId,
        userId
      ]);

      await connection.end();

      res.json({
        success: true,
        message: '任务更新成功'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('更新任务失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '更新任务失败',
        error: error.message
      });
    }
  }

  /**
   * 删除任务
   */
  async deleteTask(req, res) {
    try {
      const userId = req.user.id;
      const taskId = req.params.id;

      const connection = await mysql.createConnection(dbConfig);

      // 检查任务是否存在
      const [existingTasks] = await connection.execute(
        'SELECT id FROM tasks WHERE id = ? AND user_id = ?',
        [taskId, userId]
      );

      if (existingTasks.length === 0) {
        await connection.end();
        return res.status(404).json({
          success: false,
          message: '任务不存在'
        });
      }

      // 删除任务
      await connection.execute(
        'DELETE FROM tasks WHERE id = ? AND user_id = ?',
        [taskId, userId]
      );

      await connection.end();

      res.json({
        success: true,
        message: '任务删除成功'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('删除任务失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '删除任务失败',
        error: error.message
      });
    }
  }

  /**
   * 批量操作任务
   */
  async batchOperation(req, res) {
    try {
      const userId = req.user.id;
      const { action, taskIds, status } = req.body;

      if (!Array.isArray(taskIds) || taskIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请选择要操作的任务'
        });
      }

      const connection = await mysql.createConnection(dbConfig);

      if (action === 'updateStatus') {
        const validStatuses = ['pending', 'processing', 'completed', 'overdue'];
        if (!validStatuses.includes(status)) {
          await connection.end();
          return res.status(400).json({
            success: false,
            message: '无效的任务状态'
          });
        }

        const placeholders = taskIds.map(() => '?').join(',');
        const query = `
          UPDATE tasks
          SET status = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id IN (${placeholders}) AND user_id = ?
        `;

        await connection.execute(query, [status, ...taskIds, userId]);

        await connection.end();
        res.json({
          success: true,
          message: `成功更新 ${taskIds.length} 个任务的状态`
        });
      } else if (action === 'delete') {
        const placeholders = taskIds.map(() => '?').join(',');
        const query = `
          DELETE FROM tasks
          WHERE id IN (${placeholders}) AND user_id = ?
        `;

        const [result] = await connection.execute(query, [...taskIds, userId]);

        await connection.end();
        res.json({
          success: true,
          message: `成功删除 ${result.affectedRows} 个任务`
        });
      } else {
        await connection.end();
        res.status(400).json({
          success: false,
          message: '不支持的操作类型'
        });
      }
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('批量操作失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '批量操作失败',
        error: error.message
      });
    }
  }

  /**
   * 获取任务分类列表
   */
  async getTaskCategories(req, res) {
    try {
      const userId = req.user.id;
      const connection = await mysql.createConnection(dbConfig);

      const query = `
        SELECT DISTINCT category
        FROM tasks
        WHERE user_id = ? AND category IS NOT NULL AND category != ''
        ORDER BY category
      `;

      const [categories] = await connection.execute(query, [userId]);
      await connection.end();

      const categoryList = categories.map((row) => row.category);

      res.json({
        success: true,
        data: categoryList
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取任务分类失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取任务分类失败',
        error: error.message
      });
    }
  }

  /**
   * 完成任务（快捷操作）
   */
  async completeTask(req, res) {
    try {
      const userId = req.user.id;
      const taskId = req.params.id;

      const connection = await mysql.createConnection(dbConfig);

      const query = `
        UPDATE tasks
        SET status = 'completed', updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND user_id = ?
      `;

      const [result] = await connection.execute(query, [taskId, userId]);

      if (result.affectedRows === 0) {
        await connection.end();
        return res.status(404).json({
          success: false,
          message: '任务不存在'
        });
      }

      await connection.end();

      res.json({
        success: true,
        message: '任务已完成'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('完成任务失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '完成任务失败',
        error: error.message
      });
    }
  }

  /**
   * 重启任务（快捷操作）
   */
  async restartTask(req, res) {
    try {
      const userId = req.user.id;
      const taskId = req.params.id;

      const connection = await mysql.createConnection(dbConfig);

      const query = `
        UPDATE tasks
        SET status = 'pending', updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND user_id = ?
      `;

      const [result] = await connection.execute(query, [taskId, userId]);

      if (result.affectedRows === 0) {
        await connection.end();
        return res.status(404).json({
          success: false,
          message: '任务不存在'
        });
      }

      await connection.end();

      res.json({
        success: true,
        message: '任务已重启'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('重启任务失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '重启任务失败',
        error: error.message
      });
    }
  }
}

module.exports = new TaskController();
