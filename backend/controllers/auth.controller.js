const User = require('../models/user.model');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// 用户注册
exports.register = async (req, res) => {
  try {
    const { username, password, name, farmName, phone, email } = req.body;

    // 检查用户名是否已存在
    const existingUser = await User.findOne({ where: { username } });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名已存在'
      });
    }

    // 密码加密
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 创建新用户
    const user = await User.create({
      username,
      password: hashedPassword,
      name,
      farmName,
      phone,
      email
    });

    // 生成JWT令牌
    const token = jwt.sign(
      { id: user.id, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // 返回不包含密码的用户信息
    const userData = {
      id: user.id,
      username: user.username,
      name: user.name,
      farmName: user.farmName,
      phone: user.phone,
      email: user.email,
      role: user.role
    };

    res.status(201).json({
      success: true,
      message: '用户注册成功',
      data: {
        user: userData,
        token
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 用户登录
exports.login = async (req, res) => {
  try {
    const { username, password } = req.body;

    // 查找用户
    const user = await User.findOne({ where: { username } });
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 生成JWT令牌
    const token = jwt.sign(
      { id: user.id, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // 返回不包含密码的用户信息
    const userData = {
      id: user.id,
      username: user.username,
      name: user.name,
      farmName: user.farmName,
      phone: user.phone,
      email: user.email,
      role: user.role
    };

    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: userData,
        token
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 用户登出
exports.logout = async (req, res) => {
  try {
    // 在实际应用中，这里可以将token加入黑名单
    // 目前只是简单返回成功消息
    res.json({
      success: true,
      message: '登出成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 刷新访问令牌
exports.refreshToken = async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '缺少刷新令牌'
      });
    }

    // 验证令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // 生成新的访问令牌
    const newToken = jwt.sign(
      { id: decoded.id, username: decoded.username },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    res.json({
      success: true,
      message: '令牌刷新成功',
      data: {
        token: newToken
      }
    });
  } catch (error) {
    res.status(401).json({
      success: false,
      message: '令牌无效或已过期'
    });
  }
};

// 忘记密码
exports.forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    // 查找用户
    const user = await User.findOne({ where: { email } });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 在实际应用中，这里应该发送重置密码邮件
    // 目前只是返回成功消息
    res.json({
      success: true,
      message: '密码重置邮件已发送'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 重置密码
exports.resetPassword = async (req, res) => {
  try {
    const { token, newPassword } = req.body;

    // 在实际应用中，这里应该验证重置令牌
    // 目前只是简单的密码更新逻辑

    res.json({
      success: true,
      message: '密码重置成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 获取用户信息
exports.getUserInfo = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 修改密码
exports.changePassword = async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;
    const userId = req.user.id;

    // 查找用户
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 验证旧密码
    const isOldPasswordValid = await bcrypt.compare(oldPassword, user.password);
    if (!isOldPasswordValid) {
      return res.status(400).json({
        success: false,
        message: '原密码错误'
      });
    }

    // 加密新密码
    const saltRounds = 10;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // 更新密码
    await user.update({ password: hashedNewPassword });

    res.json({
      success: true,
      message: '密码修改成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 获取当前用户信息
exports.getCurrentUser = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 更新当前用户信息
exports.updateCurrentUser = async (req, res) => {
  try {
    const { name, farmName, phone, email } = req.body;
    const userId = req.user.id;

    // 查找用户
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 更新用户信息
    await user.update({
      name: name || user.name,
      farmName: farmName || user.farmName,
      phone: phone || user.phone,
      email: email || user.email
    });

    // 返回更新后的用户信息（不包含密码）
    const updatedUser = await User.findByPk(userId, {
      attributes: { exclude: ['password'] }
    });

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: updatedUser
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 验证令牌有效性
exports.verifyToken = async (req, res) => {
  try {
    // 如果能到达这里，说明令牌是有效的（已通过中间件验证）
    const user = await User.findByPk(req.user.id, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      message: '令牌有效',
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 微信登录
exports.wechatLogin = async (req, res) => {
  try {
    const { code, encryptedData, iv } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        message: '微信登录代码不能为空'
      });
    }

    // 这里应该调用微信API获取用户信息
    // 暂时模拟微信登录逻辑
    const mockWechatUser = {
      openid: `wx_${Date.now()}`,
      nickname: '微信用户',
      avatar: 'https://avatar.default.com/user.png'
    };

    // 查找或创建用户
    let user = await User.findOne({ where: { wechatOpenid: mockWechatUser.openid } });
    
    if (!user) {
      // 创建新用户
      user = await User.create({
        username: `wx_${mockWechatUser.openid.slice(-8)}`,
        name: mockWechatUser.nickname,
        wechatOpenid: mockWechatUser.openid,
        avatar: mockWechatUser.avatar,
        password: null // 微信用户可能没有密码
      });
    }

    // 生成JWT令牌
    const token = jwt.sign(
      { id: user.id, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    const userData = {
      id: user.id,
      username: user.username,
      name: user.name,
      avatar: user.avatar,
      wechatOpenid: user.wechatOpenid
    };

    res.json({
      success: true,
      message: '微信登录成功',
      data: { user: userData, token }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '微信登录失败',
      error: error.message
    });
  }
};

// 发送验证码
exports.sendVerifyCode = async (req, res) => {
  try {
    const { phone, type = 'register' } = req.body;

    if (!phone) {
      return res.status(400).json({
        success: false,
        message: '手机号不能为空'
      });
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return res.status(400).json({
        success: false,
        message: '手机号格式不正确'
      });
    }

    // 生成验证码
    const verifyCode = Math.floor(100000 + Math.random() * 900000).toString();
    
    // 这里应该调用短信服务发送验证码
    // 暂时模拟发送成功
    console.log(`发送验证码到 ${phone}: ${verifyCode}`);

    // 在实际项目中，应该将验证码存储到Redis或数据库中，设置过期时间
    // 这里暂时返回成功响应
    
    res.json({
      success: true,
      message: '验证码发送成功',
      data: {
        phone,
        // 开发环境下可以返回验证码，生产环境不要返回
        ...(process.env.NODE_ENV !== 'production' && { code: verifyCode })
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '发送验证码失败',
      error: error.message
    });
  }
};

// 获取用户资料
exports.getProfile = async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findByPk(userId, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户资料失败',
      error: error.message
    });
  }
};

// 更新用户资料
exports.updateProfile = async (req, res) => {
  try {
    const userId = req.user.id;
    const { name, farmName, phone, email, avatar } = req.body;

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 更新用户信息
    await user.update({
      name: name || user.name,
      farmName: farmName || user.farmName,
      phone: phone || user.phone,
      email: email || user.email,
      avatar: avatar || user.avatar
    });

    res.json({
      success: true,
      message: '用户资料更新成功',
      data: {
        id: user.id,
        username: user.username,
        name: user.name,
        farmName: user.farmName,
        phone: user.phone,
        email: user.email,
        avatar: user.avatar
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新用户资料失败',
      error: error.message
    });
  }
};
