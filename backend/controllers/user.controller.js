const User = require('../models/user.model');
const bcrypt = require('bcryptjs');
const { Op } = require('sequelize');
const sequelize = require('sequelize');
const ResponseHelper = require('../utils/response-helper');
const ValidationHelper = require('../utils/validation-helper');

// 获取用户信息
exports.getUserInfo = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 更新用户信息
exports.updateUserInfo = async (req, res) => {
  try {
    const { name, farmName, phone, email } = req.body;

    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 更新用户信息
    user.name = name || user.name;
    user.farmName = farmName || user.farmName;
    user.phone = phone || user.phone;
    user.email = email || user.email;

    await user.save();

    // 返回不包含密码的用户信息
    const userData = {
      id: user.id,
      username: user.username,
      name: user.name,
      farmName: user.farmName,
      phone: user.phone,
      email: user.email,
      role: user.role
    };

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: userData
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 获取用户列表
exports.getUsers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      role = '',
      status = '',
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = req.query;

    // 验证分页参数
    if (
      !ValidationHelper.validateNumber(page, 1) ||
      !ValidationHelper.validateNumber(limit, 1, 100)
    ) {
      return ResponseHelper.error(res, '分页参数无效', 400);
    }

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // 构建查询条件
    const whereConditions = {};

    // 搜索条件
    if (search) {
      whereConditions[Op.or] = [
        { username: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } },
        { name: { [Op.like]: `%${search}%` } },
        { farmName: { [Op.like]: `%${search}%` } },
        { phone: { [Op.like]: `%${search}%` } }
      ];
    }

    // 角色筛选
    if (role) {
      whereConditions.role = role;
    }

    // 状态筛选
    if (status) {
      whereConditions.status = status;
    }

    // 排序验证
    const allowedSortFields = [
      'createdAt',
      'username',
      'email',
      'name',
      'lastLoginAt'
    ];
    const sortField = allowedSortFields.includes(sortBy) ? sortBy : 'createdAt';
    const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const { count, rows } = await User.findAndCountAll({
      where: whereConditions,
      limit: parseInt(limit),
      offset: offset,
      attributes: { exclude: ['password'] },
      order: [[sortField, order]]
    });

    // 转换数据格式
    const users = rows.map((user) => ({
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      farmName: user.farmName,
      phone: user.phone,
      role: user.role,
      status: user.status || 'active',
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    }));

    const paginatedData = ResponseHelper.paginate(users, page, limit, count);
    return ResponseHelper.success(res, paginatedData, '获取用户列表成功');
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取用户列表失败:', error); } catch(_) {}

    return ResponseHelper.error(res, '获取用户列表失败', 500, error.message);
  }
};

// 创建用户
exports.createUser = async (req, res) => {
  try {
    const { username, password, email, role } = req.body;

    // 验证必填字段
    if (!username || !password || !email) {
      return res.status(400).json({
        success: false,
        message: '用户名、密码和邮箱为必填项'
      });
    }

    // 检查用户是否已存在
    const existingUser = await User.findOne({
      where: {
        [Sequelize.Op.or]: [{ username: username }, { email: email }]
      }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名或邮箱已存在'
      });
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10);

    // 创建用户
    const user = await User.create({
      username,
      password: hashedPassword,
      email,
      role: role || 'user'
    });

    // 返回不包含密码的用户信息
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.status(201).json({
      success: true,
      message: '用户创建成功',
      data: userData
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('创建用户失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取指定用户详情
exports.getUserById = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取用户详情失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 更新指定用户信息
exports.updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { username, email, role } = req.body;

    const user = await User.findByPk(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 更新用户信息
    await user.update({
      username: username || user.username,
      email: email || user.email,
      role: role || user.role
    });

    // 返回不包含密码的用户信息
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: userData
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('更新用户信息失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 部分更新指定用户信息
exports.patchUser = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const user = await User.findByPk(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 如果请求中包含密码，则加密处理
    if (updateData.password) {
      updateData.password = await bcrypt.hash(updateData.password, 10);
    }

    // 更新用户信息
    await user.update(updateData);

    // 返回不包含密码的用户信息
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: userData
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('部分更新用户信息失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 删除指定用户
exports.deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 删除用户
    await user.destroy();

    res.json({
      success: true,
      message: '用户删除成功'
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('删除用户失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取用户统计信息
exports.getUserStats = async (req, res) => {
  try {
    const totalUsers = await User.count();

    const stats = {
      totalUsers: totalUsers,
      adminUsers: await User.count({ where: { role: 'admin' } }),
      normalUsers: await User.count({ where: { role: 'user' } }),
      managerUsers: await User.count({ where: { role: 'manager' } })
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取用户统计信息失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 批量更新用户
exports.batchUpdateUsers = async (req, res) => {
  try {
    const { userIds, updateData } = req.body;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '用户ID列表不能为空'
      });
    }

    // 批量更新用户
    const [updatedRows] = await User.update(updateData, {
      where: {
        id: {
          [Sequelize.Op.in]: userIds
        }
      }
    });

    res.json({
      success: true,
      message: `成功更新${updatedRows}个用户`,
      data: {
        updatedRows: updatedRows
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('批量更新用户失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 批量删除用户
exports.batchDeleteUsers = async (req, res) => {
  try {
    const { userIds } = req.body;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '用户ID列表不能为空'
      });
    }

    // 批量删除用户
    const deletedRows = await User.destroy({
      where: {
        id: {
          [Sequelize.Op.in]: userIds
        }
      }
    });

    res.json({
      success: true,
      message: `成功删除${deletedRows}个用户`,
      data: {
        deletedRows: deletedRows
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('批量删除用户失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取用户角色
exports.getUserRoles = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: {
        userId: user.id,
        role: user.role
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取用户角色失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 更新用户角色
exports.updateUserRoles = async (req, res) => {
  try {
    const { id } = req.params;
    const { role } = req.body;

    const user = await User.findByPk(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 更新用户角色
    await user.update({ role });

    res.json({
      success: true,
      message: '用户角色更新成功',
      data: {
        userId: user.id,
        role: user.role
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('更新用户角色失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取用户权限
exports.getUserPermissions = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 根据用户角色确定权限
    let permissions = [];
    switch (user.role) {
    case 'admin':
      permissions = ['read', 'write', 'delete', 'manage_users'];
      break;
    case 'manager':
      permissions = ['read', 'write', 'delete'];
      break;
    case 'user':
      permissions = ['read'];
      break;
    default:
      permissions = ['read'];
    }

    res.json({
      success: true,
      data: {
        userId: user.id,
        role: user.role,
        permissions: permissions
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取用户权限失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 添加缺失的方法别名
exports.getUserDetail = exports.getUserById;
exports.getProfile = exports.getUserInfo;
exports.updateProfile = exports.updateUserInfo;
exports.createUser = exports.createUser; // 确保这个方法存在
