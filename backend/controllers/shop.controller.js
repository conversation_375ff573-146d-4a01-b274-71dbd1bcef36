const Product = require('../models/product.model');
const { Op, fn, col } = require('sequelize');

// 获取商品列表
exports.getProducts = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const category = req.query.category || '';
    const search = req.query.search || '';
    const offset = (page - 1) * limit;

    // 构建查询条件
    const whereClause = {
      status: {
        [Op.ne]: 'inactive'
      }
    };

    // 添加分类筛选
    if (category) {
      whereClause.category = category;
    }

    // 添加搜索条件
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows } = await Product.findAndCountAll({
      where: whereClause,
      limit: limit,
      offset: offset,
      order: [['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        products: rows,
        pagination: {
          page: page,
          limit: limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取商品列表失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取商品详情
exports.getProductById = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findByPk(id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }

    // 检查商品状态
    if (product.status === 'inactive') {
      return res.status(404).json({
        success: false,
        message: '商品已下架'
      });
    }

    res.json({
      success: true,
      data: product
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取商品详情失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 添加商品（管理员功能）
exports.createProduct = async (req, res) => {
  try {
    const {
      name,
      description,
      price,
      category,
      image,
      stock,
      tags,
      specifications
    } = req.body;

    // 验证必填字段
    if (!name || !price || !category) {
      return res.status(400).json({
        success: false,
        message: '商品名称、价格和分类为必填项'
      });
    }

    // 创建新商品
    const product = await Product.create({
      name,
      description: description || '',
      price: parseFloat(price),
      category,
      image: image || '/images/products/default.jpg',
      stock: parseInt(stock) || 0,
      tags: tags || [],
      specifications: specifications || {},
      createdBy: req.user.id,
      updatedBy: req.user.id
    });

    res.status(201).json({
      success: true,
      message: '商品添加成功',
      data: product
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('添加商品失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 更新商品（管理员功能）
exports.updateProduct = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findByPk(id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }

    // 更新商品信息
    const {
      name,
      description,
      price,
      category,
      image,
      stock,
      status,
      tags,
      specifications
    } = req.body;

    await product.update({
      name: name || product.name,
      description:
        description !== undefined ? description : product.description,
      price: price !== undefined ? parseFloat(price) : product.price,
      category: category || product.category,
      image: image || product.image,
      stock: stock !== undefined ? parseInt(stock) : product.stock,
      status: status || product.status,
      tags: tags || product.tags,
      specifications: specifications || product.specifications,
      updatedBy: req.user.id
    });

    res.json({
      success: true,
      message: '商品更新成功',
      data: product
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('更新商品失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 删除商品（管理员功能）
exports.deleteProduct = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findByPk(id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }

    // 软删除商品
    await product.destroy();

    res.json({
      success: true,
      message: '商品删除成功'
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('删除商品失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取商品分类
exports.getProductCategories = async (req, res) => {
  try {
    // 从商品表中获取所有唯一分类及其商品数量
    const categories = await Product.findAll({
      attributes: ['category', [fn('COUNT', col('id')), 'count']],
      where: {
        status: {
          [Op.ne]: 'inactive'
        }
      },
      group: ['category']
    });

    const categoryDetails = categories.map((category) => ({
      name: category.category,
      count: parseInt(category.get('count'))
    }));

    res.json({
      success: true,
      data: {
        categories: categoryDetails
      }
    });
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取商品分类失败:', error); } catch(_) {}

    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};
