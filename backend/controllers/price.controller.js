/**
 * 价格管理控制器
 * 处理价格相关的业务逻辑
 */

const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USERNAME || 'zhihuiyange',
  password: process.env.DB_PASSWORD || 'zhihuiyange123',
  database: process.env.DB_NAME || 'zhihuiyange_local',
  charset: 'utf8mb4'
};

class PriceController {
  /**
   * 获取价格趋势数据
   */
  async getPriceTrends(req, res) {
    try {
      const {
        breed_name,
        breed_type,
        timeRange = '30d',
        market_location
      } = req.query;

      const connection = await mysql.createConnection(dbConfig);

      // 计算时间范围
      let days = 30;
      if (timeRange === '7d') days = 7;
      else if (timeRange === '90d') days = 90;
      else if (timeRange === '1y') days = 365;

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      // 构建查询条件
      let whereClause = 'WHERE record_date >= ?';
      const queryParams = [startDate];

      if (breed_name) {
        whereClause += ' AND breed_name = ?';
        queryParams.push(breed_name);
      }

      if (breed_type) {
        whereClause += ' AND breed_type = ?';
        queryParams.push(breed_type);
      }

      if (market_location) {
        whereClause += ' AND market_location = ?';
        queryParams.push(market_location);
      }

      // 查询价格趋势数据
      const query = `
        SELECT 
          DATE(record_date) as date,
          AVG(price) as avg_price,
          MIN(price) as min_price,
          MAX(price) as max_price,
          AVG(change_rate) as avg_change_rate,
          COUNT(*) as record_count
        FROM price_records 
        ${whereClause}
        GROUP BY DATE(record_date)
        ORDER BY date ASC
      `;

      const [trends] = await connection.execute(query, queryParams);

      // 计算趋势指标
      const trendData = trends.map((trend, index) => {
        const prevTrend = index > 0 ? trends[index - 1] : null;
        const priceChange = prevTrend
          ? trend.avg_price - prevTrend.avg_price
          : 0;
        const priceChangePercent =
          prevTrend && prevTrend.avg_price > 0
            ? ((priceChange / prevTrend.avg_price) * 100).toFixed(2)
            : 0;

        return {
          date: trend.date,
          avgPrice: parseFloat(trend.avg_price.toFixed(2)),
          minPrice: parseFloat(trend.min_price.toFixed(2)),
          maxPrice: parseFloat(trend.max_price.toFixed(2)),
          priceChange: parseFloat(priceChange.toFixed(2)),
          priceChangePercent: parseFloat(priceChangePercent),
          avgChangeRate: trend.avg_change_rate
            ? parseFloat(trend.avg_change_rate.toFixed(2))
            : 0,
          recordCount: trend.record_count
        };
      });

      await connection.end();

      res.json({
        success: true,
        data: {
          trends: trendData,
          timeRange: timeRange,
          filters: {
            breedName: breed_name,
            breedType: breed_type,
            marketLocation: market_location
          }
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取价格趋势失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取价格趋势失败',
        error: error.message
      });
    }
  }

  /**
   * 获取价格统计信息
   */
  async getPriceStatistics(req, res) {
    try {
      const { breed_name, breed_type, timeRange = '30d' } = req.query;

      const connection = await mysql.createConnection(dbConfig);

      // 计算时间范围
      let days = 30;
      if (timeRange === '7d') days = 7;
      else if (timeRange === '90d') days = 90;
      else if (timeRange === '1y') days = 365;

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      // 构建查询条件
      let whereClause = 'WHERE record_date >= ?';
      const queryParams = [startDate];

      if (breed_name) {
        whereClause += ' AND breed_name = ?';
        queryParams.push(breed_name);
      }

      if (breed_type) {
        whereClause += ' AND breed_type = ?';
        queryParams.push(breed_type);
      }

      // 查询统计数据
      const statsQuery = `
        SELECT 
          COUNT(*) as total_records,
          AVG(price) as avg_price,
          MIN(price) as min_price,
          MAX(price) as max_price,
          STDDEV(price) as price_volatility,
          AVG(change_rate) as avg_change_rate,
          MIN(change_rate) as min_change_rate,
          MAX(change_rate) as max_change_rate
        FROM price_records 
        ${whereClause}
      `;

      const [stats] = await connection.execute(statsQuery, queryParams);

      // 查询最新价格
      const latestQuery = `
        SELECT price, change_rate, record_date
        FROM price_records 
        ${whereClause}
        ORDER BY record_date DESC, created_at DESC
        LIMIT 1
      `;

      const [latest] = await connection.execute(latestQuery, queryParams);

      // 查询价格分布
      const distributionQuery = `
        SELECT 
          CASE 
            WHEN price < 10 THEN '0-10'
            WHEN price < 15 THEN '10-15'
            WHEN price < 20 THEN '15-20'
            WHEN price < 25 THEN '20-25'
            ELSE '25+'
          END as price_range,
          COUNT(*) as count
        FROM price_records 
        ${whereClause}
        GROUP BY price_range
        ORDER BY price_range
      `;

      const [distribution] = await connection.execute(
        distributionQuery,
        queryParams
      );

      await connection.end();

      const statisticsData = stats[0];
      const latestPrice = latest[0];

      res.json({
        success: true,
        data: {
          overview: {
            totalRecords: statisticsData.total_records,
            avgPrice: statisticsData.avg_price
              ? parseFloat(statisticsData.avg_price.toFixed(2))
              : 0,
            minPrice: statisticsData.min_price
              ? parseFloat(statisticsData.min_price.toFixed(2))
              : 0,
            maxPrice: statisticsData.max_price
              ? parseFloat(statisticsData.max_price.toFixed(2))
              : 0,
            priceVolatility: statisticsData.price_volatility
              ? parseFloat(statisticsData.price_volatility.toFixed(2))
              : 0,
            avgChangeRate: statisticsData.avg_change_rate
              ? parseFloat(statisticsData.avg_change_rate.toFixed(2))
              : 0,
            minChangeRate: statisticsData.min_change_rate
              ? parseFloat(statisticsData.min_change_rate.toFixed(2))
              : 0,
            maxChangeRate: statisticsData.max_change_rate
              ? parseFloat(statisticsData.max_change_rate.toFixed(2))
              : 0
          },
          latest: latestPrice
            ? {
              price: parseFloat(latestPrice.price.toFixed(2)),
              changeRate: latestPrice.change_rate
                ? parseFloat(latestPrice.change_rate.toFixed(2))
                : 0,
              recordDate: latestPrice.record_date
            }
            : null,
          distribution: distribution.map((item) => ({
            priceRange: item.price_range,
            count: item.count,
            percentage: (
              (item.count / statisticsData.total_records) *
              100
            ).toFixed(1)
          })),
          timeRange: timeRange
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取价格统计失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取价格统计失败',
        error: error.message
      });
    }
  }

  /**
   * 获取相关品种价格
   */
  async getRelatedBreeds(req, res) {
    try {
      const { breed_name, breed_type } = req.query;
      const connection = await mysql.createConnection(dbConfig);

      // 获取最新日期
      const [latestDateResult] = await connection.execute(
        'SELECT MAX(record_date) as latest_date FROM price_records'
      );
      const latestDate = latestDateResult[0].latest_date;

      // 查询相关品种（同类型的其他品种）
      let query = `
        SELECT 
          breed_name,
          breed_type,
          AVG(price) as avg_price,
          AVG(change_rate) as avg_change_rate,
          COUNT(*) as record_count
        FROM price_records 
        WHERE record_date = ? AND breed_type = ?
      `;
      const queryParams = [latestDate, breed_type];

      if (breed_name) {
        query += ' AND breed_name != ?';
        queryParams.push(breed_name);
      }

      query += `
        GROUP BY breed_name, breed_type
        ORDER BY avg_price DESC
        LIMIT 10
      `;

      const [relatedBreeds] = await connection.execute(query, queryParams);

      // 格式化相关品种数据
      const formattedBreeds = relatedBreeds.map((breed) => ({
        breedName: breed.breed_name,
        breedType: breed.breed_type,
        breedTypeText: this.getBreedTypeText(breed.breed_type),
        avgPrice: parseFloat(breed.avg_price.toFixed(2)),
        avgChangeRate: breed.avg_change_rate
          ? parseFloat(breed.avg_change_rate.toFixed(2))
          : 0,
        recordCount: breed.record_count
      }));

      await connection.end();

      res.json({
        success: true,
        data: {
          relatedBreeds: formattedBreeds,
          referenceBreed: {
            breedName: breed_name,
            breedType: breed_type
          },
          latestDate: latestDate
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取相关品种价格失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取相关品种价格失败',
        error: error.message
      });
    }
  }

  /**
   * 订阅价格提醒
   */
  async subscribePriceAlert(req, res) {
    try {
      const userId = req.user.id;
      const { breed_name, breed_type, threshold_price } = req.body;

      if (!breed_name || !breed_type || !threshold_price) {
        return res.status(400).json({
          success: false,
          message: '品种名称、类型和阈值价格不能为空'
        });
      }

      const connection = await mysql.createConnection(dbConfig);

      // 检查是否已存在相同的订阅
      const [existingSubscriptions] = await connection.execute(
        'SELECT id FROM price_subscriptions WHERE user_id = ? AND breed_name = ? AND breed_type = ? AND is_active = TRUE',
        [userId, breed_name, breed_type]
      );

      if (existingSubscriptions.length > 0) {
        await connection.end();
        return res.status(400).json({
          success: false,
          message: '该品种的价格提醒已存在'
        });
      }

      // 创建订阅
      const query = `
        INSERT INTO price_subscriptions (user_id, breed_name, breed_type, threshold_price, is_active)
        VALUES (?, ?, ?, ?, TRUE)
      `;

      const [result] = await connection.execute(query, [
        userId,
        breed_name,
        breed_type,
        threshold_price
      ]);

      await connection.end();

      res.status(201).json({
        success: true,
        message: '价格提醒订阅成功',
        data: {
          subscriptionId: result.insertId,
          breedName: breed_name,
          breedType: breed_type,
          thresholdPrice: threshold_price
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('订阅价格提醒失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '订阅价格提醒失败',
        error: error.message
      });
    }
  }

  // 辅助方法
  getBreedTypeText(breedType) {
    const typeMap = {
      gosling: '鹅苗',
      adult: '成鹅'
    };
    return typeMap[breedType] || breedType;
  }

  /**
   * 获取用户的价格订阅列表
   */
  async getUserSubscriptions(req, res) {
    try {
      const userId = req.user.id;
      const connection = await mysql.createConnection(dbConfig);

      const query = `
        SELECT
          id,
          breed_name,
          breed_type,
          threshold_price,
          is_active,
          created_at,
          updated_at
        FROM price_subscriptions
        WHERE user_id = ?
        ORDER BY created_at DESC
      `;

      const [subscriptions] = await connection.execute(query, [userId]);

      // 格式化订阅数据
      const formattedSubscriptions = subscriptions.map((sub) => ({
        id: sub.id,
        breedName: sub.breed_name,
        breedType: sub.breed_type,
        breedTypeText: this.getBreedTypeText(sub.breed_type),
        thresholdPrice: parseFloat(sub.threshold_price),
        isActive: Boolean(sub.is_active),
        createTime: sub.created_at,
        updateTime: sub.updated_at
      }));

      await connection.end();

      res.json({
        success: true,
        data: {
          subscriptions: formattedSubscriptions,
          total: formattedSubscriptions.length,
          active: formattedSubscriptions.filter((s) => s.isActive).length
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取价格订阅列表失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取价格订阅列表失败',
        error: error.message
      });
    }
  }

  /**
   * 取消价格订阅
   */
  async cancelSubscription(req, res) {
    try {
      const userId = req.user.id;
      const subscriptionId = req.params.id;

      const connection = await mysql.createConnection(dbConfig);

      // 检查订阅是否存在
      const [existingSubscriptions] = await connection.execute(
        'SELECT id FROM price_subscriptions WHERE id = ? AND user_id = ?',
        [subscriptionId, userId]
      );

      if (existingSubscriptions.length === 0) {
        await connection.end();
        return res.status(404).json({
          success: false,
          message: '订阅不存在'
        });
      }

      // 取消订阅
      const query = `
        UPDATE price_subscriptions
        SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND user_id = ?
      `;

      await connection.execute(query, [subscriptionId, userId]);
      await connection.end();

      res.json({
        success: true,
        message: '价格订阅已取消'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('取消价格订阅失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '取消价格订阅失败',
        error: error.message
      });
    }
  }

  /**
   * 获取价格分析报告
   */
  async getPriceAnalysis(req, res) {
    try {
      const { breed_name, breed_type } = req.query;
      const connection = await mysql.createConnection(dbConfig);

      // 获取最近30天的价格数据
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);

      let whereClause = 'WHERE record_date >= ?';
      const queryParams = [startDate];

      if (breed_name) {
        whereClause += ' AND breed_name = ?';
        queryParams.push(breed_name);
      }

      if (breed_type) {
        whereClause += ' AND breed_type = ?';
        queryParams.push(breed_type);
      }

      // 查询价格数据
      const query = `
        SELECT
          price,
          change_rate,
          record_date
        FROM price_records
        ${whereClause}
        ORDER BY record_date ASC
      `;

      const [priceData] = await connection.execute(query, queryParams);

      if (priceData.length === 0) {
        await connection.end();
        return res.json({
          success: true,
          data: {
            trend: 'stable',
            forecast: 'insufficient_data',
            suggestion: '数据不足，无法进行分析'
          }
        });
      }

      // 分析价格趋势
      const prices = priceData.map((p) => p.price);
      const firstPrice = prices[0];
      const lastPrice = prices[prices.length - 1];
      const priceChange = lastPrice - firstPrice;
      const priceChangePercent = (priceChange / firstPrice) * 100;

      // 计算趋势
      let trend = 'stable';
      if (priceChangePercent > 5) {
        trend = 'rising';
      } else if (priceChangePercent < -5) {
        trend = 'falling';
      }

      // 计算波动性
      const avgPrice =
        prices.reduce((sum, price) => sum + price, 0) / prices.length;
      const variance =
        prices.reduce((sum, price) => sum + Math.pow(price - avgPrice, 2), 0) /
        prices.length;
      const volatility = Math.sqrt(variance);

      // 生成预测和建议
      let forecast = 'stable';
      let suggestion = '';

      if (trend === 'rising' && volatility < 2) {
        forecast = 'continue_rising';
        suggestion = '价格呈上升趋势且波动较小，建议适当增加库存';
      } else if (trend === 'falling' && volatility < 2) {
        forecast = 'continue_falling';
        suggestion = '价格呈下降趋势，建议谨慎采购，等待价格稳定';
      } else if (volatility > 3) {
        forecast = 'high_volatility';
        suggestion = '价格波动较大，建议密切关注市场动态，分批采购';
      } else {
        forecast = 'stable';
        suggestion = '价格相对稳定，可按正常计划进行采购';
      }

      await connection.end();

      res.json({
        success: true,
        data: {
          trend: trend,
          trendText: this.getTrendText(trend),
          priceChange: parseFloat(priceChange.toFixed(2)),
          priceChangePercent: parseFloat(priceChangePercent.toFixed(2)),
          volatility: parseFloat(volatility.toFixed(2)),
          forecast: forecast,
          forecastText: this.getForecastText(forecast),
          suggestion: suggestion,
          analysisDate: new Date(),
          dataPoints: priceData.length
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取价格分析失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取价格分析失败',
        error: error.message
      });
    }
  }

  /**
   * 获取最新价格信息
   */
  async getLatestPrices(req, res) {
    try {
      const connection = await mysql.createConnection(dbConfig);

      // 获取最新日期的所有价格记录
      const query = `
        SELECT
          breed_name,
          breed_type,
          AVG(price) as avg_price,
          AVG(change_rate) as avg_change_rate,
          MIN(price) as min_price,
          MAX(price) as max_price,
          COUNT(*) as record_count,
          MAX(record_date) as latest_date
        FROM price_records
        WHERE record_date = (SELECT MAX(record_date) FROM price_records)
        GROUP BY breed_name, breed_type
        ORDER BY breed_name, breed_type
      `;

      const [latestPrices] = await connection.execute(query);

      // 格式化价格数据
      const formattedPrices = latestPrices.map((price) => ({
        breedName: price.breed_name,
        breedType: price.breed_type,
        breedTypeText: this.getBreedTypeText(price.breed_type),
        avgPrice: parseFloat(price.avg_price.toFixed(2)),
        avgChangeRate: price.avg_change_rate
          ? parseFloat(price.avg_change_rate.toFixed(2))
          : 0,
        minPrice: parseFloat(price.min_price.toFixed(2)),
        maxPrice: parseFloat(price.max_price.toFixed(2)),
        recordCount: price.record_count,
        latestDate: price.latest_date
      }));

      await connection.end();

      res.json({
        success: true,
        data: {
          prices: formattedPrices,
          updateTime:
            formattedPrices.length > 0 ? formattedPrices[0].latestDate : null,
          total: formattedPrices.length
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取最新价格失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取最新价格失败',
        error: error.message
      });
    }
  }

  // 辅助方法
  getTrendText(trend) {
    const trendMap = {
      rising: '上升',
      falling: '下降',
      stable: '稳定'
    };
    return trendMap[trend] || trend;
  }

  getForecastText(forecast) {
    const forecastMap = {
      continue_rising: '继续上涨',
      continue_falling: '继续下跌',
      stable: '保持稳定',
      high_volatility: '高波动',
      insufficient_data: '数据不足'
    };
    return forecastMap[forecast] || forecast;
  }
}

module.exports = new PriceController();
