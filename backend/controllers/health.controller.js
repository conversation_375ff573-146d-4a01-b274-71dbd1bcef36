const HealthRecord = require('../models/health-record.model');
const Flock = require('../models/flock.model');
const { Op } = require('sequelize');
const ResponseHelper = require('../utils/response-helper');
const ValidationHelper = require('../utils/validation-helper');

/**
 * 状态映射方法：将后端状态值映射到前端期望的值
 */
function mapHealthStatus(healthStatus, checkType) {
  // 根据健康状态和检查类型组合来确定前端状态
  if (checkType === 'vaccination') {
    return 'vaccination'; // 防疫记录
  } else if (healthStatus === 'sick' || healthStatus === 'critical') {
    return 'sick'; // 生病记录
  } else if (checkType === 'treatment') {
    return 'treatment'; // 治疗记录
  } else if (healthStatus === 'critical' && checkType === 'emergency') {
    return 'death'; // 死亡记录
  } else {
    return 'healthy'; // 健康记录
  }
}

/**
 * 获取健康记录列表
 */
exports.getRecords = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      flockId = '',
      healthStatus = '',
      checkType = '',
      sortBy = 'checkDate',
      sortOrder = 'DESC'
    } = req.query;

    // 验证分页参数
    if (
      !ValidationHelper.validateNumber(page, 1) ||
      !ValidationHelper.validateNumber(limit, 1, 100)
    ) {
      return ResponseHelper.error(res, '分页参数无效', 400);
    }

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // 构建查询条件
    const whereConditions = { userId: req.user.id };

    // 搜索条件
    if (search) {
      whereConditions[Op.or] = [
        { symptoms: { [Op.like]: `%${search}%` } },
        { diagnosis: { [Op.like]: `%${search}%` } },
        { treatment: { [Op.like]: `%${search}%` } },
        { notes: { [Op.like]: `%${search}%` } }
      ];
    }

    // 鹅群筛选
    if (flockId) {
      whereConditions.flockId = flockId;
    }

    // 健康状态筛选
    if (healthStatus) {
      whereConditions.healthStatus = healthStatus;
    }

    // 检查类型筛选
    if (checkType) {
      whereConditions.checkType = checkType;
    }

    // 排序验证
    const allowedSortFields = [
      'checkDate',
      'healthStatus',
      'checkType',
      'created_at'
    ];
    const sortField = allowedSortFields.includes(sortBy) ? sortBy : 'checkDate';
    const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const { count, rows } = await HealthRecord.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: Flock,
          as: 'flock',
          attributes: ['id', 'name', 'batchNumber', 'currentCount']
        }
      ],
      limit: parseInt(limit),
      offset: offset,
      order: [[sortField, order]]
    });

    // 转换数据格式
    const records = rows.map((record) => ({
      id: record.id,
      flockId: record.flockId,
      flockName: record.flock ? record.flock.name : '未知鹅群',
      batchNumber: record.flock ? record.flock.batchNumber : '',
      checkDate: record.checkDate,
      checkType: record.checkType,
      healthStatus: record.healthStatus,
      affectedCount: record.affectedCount,
      symptoms: record.symptoms,
      diagnosis: record.diagnosis,
      treatment: record.treatment,
      medication: record.medication,
      dosage: record.dosage,
      veterinarian: record.veterinarian,
      followUpDate: record.followUpDate,
      notes: record.notes,
      severity: this.calculateSeverity(record),
      riskLevel: this.calculateRiskLevel(record),
      createdAt: record.created_at,
      updatedAt: record.updated_at
    }));

    const paginatedData = ResponseHelper.paginate(records, page, limit, count);
    return res.json({
      code: 0,
      message: '获取健康记录成功',
      data: paginatedData
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取健康记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    return res.status(500).json({
      code: -1,
      message: '获取健康记录失败',
      error: process.env.NODE_ENV === 'development' ? error.message : null
    });
  }
};

// 辅助方法
exports.calculateSeverity = (record) => {
  // 根据健康状态和受影响数量计算严重程度
  const statusWeight = {
    healthy: 0,
    warning: 1,
    sick: 2,
    critical: 3
  };

  const baseScore = statusWeight[record.healthStatus] || 0;
  const affectedRatio =
    record.affectedCount / (record.flock?.currentCount || 1);

  if (baseScore === 0) return 'low';
  if (baseScore === 1 || affectedRatio < 0.1) return 'medium';
  if (baseScore === 2 || affectedRatio < 0.3) return 'high';
  return 'critical';
};

exports.calculateRiskLevel = (record) => {
  // 根据症状和诊断计算风险等级
  const riskKeywords = {
    high: ['传染', '病毒', '细菌', '死亡', '急性'],
    medium: ['炎症', '感染', '发热', '腹泻'],
    low: ['轻微', '营养', '环境', '应激']
  };

  const text = `${record.symptoms} ${record.diagnosis}`.toLowerCase();

  for (const [level, keywords] of Object.entries(riskKeywords)) {
    if (keywords.some((keyword) => text.includes(keyword))) {
      return level;
    }
  }

  return 'low';
};

// 创建健康记录
exports.createRecord = async (req, res) => {
  try {
    const { gooseId, healthStatus, symptoms, diagnosis, treatment } = req.body;

    // 验证必填字段
    if (!gooseId || !healthStatus) {
      return res.status(400).json({
        success: false,
        message: '鹅群编号和健康状态为必填项'
      });
    }

    const record = await HealthRecord.create({
      userId: req.user.id,
      gooseId: gooseId,
      status: healthStatus,
      symptoms: symptoms,
      diagnosis: diagnosis,
      treatment: treatment
    });

    res.status(201).json({
      success: true,
      message: '健康记录创建成功',
      data: {
        id: record.id,
        userId: record.userId,
        gooseId: record.gooseId,
        healthStatus: record.status,
        symptoms: record.symptoms,
        diagnosis: record.diagnosis,
        treatment: record.treatment,
        createdAt: record.created_at,
        updatedAt: record.updated_at
      }
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('创建健康记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取健康记录详情
exports.getRecordById = async (req, res) => {
  try {
    const { id } = req.params;

    const record = await HealthRecord.findOne({
      where: {
        id: id,
        userId: req.user.id
      }
    });

    if (!record) {
      return res.status(404).json({
        success: false,
        message: '健康记录不存在'
      });
    }

    res.json({
      code: 0,
      message: '获取成功',
      data: {
        id: record.id,
        userId: record.userId,
        batchNumber: record.flockId ? `批次${record.flockId}` : '',
        status: mapHealthStatus(record.healthStatus, record.checkType) || 'healthy',
        date: record.checkDate || record.created_at,
        author: record.veterinarian || '系统',
        description: record.symptoms || record.diagnosis || record.treatment || '',
        symptoms: record.symptoms || '',
        diagnosis: record.diagnosis || '',
        treatment: record.treatment || '',
        medication: record.medication || '',
        dosage: record.dosage || '',
        affectedCount: record.affectedCount || 0,
        images: [], // 暂时为空数组，后续可以扩展
        notes: record.notes || '',
        createdAt: record.created_at,
        updatedAt: record.updated_at
      }
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取健康记录详情失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 更新健康记录
exports.updateRecord = async (req, res) => {
  try {
    const { id } = req.params;
    const { 鹅群编号, 健康状态, 症状, 诊断, 治疗 } = req.body;

    const record = await HealthRecord.findOne({
      where: {
        id: id,
        userId: req.user.id
      }
    });

    if (!record) {
      return res.status(404).json({
        success: false,
        message: '健康记录不存在'
      });
    }

    // 更新记录
    await record.update({
      gooseId: 鹅群编号,
      status: 健康状态,
      symptoms: 症状,
      diagnosis: 诊断,
      treatment: 治疗
    });

    res.json({
      code: 0,
      message: '健康记录更新成功',
      data: {
        id: record.id,
        userId: record.userId,
        batchNumber: record.flockId ? `批次${record.flockId}` : '',
        status: mapHealthStatus(record.healthStatus, record.checkType) || 'healthy',
        date: record.checkDate || record.created_at,
        author: record.veterinarian || '系统',
        description: record.symptoms || record.diagnosis || record.treatment || '',
        symptoms: record.symptoms || '',
        diagnosis: record.diagnosis || '',
        treatment: record.treatment || '',
        medication: record.medication || '',
        dosage: record.dosage || '',
        affectedCount: record.affectedCount || 0,
        images: [], // 暂时为空数组，后续可以扩展
        notes: record.notes || '',
        createdAt: record.created_at,
        updatedAt: record.updated_at
      }
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('更新健康记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 部分更新健康记录
exports.patchRecord = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const record = await HealthRecord.findOne({
      where: {
        id: id,
        userId: req.user.id
      }
    });

    if (!record) {
      return res.status(404).json({
        success: false,
        message: '健康记录不存在'
      });
    }

    // 更新记录
    await record.update(updateData);

    res.json({
      code: 0,
      message: '健康记录更新成功',
      data: {
        id: record.id,
        userId: record.userId,
        batchNumber: record.flockId ? `批次${record.flockId}` : '',
        status: mapHealthStatus(record.healthStatus, record.checkType) || 'healthy',
        date: record.checkDate || record.created_at,
        author: record.veterinarian || '系统',
        description: record.symptoms || record.diagnosis || record.treatment || '',
        symptoms: record.symptoms || '',
        diagnosis: record.diagnosis || '',
        treatment: record.treatment || '',
        medication: record.medication || '',
        dosage: record.dosage || '',
        affectedCount: record.affectedCount || 0,
        images: [], // 暂时为空数组，后续可以扩展
        notes: record.notes || '',
        createdAt: record.created_at,
        updatedAt: record.updated_at
      }
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('部分更新健康记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 删除健康记录
exports.deleteRecord = async (req, res) => {
  try {
    const { id } = req.params;

    const record = await HealthRecord.findOne({
      where: {
        id: id,
        userId: req.user.id
      }
    });

    if (!record) {
      return res.status(404).json({
        success: false,
        message: '健康记录不存在'
      });
    }

    // 删除记录
    await record.destroy();

    res.json({
      success: true,
      message: '健康记录删除成功'
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('删除健康记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 批量创建健康记录
exports.batchCreateRecords = async (req, res) => {
  try {
    const records = req.body.records;

    if (!records || !Array.isArray(records) || records.length === 0) {
      return res.status(400).json({
        success: false,
        message: '记录列表不能为空'
      });
    }

    // 为每条记录添加用户ID
    const recordsWithUserId = records.map((record) => ({
      ...record,
      userId: req.user.id
    }));

    // 批量创建记录
    const createdRecords = await HealthRecord.bulkCreate(recordsWithUserId);

    res.status(201).json({
      success: true,
      message: `成功创建${createdRecords.length}条健康记录`,
      data: {
        records: createdRecords.map((record) => ({
          id: record.id,
          userId: record.userId,
          鹅群编号: record.gooseId,
          健康状态: record.status,
          症状: record.symptoms,
          诊断: record.diagnosis,
          治疗: record.treatment,
          createdAt: record.created_at,
          updatedAt: record.updated_at
        }))
      }
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('批量创建健康记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 批量更新健康记录
exports.batchUpdateRecords = async (req, res) => {
  try {
    const updates = req.body.updates;

    if (!updates || !Array.isArray(updates) || updates.length === 0) {
      return res.status(400).json({
        success: false,
        message: '更新列表不能为空'
      });
    }

    let updatedCount = 0;

    // 逐条更新记录
    for (const update of updates) {
      const { id, ...updateData } = update;

      const record = await HealthRecord.findOne({
        where: {
          id: id,
          userId: req.user.id
        }
      });

      if (record) {
        await record.update(updateData);
        updatedCount++;
      }
    }

    res.json({
      success: true,
      message: `成功更新${updatedCount}条健康记录`,
      data: {
        updatedCount: updatedCount
      }
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('批量更新健康记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 批量删除健康记录
exports.batchDeleteRecords = async (req, res) => {
  try {
    const ids = req.body.ids;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'ID列表不能为空'
      });
    }

    // 批量删除记录
    const deletedCount = await HealthRecord.destroy({
      where: {
        id: {
          [Op.in]: ids
        },
        userId: req.user.id
      }
    });

    res.json({
      success: true,
      message: `成功删除${deletedCount}条健康记录`,
      data: {
        deletedCount: deletedCount
      }
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('批量删除健康记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取健康记录统计信息
exports.getRecordStats = async (req, res) => {
  try {
    const totalRecords = await HealthRecord.count({
      where: { userId: req.user.id }
    });

    const healthyRecords = await HealthRecord.count({
      where: {
        userId: req.user.id,
        status: '健康'
      }
    });

    const sickRecords = await HealthRecord.count({
      where: {
        userId: req.user.id,
        status: '生病'
      }
    });

    const treatedRecords = await HealthRecord.count({
      where: {
        userId: req.user.id,
        status: '已治疗'
      }
    });

    const stats = {
      total: totalRecords,
      healthy: healthyRecords,
      sick: sickRecords,
      treated: treatedRecords
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取健康记录统计信息失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取健康趋势数据
exports.getHealthTrends = async (req, res) => {
  try {
    // 获取近30天的健康记录统计
    const trends = await HealthRecord.findAll({
      where: {
        userId: req.user.id,
        createdAt: {
          [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        }
      },
      attributes: [
        [sequelize.fn('DATE', sequelize.col('created_at')), 'date'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        'status'
      ],
      group: ['date', 'status'],
      order: [['date', 'ASC']]
    });

    res.json({
      success: true,
      data: {
        trends: trends
      }
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取健康趋势数据失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 导出健康记录为CSV格式
exports.exportRecordsCSV = async (req, res) => {
  try {
    const records = await HealthRecord.findAll({
      where: { userId: req.user.id },
      order: [['created_at', 'DESC']]
    });

    // 构造CSV内容
    let csvContent = 'ID,鹅群编号,健康状态,症状,诊断,治疗,创建时间,更新时间\n';
    records.forEach((record) => {
      csvContent += `"${record.id}","${record.gooseId}","${record.status}","${record.symptoms || ''}","${record.diagnosis || ''}","${record.treatment || ''}","${record.created_at}","${record.updated_at}"\n`;
    });

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader(
      'Content-Disposition',
      'attachment; filename="health-records.csv"'
    );
    res.status(200).send(csvContent);
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('导出健康记录CSV失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 导出健康记录为Excel格式
exports.exportRecordsExcel = async (req, res) => {
  try {
    const records = await HealthRecord.findAll({
      where: { userId: req.user.id },
      order: [['created_at', 'DESC']]
    });

    // 构造Excel内容（简化版，实际项目中可以使用专门的库如xlsx）
    let excelContent =
      'ID\t鹅群编号\t健康状态\t症状\t诊断\t治疗\t创建时间\t更新时间\n';
    records.forEach((record) => {
      excelContent += `${record.id}\t${record.gooseId}\t${record.status}\t${record.symptoms || ''}\t${record.diagnosis || ''}\t${record.treatment || ''}\t${record.created_at}\t${record.updated_at}\n`;
    });

    res.setHeader('Content-Type', 'application/vnd.ms-excel');
    res.setHeader(
      'Content-Disposition',
      'attachment; filename="health-records.xls"'
    );
    res.status(200).send(excelContent);
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('导出健康记录Excel失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取健康报告列表
exports.getReports = async (req, res) => {
  try {
    const userId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    // 从数据库获取健康记录，按时间分组生成报告
    const healthRecords = await HealthRecord.findAll({
      where: { userId: userId },
      order: [['createdAt', 'DESC']],
      limit: limit * 10 // 获取更多记录用于分析
    });

    // 按月分组生成报告
    const reportsByMonth = {};
    healthRecords.forEach(record => {
      const monthKey = record.createdAt.toISOString().slice(0, 7); // YYYY-MM
      if (!reportsByMonth[monthKey]) {
        reportsByMonth[monthKey] = [];
      }
      reportsByMonth[monthKey].push(record);
    });

    // 生成报告列表
    const reports = Object.keys(reportsByMonth)
      .slice(offset, offset + limit)
      .map((monthKey, index) => {
        const records = reportsByMonth[monthKey];
        const healthyCount = records.filter(r => r.healthStatus === 'healthy').length;
        const totalCount = records.length;
        const healthRate = totalCount > 0 ? Math.round((healthyCount / totalCount) * 100) : 0;

        return {
          id: offset + index + 1,
          title: `${monthKey} 月度健康报告`,
          period: monthKey,
          summary: `本月共检查 ${totalCount} 次，健康率 ${healthRate}%`,
          recordCount: totalCount,
          healthRate: healthRate,
          createdAt: new Date(monthKey + '-01')
        };
      });

    res.json({
      success: true,
      data: {
        reports: reports,
        pagination: {
          page: page,
          limit: limit,
          total: Object.keys(reportsByMonth).length
        }
      }
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取健康报告列表失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 生成健康报告
exports.generateReport = async (req, res) => {
  try {
    const { type, period, flockIds } = req.body;
    const userId = req.user.id;

    // 验证必填字段
    if (!type || !period) {
      return res.status(400).json({
        success: false,
        message: '报告类型和时间范围为必填项'
      });
    }

    // 根据时间范围获取健康记录
    let startDate, endDate;
    const now = new Date();
    
    if (period === 'week') {
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      endDate = now;
    } else if (period === 'month') {
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    } else if (period === 'quarter') {
      const quarter = Math.floor(now.getMonth() / 3);
      startDate = new Date(now.getFullYear(), quarter * 3, 1);
      endDate = new Date(now.getFullYear(), quarter * 3 + 3, 0);
    }

    // 查询健康记录
    const whereClause = {
      userId: userId,
      createdAt: {
        [Op.between]: [startDate, endDate]
      }
    };

    if (flockIds && flockIds.length > 0) {
      whereClause.flockId = { [Op.in]: flockIds };
    }

    const healthRecords = await HealthRecord.findAll({
      where: whereClause,
      order: [['createdAt', 'DESC']]
    });

    // 生成报告内容
    const totalRecords = healthRecords.length;
    const healthyCount = healthRecords.filter(r => r.healthStatus === 'healthy').length;
    const unhealthyCount = totalRecords - healthyCount;
    const healthRate = totalRecords > 0 ? Math.round((healthyCount / totalRecords) * 100) : 0;

    // 按疾病类型统计
    const diseaseStats = {};
    healthRecords.forEach(record => {
      if (record.diagnosis && record.healthStatus !== 'healthy') {
        diseaseStats[record.diagnosis] = (diseaseStats[record.diagnosis] || 0) + 1;
      }
    });

    const reportContent = {
      period: { start: startDate, end: endDate },
      summary: {
        totalChecks: totalRecords,
        healthyCount: healthyCount,
        unhealthyCount: unhealthyCount,
        healthRate: healthRate
      },
      diseaseAnalysis: diseaseStats,
      recommendations: generateHealthRecommendations(healthRate, diseaseStats)
    };

    const report = {
      id: Date.now(), // 临时ID，实际应存储到数据库
      title: `${getReportTypeTitle(type)} - ${period}`,
      type: type,
      period: period,
      generatedBy: req.user.id,
      generatedAt: new Date(),
      content: reportContent
    };

    res.status(201).json({
      success: true,
      message: '健康报告生成成功',
      data: report
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('生成健康报告失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 辅助函数：获取报告类型标题
function getReportTypeTitle(type) {
  const titles = {
    'weekly': '周度健康报告',
    'monthly': '月度健康报告',
    'quarterly': '季度健康报告',
    'annual': '年度健康报告'
  };
  return titles[type] || '健康报告';
}

// 辅助函数：生成健康建议
function generateHealthRecommendations(healthRate, diseaseStats) {
  const recommendations = [];
  
  if (healthRate < 60) {
    recommendations.push('健康率偏低，建议加强疾病预防措施');
    recommendations.push('定期进行环境消毒和清洁');
  } else if (healthRate < 80) {
    recommendations.push('健康状况一般，需要持续关注');
    recommendations.push('优化饲养环境和营养配给');
  } else {
    recommendations.push('健康状况良好，继续保持现有管理措施');
  }

  // 根据疾病统计给出针对性建议
  Object.keys(diseaseStats).forEach(disease => {
    if (disease.includes('呼吸')) {
      recommendations.push('注意改善通风条件，预防呼吸道疾病');
    }
    if (disease.includes('消化') || disease.includes('腹泻')) {
      recommendations.push('检查饲料质量，注意饮水卫生');
    }
  });

  return recommendations;
}

// 获取健康报告详情
exports.getReportById = async (req, res) => {
  try {
    const { id } = req.params;

    // 这里应该从数据库获取真实的健康报告数据
    // 暂时返回模拟数据
    const report = {
      id: id,
      title: '健康报告详情',
      content: '这是健康报告的详细内容',
      generatedAt: new Date()
    };

    res.json({
      success: true,
      data: report
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取健康报告详情失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 删除健康报告
exports.deleteReport = async (req, res) => {
  try {
    const { id } = req.params;

    // 这里应该从数据库删除真实的健康报告数据
    // 暂时直接返回成功

    res.json({
      success: true,
      message: '健康报告删除成功'
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('删除健康报告失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 添加缺失的方法别名以兼容统一API路由
exports.getHealthRecords = exports.getRecords;
exports.createHealthRecord = exports.createRecord;
exports.getHealthStatistics = exports.getRecordStats;
