/**
 * 公告管理控制器
 * 处理公告相关的业务逻辑
 */

const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USERNAME || 'zhihuiyange',
  password: process.env.DB_PASSWORD || 'zhihuiyange123',
  database: process.env.DB_NAME || 'zhihuiyange_local',
  charset: 'utf8mb4'
};

class AnnouncementController {
  /**
   * 获取公告列表
   * 支持筛选、搜索、分页
   */
  async getAnnouncements(req, res) {
    try {
      const userId = req.user.id;
      const {
        type,
        search,
        unreadOnly,
        sortBy = 'publish_time',
        sortOrder = 'desc',
        page = 1,
        limit = 10
      } = req.query;

      const connection = await mysql.createConnection(dbConfig);

      // 构建查询条件
      let whereClause = 'WHERE 1=1';
      const queryParams = [];

      if (type) {
        whereClause += ' AND a.type = ?';
        queryParams.push(type);
      }

      if (search) {
        whereClause +=
          ' AND (a.title LIKE ? OR a.summary LIKE ? OR a.publisher LIKE ?)';
        const searchPattern = `%${search}%`;
        queryParams.push(searchPattern, searchPattern, searchPattern);
      }

      // 如果只显示未读公告
      if (unreadOnly === 'true') {
        whereClause += ' AND ar.id IS NULL';
      }

      // 构建排序
      const validSortFields = ['publish_time', 'view_count', 'created_at'];
      const sortField = validSortFields.includes(sortBy)
        ? sortBy
        : 'publish_time';
      const order = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

      // 置顶公告优先排序
      const orderClause = `ORDER BY a.is_top DESC, a.${sortField} ${order}`;

      // 分页
      const offset = (parseInt(page) - 1) * parseInt(limit);
      const limitClause = `LIMIT ${parseInt(limit)} OFFSET ${offset}`;

      // 查询公告列表（包含已读状态）
      const query = `
        SELECT 
          a.id,
          a.title,
          a.summary,
          a.type,
          a.publisher,
          a.is_top,
          a.is_important,
          a.view_count,
          a.attachment_count,
          a.publish_time,
          a.created_at,
          a.updated_at,
          CASE WHEN ar.id IS NOT NULL THEN 1 ELSE 0 END as is_read
        FROM announcements a
        LEFT JOIN announcement_reads ar ON a.id = ar.announcement_id AND ar.user_id = ?
        ${whereClause} 
        ${orderClause} 
        ${limitClause}
      `;

      const [announcements] = await connection.execute(query, [
        userId,
        ...queryParams
      ]);

      // 查询总数
      const countQuery = `
        SELECT COUNT(*) as total 
        FROM announcements a
        LEFT JOIN announcement_reads ar ON a.id = ar.announcement_id AND ar.user_id = ?
        ${whereClause}
      `;
      const [countResult] = await connection.execute(countQuery, [
        userId,
        ...queryParams
      ]);
      const total = countResult[0].total;

      // 格式化公告数据
      const formattedAnnouncements = announcements.map((announcement) => ({
        id: announcement.id,
        title: announcement.title,
        summary: announcement.summary,
        type: announcement.type,
        typeText: this.getTypeText(announcement.type),
        publisher: announcement.publisher,
        isTop: Boolean(announcement.is_top),
        isImportant: Boolean(announcement.is_important),
        viewCount: announcement.view_count,
        attachmentCount: announcement.attachment_count,
        publishTime: this.formatPublishTime(announcement.publish_time),
        isRead: Boolean(announcement.is_read),
        createTime: announcement.created_at,
        updateTime: announcement.updated_at
      }));

      await connection.end();

      res.json({
        success: true,
        data: {
          announcements: formattedAnnouncements,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            totalPages: Math.ceil(total / parseInt(limit))
          }
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取公告列表失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取公告列表失败',
        error: error.message
      });
    }
  }

  /**
   * 创建新公告
   */
  async createAnnouncement(req, res) {
    try {
      // 检查管理员权限
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: '权限不足，只有管理员可以创建公告'
        });
      }

      const {
        title,
        content,
        summary,
        type,
        publisher,
        is_top = false,
        is_important = false,
        attachment_count = 0,
        publish_time
      } = req.body;

      const connection = await mysql.createConnection(dbConfig);

      const query = `
        INSERT INTO announcements (title, content, summary, type, publisher, is_top, is_important, attachment_count, publish_time)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const publishDateTime = publish_time
        ? new Date(publish_time)
        : new Date();

      const [result] = await connection.execute(query, [
        title,
        content,
        summary,
        type,
        publisher,
        is_top,
        is_important,
        attachment_count,
        publishDateTime
      ]);

      // 获取创建的公告详情
      const [newAnnouncement] = await connection.execute(
        'SELECT * FROM announcements WHERE id = ?',
        [result.insertId]
      );

      await connection.end();

      res.status(201).json({
        success: true,
        message: '公告创建成功',
        data: {
          id: result.insertId,
          ...newAnnouncement[0]
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('创建公告失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '创建公告失败',
        error: error.message
      });
    }
  }

  /**
   * 获取公告统计信息
   */
  async getAnnouncementStatistics(req, res) {
    try {
      const userId = req.user.id;
      const connection = await mysql.createConnection(dbConfig);

      const query = `
        SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN a.is_important = 1 THEN 1 ELSE 0 END) as important,
          SUM(CASE WHEN a.type = 'notice' THEN 1 ELSE 0 END) as notice,
          SUM(CASE WHEN a.type = 'policy' THEN 1 ELSE 0 END) as policy,
          SUM(CASE WHEN a.type = 'activity' THEN 1 ELSE 0 END) as activity,
          SUM(CASE WHEN ar.id IS NULL THEN 1 ELSE 0 END) as unread
        FROM announcements a
        LEFT JOIN announcement_reads ar ON a.id = ar.announcement_id AND ar.user_id = ?
      `;

      const [result] = await connection.execute(query, [userId]);
      await connection.end();

      res.json({
        success: true,
        data: {
          all: result[0].total,
          important: result[0].important,
          notice: result[0].notice,
          policy: result[0].policy,
          activity: result[0].activity,
          unread: result[0].unread
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取公告统计失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取公告统计失败',
        error: error.message
      });
    }
  }

  /**
   * 获取公告详情
   */
  async getAnnouncementById(req, res) {
    try {
      const userId = req.user.id;
      const announcementId = req.params.id;

      const connection = await mysql.createConnection(dbConfig);

      const query = `
        SELECT 
          a.*,
          CASE WHEN ar.id IS NOT NULL THEN 1 ELSE 0 END as is_read
        FROM announcements a
        LEFT JOIN announcement_reads ar ON a.id = ar.announcement_id AND ar.user_id = ?
        WHERE a.id = ?
      `;

      const [announcements] = await connection.execute(query, [
        userId,
        announcementId
      ]);

      if (announcements.length === 0) {
        await connection.end();
        return res.status(404).json({
          success: false,
          message: '公告不存在'
        });
      }

      await connection.end();

      const announcement = announcements[0];
      res.json({
        success: true,
        data: {
          ...announcement,
          typeText: this.getTypeText(announcement.type),
          isRead: Boolean(announcement.is_read),
          isTop: Boolean(announcement.is_top),
          isImportant: Boolean(announcement.is_important)
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取公告详情失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取公告详情失败',
        error: error.message
      });
    }
  }

  /**
   * 标记公告为已读
   */
  async markAsRead(req, res) {
    try {
      const userId = req.user.id;
      const announcementId = req.params.id;

      const connection = await mysql.createConnection(dbConfig);

      // 检查公告是否存在
      const [existingAnnouncements] = await connection.execute(
        'SELECT id FROM announcements WHERE id = ?',
        [announcementId]
      );

      if (existingAnnouncements.length === 0) {
        await connection.end();
        return res.status(404).json({
          success: false,
          message: '公告不存在'
        });
      }

      // 插入或更新已读记录
      const query = `
        INSERT INTO announcement_reads (user_id, announcement_id)
        VALUES (?, ?)
        ON DUPLICATE KEY UPDATE read_at = CURRENT_TIMESTAMP
      `;

      await connection.execute(query, [userId, announcementId]);
      await connection.end();

      res.json({
        success: true,
        message: '公告已标记为已读'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('标记公告已读失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '标记公告已读失败',
        error: error.message
      });
    }
  }

  // 辅助方法
  getTypeText(type) {
    const typeMap = {
      important: '重要',
      notice: '通知',
      policy: '政策',
      activity: '活动'
    };
    return typeMap[type] || type;
  }

  formatPublishTime(publishTime) {
    const now = new Date();
    const publishDate = new Date(publishTime);
    const diffTime = now - publishDate;
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffHours < 1) {
      const diffMinutes = Math.floor(diffTime / (1000 * 60));
      return diffMinutes < 1 ? '刚刚' : `${diffMinutes}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays === 1) {
      return '1天前';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return publishDate.toLocaleDateString('zh-CN');
    }
  }

  /**
   * 标记所有公告为已读
   */
  async markAllAsRead(req, res) {
    try {
      const userId = req.user.id;
      const { announcementIds } = req.body;

      const connection = await mysql.createConnection(dbConfig);

      let query;
      let queryParams;

      if (
        announcementIds &&
        Array.isArray(announcementIds) &&
        announcementIds.length > 0
      ) {
        // 标记指定公告为已读
        const placeholders = announcementIds.map(() => '?').join(',');
        query = `
          INSERT INTO announcement_reads (user_id, announcement_id)
          SELECT ?, id FROM announcements WHERE id IN (${placeholders})
          ON DUPLICATE KEY UPDATE read_at = CURRENT_TIMESTAMP
        `;
        queryParams = [userId, ...announcementIds];
      } else {
        // 标记所有公告为已读
        query = `
          INSERT INTO announcement_reads (user_id, announcement_id)
          SELECT ?, id FROM announcements
          ON DUPLICATE KEY UPDATE read_at = CURRENT_TIMESTAMP
        `;
        queryParams = [userId];
      }

      const [result] = await connection.execute(query, queryParams);
      await connection.end();

      res.json({
        success: true,
        message: '所有公告已标记为已读',
        data: {
          affectedRows: result.affectedRows
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('标记所有公告已读失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '标记所有公告已读失败',
        error: error.message
      });
    }
  }

  /**
   * 获取公告已读状态
   */
  async getReadStatus(req, res) {
    try {
      const userId = req.user.id;
      const announcementId = req.params.id;

      const connection = await mysql.createConnection(dbConfig);

      const query = `
        SELECT
          CASE WHEN ar.id IS NOT NULL THEN 1 ELSE 0 END as is_read,
          ar.read_at
        FROM announcements a
        LEFT JOIN announcement_reads ar ON a.id = ar.announcement_id AND ar.user_id = ?
        WHERE a.id = ?
      `;

      const [result] = await connection.execute(query, [
        userId,
        announcementId
      ]);

      if (result.length === 0) {
        await connection.end();
        return res.status(404).json({
          success: false,
          message: '公告不存在'
        });
      }

      await connection.end();

      res.json({
        success: true,
        data: {
          isRead: Boolean(result[0].is_read),
          readAt: result[0].read_at
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取公告已读状态失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取公告已读状态失败',
        error: error.message
      });
    }
  }

  /**
   * 增加公告浏览量
   */
  async incrementViewCount(req, res) {
    try {
      const announcementId = req.params.id;

      const connection = await mysql.createConnection(dbConfig);

      const query = `
        UPDATE announcements
        SET view_count = view_count + 1
        WHERE id = ?
      `;

      const [result] = await connection.execute(query, [announcementId]);

      if (result.affectedRows === 0) {
        await connection.end();
        return res.status(404).json({
          success: false,
          message: '公告不存在'
        });
      }

      await connection.end();

      res.json({
        success: true,
        message: '浏览量已更新'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('更新浏览量失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '更新浏览量失败',
        error: error.message
      });
    }
  }

  /**
   * 获取公告类型列表
   */
  async getAnnouncementTypes(req, res) {
    try {
      const types = [
        { value: 'important', label: '重要', color: '#ff4d4f' },
        { value: 'notice', label: '通知', color: '#1890ff' },
        { value: 'policy', label: '政策', color: '#52c41a' },
        { value: 'activity', label: '活动', color: '#faad14' }
      ];

      res.json({
        success: true,
        data: types
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取公告类型失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取公告类型失败',
        error: error.message
      });
    }
  }

  /**
   * 批量标记公告为已读
   */
  async batchMarkAsRead(req, res) {
    try {
      const userId = req.user.id;
      const { announcementIds } = req.body;

      if (!Array.isArray(announcementIds) || announcementIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请提供要标记的公告ID列表'
        });
      }

      const connection = await mysql.createConnection(dbConfig);

      const placeholders = announcementIds.map(() => '?').join(',');
      const query = `
        INSERT INTO announcement_reads (user_id, announcement_id)
        SELECT ?, id FROM announcements WHERE id IN (${placeholders})
        ON DUPLICATE KEY UPDATE read_at = CURRENT_TIMESTAMP
      `;

      await connection.execute(query, [userId, ...announcementIds]);
      await connection.end();

      res.json({
        success: true,
        message: `成功标记 ${announcementIds.length} 个公告为已读`
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('批量标记已读失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '批量标记已读失败',
        error: error.message
      });
    }
  }

  /**
   * 获取未读公告数量
   */
  async getUnreadCount(req, res) {
    try {
      const userId = req.user.id;
      const connection = await mysql.createConnection(dbConfig);

      const query = `
        SELECT COUNT(*) as unread_count
        FROM announcements a
        LEFT JOIN announcement_reads ar ON a.id = ar.announcement_id AND ar.user_id = ?
        WHERE ar.id IS NULL
      `;

      const [result] = await connection.execute(query, [userId]);
      await connection.end();

      res.json({
        success: true,
        data: {
          unreadCount: result[0].unread_count
        }
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取未读公告数量失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '获取未读公告数量失败',
        error: error.message
      });
    }
  }

  /**
   * 更新公告信息
   */
  async updateAnnouncement(req, res) {
    try {
      // 检查管理员权限
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: '权限不足，只有管理员可以更新公告'
        });
      }

      const announcementId = req.params.id;
      const {
        title,
        content,
        summary,
        type,
        publisher,
        is_top,
        is_important,
        attachment_count
      } = req.body;

      const connection = await mysql.createConnection(dbConfig);

      // 检查公告是否存在
      const [existingAnnouncements] = await connection.execute(
        'SELECT id FROM announcements WHERE id = ?',
        [announcementId]
      );

      if (existingAnnouncements.length === 0) {
        await connection.end();
        return res.status(404).json({
          success: false,
          message: '公告不存在'
        });
      }

      // 更新公告
      const query = `
        UPDATE announcements
        SET title = ?, content = ?, summary = ?, type = ?, publisher = ?,
            is_top = ?, is_important = ?, attachment_count = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      await connection.execute(query, [
        title,
        content,
        summary,
        type,
        publisher,
        is_top,
        is_important,
        attachment_count,
        announcementId
      ]);

      await connection.end();

      res.json({
        success: true,
        message: '公告更新成功'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('更新公告失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '更新公告失败',
        error: error.message
      });
    }
  }

  /**
   * 删除公告
   */
  async deleteAnnouncement(req, res) {
    try {
      // 检查管理员权限
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: '权限不足，只有管理员可以删除公告'
        });
      }

      const announcementId = req.params.id;
      const connection = await mysql.createConnection(dbConfig);

      // 检查公告是否存在
      const [existingAnnouncements] = await connection.execute(
        'SELECT id FROM announcements WHERE id = ?',
        [announcementId]
      );

      if (existingAnnouncements.length === 0) {
        await connection.end();
        return res.status(404).json({
          success: false,
          message: '公告不存在'
        });
      }

      // 删除相关的已读记录
      await connection.execute(
        'DELETE FROM announcement_reads WHERE announcement_id = ?',
        [announcementId]
      );

      // 删除公告
      await connection.execute('DELETE FROM announcements WHERE id = ?', [
        announcementId
      ]);

      await connection.end();

      res.json({
        success: true,
        message: '公告删除成功'
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('删除公告失败:', error); } catch(_) {}

      res.status(500).json({
        success: false,
        message: '删除公告失败',
        error: error.message
      });
    }
  }
}

module.exports = new AnnouncementController();
