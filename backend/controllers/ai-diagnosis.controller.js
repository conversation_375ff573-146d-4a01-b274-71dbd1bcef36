const sequelize = require('sequelize');
const { Op } = require('sequelize');
const HealthRecord = require('../models/health-record.model');
const AIUsageStats = require('../models/ai-usage-stats.model');
const AIConfig = require('../models/ai-config.model');

// 基础诊断逻辑（基于症状关键词）
function performBasicDiagnosis(symptoms, images) {
  const symptomsLower = symptoms.toLowerCase();
  let diagnosis, confidence, suggestions, severity;

  // 呼吸道症状
  if (symptomsLower.includes('咳嗽') || symptomsLower.includes('呼吸困难') || symptomsLower.includes('喘息')) {
    diagnosis = '疑似呼吸道感染';
    confidence = 0.75;
    suggestions = ['增加鹅舍通风', '检查空气质量', '考虑使用抗生素治疗', '隔离观察'];
    severity = 'medium';
  }
  // 消化道症状
  else if (symptomsLower.includes('腹泻') || symptomsLower.includes('呕吐') || symptomsLower.includes('食欲不振')) {
    diagnosis = '疑似消化道疾病';
    confidence = 0.70;
    suggestions = ['检查饲料质量', '调整饮食', '补充益生菌', '保持环境清洁'];
    severity = 'medium';
  }
  // 行为异常
  else if (symptomsLower.includes('精神萎靡') || symptomsLower.includes('活动减少') || symptomsLower.includes('嗜睡')) {
    diagnosis = '疑似一般性疾病';
    confidence = 0.60;
    suggestions = ['观察体温变化', '检查饲养环境', '增加营养供给', '必要时就医'];
    severity = 'low';
  }
  // 外观异常
  else if (symptomsLower.includes('羽毛脱落') || symptomsLower.includes('皮肤异常') || symptomsLower.includes('肿胀')) {
    diagnosis = '疑似外部疾病';
    confidence = 0.65;
    suggestions = ['检查寄生虫', '改善环境卫生', '使用外用药物', '加强清洁消毒'];
    severity = 'medium';
  }
  // 默认情况
  else {
    diagnosis = '需要进一步观察';
    confidence = 0.50;
    suggestions = ['持续观察症状变化', '记录详细病史', '必要时联系兽医', '保持良好饲养环境'];
    severity = 'low';
  }

  return { diagnosis, confidence, suggestions, severity };
}

// 获取诊断记录列表
exports.getDiagnosisRecords = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const userId = req.user.id;

    // 从数据库获取AI诊断记录
    const { count, rows: records } = await HealthRecord.findAndCountAll({
      where: {
        userId: userId,
        checkType: 'ai_diagnosis'
      },
      limit: limit,
      offset: offset,
      order: [['createdAt', 'DESC']],
      attributes: ['id', 'gooseId', 'diagnosis', 'treatment', 'createdAt', 'symptoms']
    });

    // 格式化数据
    const formattedRecords = records.map(record => ({
      id: record.id,
      gooseId: record.gooseId || `G${record.id.toString().padStart(3, '0')}`,
      diagnosis: record.diagnosis || '健康检查',
      result: record.treatment || '诊断结果正常',
      symptoms: record.symptoms,
      createdAt: record.createdAt
    }));

    res.json({
      success: true,
      data: {
        records: formattedRecords,
        pagination: {
          page: page,
          limit: limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取诊断记录列表失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 添加诊断记录
exports.createDiagnosisRecord = async (req, res) => {
  try {
    const { gooseId, symptoms, diagnosis, treatment } = req.body;

    // 验证必填字段
    if (!gooseId || !symptoms) {
      return res.status(400).json({
        success: false,
        message: '鹅群编号和症状为必填项'
      });
    }

    // 创建健康记录
    const record = await HealthRecord.create({
      userId: req.user.id,
      gooseId,
      symptoms,
      diagnosis: diagnosis || '',
      treatment: treatment || '',
      status: 'completed'
    });

    res.status(201).json({
      success: true,
      message: '诊断记录添加成功',
      data: record
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('添加诊断记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// AI健康诊断
exports.aiDiagnosis = async (req, res) => {
  try {
    const { symptoms, images } = req.body;

    // 验证必填字段
    if (!symptoms) {
      return res.status(400).json({
        success: false,
        message: '症状描述为必填项'
      });
    }

    // 调用实际的AI服务进行诊断
    let diagnosisResult;
    
    try {
      // 获取默认AI配置
      const aiConfig = await AIConfig.findOne({
        where: { isDefault: true, enabled: true }
      });

      if (!aiConfig) {
        // 如果没有配置AI服务，返回基础诊断结果
        diagnosisResult = {
          diagnosis: '请配置AI诊断服务',
          confidence: 0.0,
          suggestions: ['请先在系统设置中配置AI诊断服务', '或联系管理员进行配置'],
          severity: 'info'
        };
      } else {
        // 这里可以接入真实的AI服务API
        // 目前提供基于症状的简单规则诊断
        diagnosisResult = performBasicDiagnosis(symptoms, images);
      }
    } catch (aiError) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('AI诊断服务调用失败', { error: aiError.message, stack: aiError.stack }); } catch(_) {}
      // 回退到基础诊断
      diagnosisResult = performBasicDiagnosis(symptoms, images);
    }

    // 记录AI使用统计
    await AIUsageStats.create({
      userId: req.user.id,
      provider: 'mock',
      model: 'mock-model',
      scenario: 'HEALTH_DIAGNOSIS',
      feature: 'ai_diagnosis',
      requestTokens: symptoms.length,
      responseTokens: JSON.stringify(diagnosisResult).length,
      totalTokens: symptoms.length + JSON.stringify(diagnosisResult).length,
      cost: 0.001,
      responseTime: Math.floor(Math.random() * 1000) + 500,
      success: true
    });

    res.json({
      success: true,
      message: 'AI诊断完成',
      data: diagnosisResult
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('AI诊断失败', { error: error.message, stack: error.stack }); } catch(_) {}

    // 记录失败的AI使用统计
    try {
      await AIUsageStats.create({
        userId: req.user.id,
        provider: 'mock',
        model: 'mock-model',
        scenario: 'HEALTH_DIAGNOSIS',
        feature: 'ai_diagnosis',
        success: false,
        errorMessage: error.message
      });
    } catch (statError) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('记录AI使用统计失败', { error: statError.message, stack: statError.stack }); } catch(_) {}
    }

    res.status(500).json({
      success: false,
      message: 'AI诊断失败',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取AI诊断历史
exports.getAIDiagnosisHistory = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // 获取AI使用统计中健康诊断相关的记录
    const { count, rows } = await AIUsageStats.findAndCountAll({
      where: {
        userId: req.user.id,
        scenario: 'HEALTH_DIAGNOSIS'
      },
      limit: limit,
      offset: offset,
      order: [['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        history: rows,
        pagination: {
          page: page,
          limit: limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取AI诊断历史失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

// 获取AI诊断详情
exports.getAIDiagnosisById = async (req, res) => {
  try {
    const { id } = req.params;

    const record = await AIUsageStats.findOne({
      where: {
        id: id,
        userId: req.user.id
      }
    });

    if (!record) {
      return res.status(404).json({
        success: false,
        message: '诊断记录不存在'
      });
    }

    res.json({
      success: true,
      data: record
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取AI诊断详情失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};
