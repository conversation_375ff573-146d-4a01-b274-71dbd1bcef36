const HelpCenterModel = require('../models/help-center.model');
const { successResponse, errorResponse } = require('../utils/response-helper');

class HelpCenterController {

  // ======================== 公开API接口 ========================

  // 获取帮助分类 - 公开接口
  static async getCategories(req, res) {
    try {
      const categories = await HelpCenterModel.getCategories();
      
      // 格式化数据为小程序需要的格式
      const formattedCategories = categories.map(category => ({
        id: category.id,
        title: category.name,
        icon: category.icon,
        description: category.description,
        articles: [] // 这里可以根据需要加载文章
      }));

      return successResponse(res, '获取分类成功', {
        categories: formattedCategories
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取帮助分类失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '获取分类失败', 500);
    }
  }

  // 获取分类详情 - 公开接口
  static async getCategoryDetail(req, res) {
    try {
      const { id } = req.params;
      const category = await HelpCenterModel.getCategoryById(id);
      
      if (!category) {
        return errorResponse(res, '分类不存在', 404);
      }

      // 获取该分类下的文章
      const articles = await HelpCenterModel.getArticles({ category_id: id, limit: 20 });
      
      // 获取该分类下的FAQ
      const faqs = await HelpCenterModel.getFAQs({ category_id: id, limit: 10 });

      return successResponse(res, '获取分类详情成功', {
        category: {
          id: category.id,
          name: category.name,
          description: category.description,
          icon: category.icon,
          article_count: category.article_count,
          faq_count: category.faq_count
        },
        articles: articles.map(article => ({
          id: article.id,
          title: article.title,
          summary: article.summary,
          view_count: article.view_count,
          created_at: article.created_at
        })),
        faqs: faqs.map(faq => ({
          id: faq.id,
          question: faq.question,
          answer: faq.answer,
          helpful_count: faq.helpful_count,
          expanded: false
        }))
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取分类详情失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '获取分类详情失败', 500);
    }
  }

  // 获取文章列表 - 公开接口
  static async getArticles(req, res) {
    try {
      const { category_id, keyword, limit = 20, order_by } = req.query;
      
      const filters = {
        category_id,
        keyword,
        limit: parseInt(limit),
        order_by
      };

      const articles = await HelpCenterModel.getArticles(filters);
      
      const formattedArticles = articles.map(article => ({
        id: article.id,
        title: article.title,
        summary: article.summary,
        view_count: article.view_count,
        like_count: article.like_count,
        category_name: article.category_name,
        category_icon: article.category_icon,
        created_at: article.created_at,
        tags: JSON.parse(article.tags || '[]')
      }));

      return successResponse(res, '获取文章列表成功', {
        articles: formattedArticles
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取文章列表失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '获取文章列表失败', 500);
    }
  }

  // 获取文章详情 - 公开接口
  static async getArticleDetail(req, res) {
    try {
      const { id } = req.params;
      const article = await HelpCenterModel.getArticleById(id);
      
      if (!article) {
        return errorResponse(res, '文章不存在', 404);
      }

      const formattedArticle = {
        id: article.id,
        title: article.title,
        summary: article.summary,
        content: article.content,
        content_type: article.content_type,
        featured_image: article.featured_image,
        view_count: article.view_count,
        like_count: article.like_count,
        helpful_count: article.helpful_count,
        unhelpful_count: article.unhelpful_count,
        category_name: article.category_name,
        category_icon: article.category_icon,
        created_at: article.created_at,
        updated_at: article.updated_at,
        tags: JSON.parse(article.tags || '[]')
      };

      return successResponse(res, '获取文章详情成功', {
        article: formattedArticle
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取文章详情失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '获取文章详情失败', 500);
    }
  }

  // 获取FAQ列表 - 公开接口
  static async getFAQs(req, res) {
    try {
      const { category_id, keyword, featured, limit = 20 } = req.query;
      
      const filters = {
        category_id,
        keyword,
        featured: featured === 'true',
        limit: parseInt(limit)
      };

      const faqs = await HelpCenterModel.getFAQs(filters);
      
      const formattedFAQs = faqs.map(faq => ({
        id: faq.id,
        question: faq.question,
        answer: faq.answer,
        helpful_count: faq.helpful_count,
        unhelpful_count: faq.unhelpful_count,
        view_count: faq.view_count,
        category_name: faq.category_name,
        expanded: false,
        tags: JSON.parse(faq.tags || '[]')
      }));

      return successResponse(res, '获取FAQ列表成功', {
        faqs: formattedFAQs
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取FAQ列表失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '获取FAQ列表失败', 500);
    }
  }

  // 获取热门教程 - 公开接口
  static async getTutorials(req, res) {
    try {
      const { category_id, difficulty, featured, limit = 10 } = req.query;
      
      const filters = {
        category_id,
        difficulty,
        featured: featured === 'true',
        limit: parseInt(limit)
      };

      const tutorials = await HelpCenterModel.getTutorials(filters);
      
      const formattedTutorials = tutorials.map(tutorial => ({
        id: tutorial.id,
        title: tutorial.title,
        description: tutorial.description,
        thumbnail: tutorial.thumbnail,
        duration: tutorial.duration,
        difficulty: tutorial.difficulty,
        view_count: tutorial.view_count,
        like_count: tutorial.like_count,
        category_name: tutorial.category_name,
        created_at: tutorial.created_at,
        tags: JSON.parse(tutorial.tags || '[]')
      }));

      return successResponse(res, '获取教程列表成功', {
        tutorials: formattedTutorials
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取教程列表失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '获取教程列表失败', 500);
    }
  }

  // 获取教程详情 - 公开接口
  static async getTutorialDetail(req, res) {
    try {
      const { id } = req.params;
      const tutorial = await HelpCenterModel.getTutorialById(id);
      
      if (!tutorial) {
        return errorResponse(res, '教程不存在', 404);
      }

      const formattedTutorial = {
        id: tutorial.id,
        title: tutorial.title,
        description: tutorial.description,
        content: tutorial.content,
        thumbnail: tutorial.thumbnail,
        video_url: tutorial.video_url,
        duration: tutorial.duration,
        difficulty: tutorial.difficulty,
        view_count: tutorial.view_count,
        like_count: tutorial.like_count,
        category_name: tutorial.category_name,
        steps: JSON.parse(tutorial.steps || '[]'),
        tags: JSON.parse(tutorial.tags || '[]'),
        created_at: tutorial.created_at
      };

      return successResponse(res, '获取教程详情成功', {
        tutorial: formattedTutorial
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取教程详情失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '获取教程详情失败', 500);
    }
  }

  // 综合搜索 - 公开接口
  static async search(req, res) {
    try {
      const { keyword, type } = req.query;
      
      if (!keyword || keyword.trim().length < 2) {
        return errorResponse(res, '搜索关键词至少需要2个字符', 400);
      }

      const searchFilters = {
        user_id: req.user?.id,
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      };

      const results = await HelpCenterModel.search(keyword.trim(), searchFilters);
      
      // 根据类型过滤结果
      const filteredResults = {
        total: 0,
        articles: [],
        faqs: [],
        tutorials: []
      };

      if (!type || type === 'article') {
        filteredResults.articles = results.articles.map(item => ({
          id: item.id,
          title: item.title,
          content: item.content,
          view_count: item.view_count,
          type: 'article'
        }));
      }

      if (!type || type === 'faq') {
        filteredResults.faqs = results.faqs.map(item => ({
          id: item.id,
          question: item.title,
          answer: item.content,
          view_count: item.view_count,
          type: 'faq'
        }));
      }

      if (!type || type === 'tutorial') {
        filteredResults.tutorials = results.tutorials.map(item => ({
          id: item.id,
          title: item.title,
          description: item.content,
          view_count: item.view_count,
          type: 'tutorial'
        }));
      }

      filteredResults.total = filteredResults.articles.length + 
                             filteredResults.faqs.length + 
                             filteredResults.tutorials.length;

      return successResponse(res, '搜索成功', filteredResults);
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('搜索失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '搜索失败', 500);
    }
  }

  // 获取搜索建议 - 公开接口
  static async getSearchSuggestions(req, res) {
    try {
      const { keyword } = req.query;
      
      if (!keyword || keyword.trim().length < 1) {
        return successResponse(res, '搜索建议获取成功', { suggestions: [] });
      }

      const suggestions = await HelpCenterModel.getSearchSuggestions(keyword.trim());
      
      return successResponse(res, '搜索建议获取成功', {
        suggestions: suggestions.map(item => item.keyword)
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取搜索建议失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '获取搜索建议失败', 500);
    }
  }

  // 提交反馈 - 公开接口
  static async submitFeedback(req, res) {
    try {
      const { type, target_id, rating, is_helpful, content, contact_info } = req.body;
      
      if (!type) {
        return errorResponse(res, '反馈类型不能为空', 400);
      }

      const feedbackData = {
        type,
        target_id,
        user_id: req.user?.id,
        rating,
        is_helpful,
        content,
        contact_info,
        ip_address: req.ip
      };

      const feedbackId = await HelpCenterModel.submitFeedback(feedbackData);
      
      return successResponse(res, '反馈提交成功', {
        feedback_id: feedbackId
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('提交反馈失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '提交反馈失败', 500);
    }
  }

  // 获取帮助中心统计 - 公开接口
  static async getStatistics(req, res) {
    try {
      const statistics = await HelpCenterModel.getStatistics();
      
      return successResponse(res, '获取统计数据成功', {
        statistics: {
          total_articles: statistics.total_articles,
          total_views: statistics.total_views,
          total_questions: statistics.total_faqs,
          satisfaction_rate: statistics.satisfaction_rate
        }
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取统计数据失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '获取统计数据失败', 500);
    }
  }

  // 获取公开设置 - 公开接口
  static async getPublicSettings(req, res) {
    try {
      const settings = await HelpCenterModel.getPublicSettings();
      
      return successResponse(res, '获取设置成功', {
        settings
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取设置失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '获取设置失败', 500);
    }
  }

  // ======================== 管理员API接口 ========================

  // 创建分类 - 管理员接口
  static async createCategory(req, res) {
    try {
      const { name, description, icon, sort_order } = req.body;
      
      if (!name) {
        return errorResponse(res, '分类名称不能为空', 400);
      }

      const categoryData = {
        name,
        description,
        icon,
        sort_order,
        created_by: req.user.id
      };

      const categoryId = await HelpCenterModel.createCategory(categoryData);
      
      return successResponse(res, '创建分类成功', {
        category_id: categoryId
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('创建分类失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '创建分类失败', 500);
    }
  }

  // 更新分类 - 管理员接口
  static async updateCategory(req, res) {
    try {
      const { id } = req.params;
      const { name, description, icon, sort_order } = req.body;
      
      if (!name) {
        return errorResponse(res, '分类名称不能为空', 400);
      }

      const categoryData = {
        name,
        description,
        icon,
        sort_order,
        updated_by: req.user.id
      };

      await HelpCenterModel.updateCategory(id, categoryData);
      
      return successResponse(res, '更新分类成功');
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('更新分类失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '更新分类失败', 500);
    }
  }

  // 删除分类 - 管理员接口
  static async deleteCategory(req, res) {
    try {
      const { id } = req.params;
      
      await HelpCenterModel.deleteCategory(id);
      
      return successResponse(res, '删除分类成功');
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('删除分类失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '删除分类失败', 500);
    }
  }

  // 创建文章 - 管理员接口
  static async createArticle(req, res) {
    try {
      const {
        category_id, title, summary, content, content_type,
        featured_image, tags, meta_keywords, meta_description,
        sort_order, is_published, is_featured, publish_at
      } = req.body;
      
      if (!title || !content) {
        return errorResponse(res, '标题和内容不能为空', 400);
      }

      const articleData = {
        category_id,
        title,
        summary,
        content,
        content_type,
        featured_image,
        tags,
        meta_keywords,
        meta_description,
        sort_order,
        is_published,
        is_featured,
        publish_at,
        created_by: req.user.id
      };

      const articleId = await HelpCenterModel.createArticle(articleData);
      
      return successResponse(res, '创建文章成功', {
        article_id: articleId
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('创建文章失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '创建文章失败', 500);
    }
  }

  // 更新文章 - 管理员接口
  static async updateArticle(req, res) {
    try {
      const { id } = req.params;
      const {
        category_id, title, summary, content, content_type,
        featured_image, tags, meta_keywords, meta_description,
        sort_order, is_published, is_featured, publish_at
      } = req.body;
      
      if (!title || !content) {
        return errorResponse(res, '标题和内容不能为空', 400);
      }

      const articleData = {
        category_id,
        title,
        summary,
        content,
        content_type,
        featured_image,
        tags,
        meta_keywords,
        meta_description,
        sort_order,
        is_published,
        is_featured,
        publish_at,
        updated_by: req.user.id
      };

      await HelpCenterModel.updateArticle(id, articleData);
      
      return successResponse(res, '更新文章成功');
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('更新文章失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '更新文章失败', 500);
    }
  }

  // 删除文章 - 管理员接口
  static async deleteArticle(req, res) {
    try {
      const { id } = req.params;
      
      await HelpCenterModel.deleteArticle(id);
      
      return successResponse(res, '删除文章成功');
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('删除文章失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '删除文章失败', 500);
    }
  }

  // 创建FAQ - 管理员接口
  static async createFAQ(req, res) {
    try {
      const {
        category_id, question, answer, tags, sort_order,
        is_published, is_featured
      } = req.body;
      
      if (!question || !answer) {
        return errorResponse(res, '问题和答案不能为空', 400);
      }

      const faqData = {
        category_id,
        question,
        answer,
        tags,
        sort_order,
        is_published,
        is_featured,
        created_by: req.user.id
      };

      const faqId = await HelpCenterModel.createFAQ(faqData);
      
      return successResponse(res, '创建FAQ成功', {
        faq_id: faqId
      });
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('创建FAQ失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '创建FAQ失败', 500);
    }
  }

  // 更新FAQ - 管理员接口
  static async updateFAQ(req, res) {
    try {
      const { id } = req.params;
      const {
        category_id, question, answer, tags, sort_order,
        is_published, is_featured
      } = req.body;
      
      if (!question || !answer) {
        return errorResponse(res, '问题和答案不能为空', 400);
      }

      const faqData = {
        category_id,
        question,
        answer,
        tags,
        sort_order,
        is_published,
        is_featured,
        updated_by: req.user.id
      };

      await HelpCenterModel.updateFAQ(id, faqData);
      
      return successResponse(res, '更新FAQ成功');
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('更新FAQ失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '更新FAQ失败', 500);
    }
  }

  // 删除FAQ - 管理员接口
  static async deleteFAQ(req, res) {
    try {
      const { id } = req.params;
      
      await HelpCenterModel.deleteFAQ(id);
      
      return successResponse(res, '删除FAQ成功');
    } catch (error) {
      try { const { Logger } = require('../middleware/errorHandler'); Logger.error('删除FAQ失败', { error: error.message, stack: error.stack }); } catch(_) {}
      return errorResponse(res, '删除FAQ失败', 500);
    }
  }

  // 获取管理员统计面板数据 - 管理员接口
  static async getAdminStatistics(req, res) {
    try {
      const statistics = await HelpCenterModel.getStatistics();
      
      return successResponse(res, '获取管理统计数据成功', {
        statistics
      });
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取管理统计数据失败:', error); } catch(_) {}

      return errorResponse(res, '获取管理统计数据失败', 500);
    }
  }

  // 更新设置 - 管理员接口
  static async updateSetting(req, res) {
    try {
      const { key, value, type = 'string' } = req.body;
      
      if (!key) {
        return errorResponse(res, '设置键不能为空', 400);
      }

      await HelpCenterModel.updateSetting(key, value, type);
      
      return successResponse(res, '更新设置成功');
    } catch (error) {
      try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('更新设置失败:', error); } catch(_) {}

      return errorResponse(res, '更新设置失败', 500);
    }
  }
}

module.exports = HelpCenterController;