const Flock = require('../models/flock.model');
const { Op } = require('sequelize');
const ResponseHelper = require('../utils/response-helper');
const ValidationHelper = require('../utils/validation-helper');

/**
 * 获取鹅群列表
 */
exports.getFlocks = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      breed = '',
      ageGroup = '',
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = req.query;

    // 验证分页参数
    if (
      !ValidationHelper.validateNumber(page, 1) ||
      !ValidationHelper.validateNumber(limit, 1, 100)
    ) {
      return ResponseHelper.error(res, '分页参数无效', 400);
    }

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // 构建查询条件
    const whereConditions = { userId: req.user.id };

    // 搜索条件
    if (search) {
      whereConditions[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { batchNumber: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    // 状态筛选
    if (status) {
      whereConditions.status = status;
    }

    // 品种筛选
    if (breed) {
      whereConditions.breed = breed;
    }

    // 年龄组筛选
    if (ageGroup) {
      whereConditions.ageGroup = ageGroup;
    }

    // 排序验证
    const allowedSortFields = [
      'createdAt',
      'name',
      'totalCount',
      'currentCount',
      'establishedDate'
    ];
    const sortField = allowedSortFields.includes(sortBy) ? sortBy : 'createdAt';
    const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const { count, rows } = await Flock.findAndCountAll({
      where: whereConditions,
      limit: parseInt(limit),
      offset: offset,
      order: [[sortField, order]]
    });

    // 转换数据格式
    const flocks = rows.map((flock) => ({
      id: flock.id,
      name: flock.name,
      batchNumber: flock.batchNumber,
      breed: flock.breed,
      totalCount: flock.totalCount,
      currentCount: flock.currentCount,
      maleCount: flock.maleCount,
      femaleCount: flock.femaleCount,
      ageGroup: flock.ageGroup,
      status: flock.status,
      establishedDate: flock.establishedDate,
      location: flock.location,
      description: flock.description,
      healthStatus: this.calculateHealthStatus(flock),
      productionRate: this.calculateProductionRate(flock),
      createdAt: flock.createdAt,
      updatedAt: flock.updatedAt
    }));

    const paginatedData = ResponseHelper.paginate(flocks, page, limit, count);
    return ResponseHelper.success(res, paginatedData, '获取鹅群列表成功');
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取鹅群列表失败:', error); } catch(_) {}

    return ResponseHelper.error(res, '获取鹅群列表失败', 500, error.message);
  }
};

/**
 * 创建鹅群
 */
exports.createFlock = async (req, res) => {
  try {
    const {
      name,
      breed,
      totalCount,
      maleCount,
      femaleCount,
      ageGroup,
      establishedDate,
      location,
      description
    } = req.body;

    // 验证必填字段
    const requiredFields = ['name', 'breed', 'totalCount'];
    const missing = ValidationHelper.validateRequired(requiredFields, req.body);
    if (missing.length > 0) {
      return ResponseHelper.error(
        res,
        `缺少必填字段: ${missing.join(', ')}`,
        400
      );
    }

    // 验证数量
    if (!ValidationHelper.validateNumber(totalCount, 1)) {
      return ResponseHelper.error(res, '总数量必须大于0', 400);
    }

    // 验证性别数量
    const male = parseInt(maleCount) || 0;
    const female = parseInt(femaleCount) || 0;
    if (male + female !== parseInt(totalCount)) {
      return ResponseHelper.error(res, '雄性和雌性数量之和必须等于总数量', 400);
    }

    // 生成批次号
    const batchNumber = `FLOCK-${Date.now()}`;

    // 创建鹅群
    const flock = await Flock.create({
      userId: req.user.id,
      name,
      batchNumber,
      breed,
      totalCount: parseInt(totalCount),
      currentCount: parseInt(totalCount),
      maleCount: male,
      femaleCount: female,
      ageGroup: ageGroup || 'young',
      status: 'active',
      establishedDate: establishedDate || new Date(),
      location,
      description
    });

    return ResponseHelper.success(res, flock, '鹅群创建成功', 201);
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('创建鹅群失败:', error); } catch(_) {}

    return ResponseHelper.error(res, '创建鹅群失败', 500, error.message);
  }
};

/**
 * 获取鹅群详情
 */
exports.getFlockById = async (req, res) => {
  try {
    const { id } = req.params;

    const flock = await Flock.findOne({
      where: {
        id,
        userId: req.user.id
      }
    });

    if (!flock) {
      return ResponseHelper.error(res, '鹅群不存在', 404);
    }

    // 添加计算字段
    const flockData = {
      ...flock.toJSON(),
      healthStatus: this.calculateHealthStatus(flock),
      productionRate: this.calculateProductionRate(flock),
      survivalRate: this.calculateSurvivalRate(flock),
      ageInDays: this.calculateAgeInDays(flock.establishedDate)
    };

    return ResponseHelper.success(res, flockData, '获取鹅群详情成功');
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('获取鹅群详情失败:', error); } catch(_) {}

    return ResponseHelper.error(res, '获取鹅群详情失败', 500, error.message);
  }
};

/**
 * 更新鹅群
 */
exports.updateFlock = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      breed,
      currentCount,
      maleCount,
      femaleCount,
      ageGroup,
      status,
      location,
      description
    } = req.body;

    const flock = await Flock.findOne({
      where: {
        id,
        userId: req.user.id
      }
    });

    if (!flock) {
      return ResponseHelper.error(res, '鹅群不存在', 404);
    }

    // 验证当前数量不能超过总数量
    if (currentCount && parseInt(currentCount) > flock.totalCount) {
      return ResponseHelper.error(res, '当前数量不能超过总数量', 400);
    }

    // 验证性别数量
    if (maleCount !== undefined && femaleCount !== undefined) {
      const male = parseInt(maleCount) || 0;
      const female = parseInt(femaleCount) || 0;
      const current = currentCount
        ? parseInt(currentCount)
        : flock.currentCount;

      if (male + female !== current) {
        return ResponseHelper.error(
          res,
          '雄性和雌性数量之和必须等于当前数量',
          400
        );
      }
    }

    // 更新鹅群信息
    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (breed !== undefined) updateData.breed = breed;
    if (currentCount !== undefined)
      updateData.currentCount = parseInt(currentCount);
    if (maleCount !== undefined) updateData.maleCount = parseInt(maleCount);
    if (femaleCount !== undefined)
      updateData.femaleCount = parseInt(femaleCount);
    if (ageGroup !== undefined) updateData.ageGroup = ageGroup;
    if (status !== undefined) updateData.status = status;
    if (location !== undefined) updateData.location = location;
    if (description !== undefined) updateData.description = description;

    await flock.update(updateData);

    // 获取更新后的数据
    const updatedFlock = await Flock.findByPk(id);
    return ResponseHelper.success(res, updatedFlock, '鹅群更新成功');
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('更新鹅群失败:', error); } catch(_) {}

    return ResponseHelper.error(res, '更新鹅群失败', 500, error.message);
  }
};

/**
 * 删除鹅群
 */
exports.deleteFlock = async (req, res) => {
  try {
    const { id } = req.params;

    const flock = await Flock.findOne({
      where: {
        id,
        userId: req.user.id
      }
    });

    if (!flock) {
      return ResponseHelper.error(res, '鹅群不存在', 404);
    }

    // 软删除或硬删除
    await flock.destroy();

    return ResponseHelper.success(res, null, '鹅群删除成功');
  } catch (error) {
    try { const { Logger } = require('..\/middleware\/errorHandler'); Logger.error('删除鹅群失败:', error); } catch(_) {}

    return ResponseHelper.error(res, '删除鹅群失败', 500, error.message);
  }
};

// 辅助方法
exports.calculateHealthStatus = (flock) => {
  // 根据存活率计算健康状态
  const survivalRate = flock.currentCount / flock.totalCount;
  if (survivalRate >= 0.95) return 'excellent';
  if (survivalRate >= 0.9) return 'good';
  if (survivalRate >= 0.8) return 'fair';
  return 'poor';
};

exports.calculateProductionRate = (flock) => {
  // 这里应该根据实际的生产记录计算
  // 从数据库查询鹅群统计数据
  return Math.floor(Math.random() * 30) + 70; // 70-100%
};

exports.calculateSurvivalRate = (flock) => {
  return ((flock.currentCount / flock.totalCount) * 100).toFixed(2);
};

exports.calculateAgeInDays = (establishedDate) => {
  const now = new Date();
  const established = new Date(establishedDate);
  const diffTime = Math.abs(now - established);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// 添加缺失的方法别名以兼容统一API路由
exports.getFlockDetail = exports.getFlockById;
exports.getFlockStatistics = async (req, res) => {
  try {
    const { id } = req.params;
    // 模拟鹅群统计数据，实际应从数据库获取
    const statistics = {
      flockId: parseInt(id),
      name: `鹅群-${id}`,
      totalGeese: 500,
      healthyGeese: 475,
      sickGeese: 25,
      healthStatus: 'excellent',
      productionRate: 85.2,
      survivalRate: 95.0,
      ageInDays: 120,
      lastUpdated: new Date().toISOString()
    };

    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取鹅群统计失败',
      error: error.message
    });
  }
};

exports.batchOperations = async (req, res) => {
  try {
    const { operation, flockIds, data } = req.body;
    
    if (!flockIds || !Array.isArray(flockIds)) {
      return res.status(400).json({
        success: false,
        message: '鹅群ID列表不能为空'
      });
    }

    // 模拟批量操作结果
    const result = { 
      affected: flockIds.length, 
      failed: [],
      operation,
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      message: `批量${operation}操作完成`,
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '批量操作失败',
      error: error.message
    });
  }
};
