/**
 * 平台管理控制器
 * Platform Admin Controller
 */

const ResponseHelper = require('../utils/response-helper');

/**
 * 获取平台统计
 */
const getPlatformStatistics = async (req, res) => {
  try {
    const statistics = {
      overview: {
        totalTenants: 156,
        activeTenants: 142,
        totalUsers: 3520,
        totalRevenue: 1256000,
        monthlyGrowth: 12.5
      },
      tenants: {
        new: 12,
        active: 142,
        suspended: 8,
        expired: 6
      },
      revenue: {
        total: 1256000,
        thisMonth: 125600,
        lastMonth: 118200,
        growth: 6.3
      },
      usage: {
        totalApiCalls: 12500000,
        totalDataUsage: '2.5TB',
        avgCallsPerTenant: 80128,
        avgDataPerTenant: '16.4GB'
      },
      performance: {
        uptime: 99.95,
        responseTime: 145,
        errorRate: 0.02,
        satisfaction: 4.8
      }
    };

    return ResponseHelper.success(res, statistics, '获取平台统计成功');
  } catch (error) {
    console.error('获取平台统计失败:', error);
    return ResponseHelper.error(res, '获取平台统计失败', 500, error);
  }
};

/**
 * 获取平台分析数据
 */
const getPlatformAnalytics = async (req, res) => {
  try {
    const { period = '30d', metric } = req.query;

    const analytics = {
      period,
      tenantGrowth: [
        { date: '2024-01-01', count: 144 },
        { date: '2024-01-02', count: 145 },
        { date: '2024-01-03', count: 147 },
        { date: '2024-01-04', count: 149 },
        { date: '2024-01-05', count: 152 }
      ],
      revenueGrowth: [
        { date: '2024-01-01', amount: 118200 },
        { date: '2024-01-02', amount: 119500 },
        { date: '2024-01-03', amount: 121800 },
        { date: '2024-01-04', amount: 123200 },
        { date: '2024-01-05', amount: 125600 }
      ],
      userActivity: [
        { date: '2024-01-01', activeUsers: 2850 },
        { date: '2024-01-02', activeUsers: 2920 },
        { date: '2024-01-03', activeUsers: 3120 },
        { date: '2024-01-04', activeUsers: 3280 },
        { date: '2024-01-05', activeUsers: 3520 }
      ],
      apiUsage: [
        { date: '2024-01-01', calls: 850000 },
        { date: '2024-01-02', calls: 920000 },
        { date: '2024-01-03', calls: 1120000 },
        { date: '2024-01-04', calls: 1280000 },
        { date: '2024-01-05', calls: 1350000 }
      ],
      featureUsage: {
        health: 95.2,
        production: 88.7,
        oa: 76.3,
        shop: 62.1,
        ai: 45.8
      }
    };

    return ResponseHelper.success(res, analytics, '获取平台分析数据成功');
  } catch (error) {
    console.error('获取平台分析数据失败:', error);
    return ResponseHelper.error(res, '获取平台分析数据失败', 500, error);
  }
};

/**
 * 获取收入分析
 */
const getRevenueAnalytics = async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    const revenueAnalytics = {
      period,
      total: {
        amount: 1256000,
        growth: 12.5,
        target: 1400000,
        completion: 89.7
      },
      byPlan: {
        basic: { amount: 225600, percentage: 18.0, tenants: 45 },
        standard: { amount: 536800, percentage: 42.7, tenants: 67 },
        premium: { amount: 360000, percentage: 28.7, tenants: 30 },
        enterprise: { amount: 133600, percentage: 10.6, tenants: 14 }
      },
      monthly: [
        { month: '2023-08', amount: 985000 },
        { month: '2023-09', amount: 1025000 },
        { month: '2023-10', amount: 1085000 },
        { month: '2023-11', amount: 1155000 },
        { month: '2023-12', amount: 1225000 },
        { month: '2024-01', amount: 1256000 }
      ],
      forecast: {
        nextMonth: 1320000,
        nextQuarter: 4100000,
        confidence: 85
      },
      churn: {
        rate: 2.3,
        lost: 28800,
        recovered: 15600
      }
    };

    return ResponseHelper.success(res, revenueAnalytics, '获取收入分析成功');
  } catch (error) {
    console.error('获取收入分析失败:', error);
    return ResponseHelper.error(res, '获取收入分析失败', 500, error);
  }
};

/**
 * 获取使用情况分析
 */
const getUsageAnalytics = async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    const usageAnalytics = {
      period,
      api: {
        totalCalls: 12500000,
        avgCallsPerDay: 416667,
        peakCalls: 650000,
        efficiency: 94.2
      },
      storage: {
        totalUsage: '2.5TB',
        avgPerTenant: '16.4GB',
        growth: '12.3%',
        capacity: '10TB'
      },
      features: {
        health: { usage: 95.2, growth: 5.8 },
        production: { usage: 88.7, growth: 3.2 },
        oa: { usage: 76.3, growth: 8.1 },
        shop: { usage: 62.1, growth: 15.2 },
        ai: { usage: 45.8, growth: 28.7 }
      },
      performance: {
        responseTime: {
          avg: 145,
          p95: 280,
          p99: 450
        },
        uptime: 99.95,
        errorRate: 0.02
      },
      geographic: {
        china: 78.5,
        asia: 12.3,
        america: 6.2,
        europe: 2.1,
        others: 0.9
      }
    };

    return ResponseHelper.success(res, usageAnalytics, '获取使用情况分析成功');
  } catch (error) {
    console.error('获取使用情况分析失败:', error);
    return ResponseHelper.error(res, '获取使用情况分析失败', 500, error);
  }
};

/**
 * 获取系统健康状态
 */
const getSystemHealth = async (req, res) => {
  try {
    const health = {
      overall: 'healthy',
      score: 98.5,
      lastCheck: new Date().toISOString(),
      services: {
        api: {
          status: 'healthy',
          responseTime: 145,
          uptime: 99.95,
          instances: 3
        },
        database: {
          status: 'healthy',
          connections: 85,
          maxConnections: 200,
          queryTime: 25
        },
        cache: {
          status: 'healthy',
          hitRate: 94.2,
          memory: '2.8GB',
          maxMemory: '4GB'
        },
        storage: {
          status: 'healthy',
          usage: '2.5TB',
          capacity: '10TB',
          iops: 2500
        },
        cdn: {
          status: 'healthy',
          hitRate: 96.8,
          bandwidth: '125Mbps',
          locations: 15
        }
      },
      metrics: {
        cpu: 45.2,
        memory: 68.7,
        disk: 25.0,
        network: 12.5
      },
      alerts: [
        {
          level: 'warning',
          message: 'API响应时间略高于正常水平',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString()
        }
      ]
    };

    return ResponseHelper.success(res, health);
  } catch (error) {
    console.error('获取系统健康状态失败:', error);
    return ResponseHelper.error(res, '获取系统健康状态失败', 500, error);
  }
};

/**
 * 获取系统日志
 */
const getSystemLogs = async (req, res) => {
  try {
    const { level, startDate, endDate, limit = 100 } = req.query;

    // 模拟系统日志
    const logs = [
      {
        id: 1,
        timestamp: new Date().toISOString(),
        level: 'info',
        service: 'api',
        message: 'API服务启动成功',
        metadata: { port: 3001, version: '2.0.0' }
      },
      {
        id: 2,
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        level: 'warning',
        service: 'database',
        message: '数据库连接池使用率较高',
        metadata: { usage: 85, max: 100 }
      },
      {
        id: 3,
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        level: 'error',
        service: 'auth',
        message: '用户认证失败次数过多',
        metadata: { userId: 123, attempts: 5 }
      }
    ];

    // 过滤日志
    let filteredLogs = logs;
    if (level) {
      filteredLogs = filteredLogs.filter(log => log.level === level);
    }

    // 限制数量
    const limitedLogs = filteredLogs.slice(0, parseInt(limit));

    return ResponseHelper.success(res, {
      logs: limitedLogs,
      total: filteredLogs.length,
      summary: {
        info: logs.filter(l => l.level === 'info').length,
        warning: logs.filter(l => l.level === 'warning').length,
        error: logs.filter(l => l.level === 'error').length
      }
    });
  } catch (error) {
    console.error('获取系统日志失败:', error);
    return ResponseHelper.error(res, '获取系统日志失败');
  }
};

/**
 * 获取错误日志
 */
const getErrorLogs = async (req, res) => {
  try {
    const { severity, startDate, endDate, limit = 50 } = req.query;

    // 模拟错误日志
    const errorLogs = [
      {
        id: 1,
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        severity: 'high',
        service: 'auth',
        error: 'Authentication failed',
        message: '用户认证失败次数过多',
        stack: 'Error: Authentication failed\n    at AuthController.login...',
        tenant: 'DEMO001',
        userId: 123,
        resolved: false
      },
      {
        id: 2,
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        severity: 'medium',
        service: 'api',
        error: 'Database connection timeout',
        message: '数据库连接超时',
        stack: 'Error: Connection timeout\n    at Database.connect...',
        tenant: 'DEMO002',
        userId: null,
        resolved: true
      }
    ];

    // 过滤错误日志
    let filteredLogs = errorLogs;
    if (severity) {
      filteredLogs = filteredLogs.filter(log => log.severity === severity);
    }

    // 限制数量
    const limitedLogs = filteredLogs.slice(0, parseInt(limit));

    return ResponseHelper.success(res, {
      errors: limitedLogs,
      total: filteredLogs.length,
      summary: {
        high: errorLogs.filter(e => e.severity === 'high').length,
        medium: errorLogs.filter(e => e.severity === 'medium').length,
        low: errorLogs.filter(e => e.severity === 'low').length,
        resolved: errorLogs.filter(e => e.resolved).length,
        unresolved: errorLogs.filter(e => !e.resolved).length
      }
    });
  } catch (error) {
    console.error('获取错误日志失败:', error);
    return ResponseHelper.error(res, '获取错误日志失败');
  }
};

/**
 * 获取订阅管理
 */
const getSubscriptions = async (req, res) => {
  try {
    const { status, plan, expiring } = req.query;

    // 模拟订阅数据
    const subscriptions = [
      {
        id: 1,
        tenantId: 1,
        tenantName: '示例农场A',
        plan: 'standard',
        status: 'active',
        startDate: '2024-01-01T00:00:00Z',
        endDate: '2024-12-31T23:59:59Z',
        autoRenew: true,
        amount: 8000,
        features: ['health', 'production', 'oa', 'shop'],
        daysToExpiry: 351
      },
      {
        id: 2,
        tenantId: 2,
        tenantName: '绿色养殖场',
        plan: 'premium',
        status: 'active',
        startDate: '2024-01-01T00:00:00Z',
        endDate: '2024-12-31T23:59:59Z',
        autoRenew: false,
        amount: 15000,
        features: ['health', 'production', 'oa', 'shop', 'ai'],
        daysToExpiry: 351
      }
    ];

    // 过滤订阅
    let filteredSubscriptions = subscriptions;
    if (status) {
      filteredSubscriptions = filteredSubscriptions.filter(s => s.status === status);
    }
    if (plan) {
      filteredSubscriptions = filteredSubscriptions.filter(s => s.plan === plan);
    }
    if (expiring === 'true') {
      filteredSubscriptions = filteredSubscriptions.filter(s => s.daysToExpiry <= 30);
    }

    const summary = {
      total: subscriptions.length,
      active: subscriptions.filter(s => s.status === 'active').length,
      expiring: subscriptions.filter(s => s.daysToExpiry <= 30).length,
      autoRenew: subscriptions.filter(s => s.autoRenew).length,
      totalRevenue: subscriptions.reduce((sum, s) => sum + s.amount, 0)
    };

    return ResponseHelper.success(res, {
      subscriptions: filteredSubscriptions,
      summary
    });
  } catch (error) {
    console.error('获取订阅管理失败:', error);
    return ResponseHelper.error(res, '获取订阅管理失败');
  }
};

/**
 * 获取订阅计划
 */
const getSubscriptionPlans = async (req, res) => {
  try {
    const plans = [
      {
        id: 'basic',
        name: '基础版',
        price: 3000,
        currency: 'CNY',
        period: 'year',
        features: [
          '最多5个鹅群',
          '基础健康管理',
          '生产记录',
          '5GB存储空间',
          '邮件支持'
        ],
        limits: {
          flocks: 5,
          users: 10,
          storage: '5GB',
          apiCalls: 50000
        },
        popular: false
      },
      {
        id: 'standard',
        name: '标准版',
        price: 8000,
        currency: 'CNY',
        period: 'year',
        features: [
          '最多20个鹅群',
          '完整健康管理',
          '生产管理系统',
          'OA办公系统',
          '20GB存储空间',
          '电话+邮件支持'
        ],
        limits: {
          flocks: 20,
          users: 30,
          storage: '20GB',
          apiCalls: 200000
        },
        popular: true
      },
      {
        id: 'premium',
        name: '高级版',
        price: 15000,
        currency: 'CNY',
        period: 'year',
        features: [
          '无限鹅群',
          'AI智能诊断',
          '商城系统',
          '高级分析报表',
          '50GB存储空间',
          '7x24小时支持'
        ],
        limits: {
          flocks: -1,
          users: 100,
          storage: '50GB',
          apiCalls: 500000
        },
        popular: false
      },
      {
        id: 'enterprise',
        name: '企业版',
        price: 30000,
        currency: 'CNY',
        period: 'year',
        features: [
          '所有功能',
          '私有化部署',
          '定制开发',
          '无限存储',
          '专属客服',
          'SLA保证'
        ],
        limits: {
          flocks: -1,
          users: -1,
          storage: 'unlimited',
          apiCalls: -1
        },
        popular: false
      }
    ];

    return ResponseHelper.success(res, { plans });
  } catch (error) {
    console.error('获取订阅计划失败:', error);
    return ResponseHelper.error(res, '获取订阅计划失败');
  }
};

/**
 * 获取计费信息
 */
const getBillingInfo = async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    const billing = {
      period,
      summary: {
        totalRevenue: 1256000,
        paidInvoices: 142,
        pendingInvoices: 8,
        overdueInvoices: 3,
        refunds: 2400
      },
      monthly: [
        { month: '2023-08', revenue: 985000, invoices: 130 },
        { month: '2023-09', revenue: 1025000, invoices: 132 },
        { month: '2023-10', revenue: 1085000, invoices: 135 },
        { month: '2023-11', revenue: 1155000, invoices: 138 },
        { month: '2023-12', revenue: 1225000, invoices: 140 },
        { month: '2024-01', revenue: 1256000, invoices: 142 }
      ],
      byPlan: [
        { plan: 'basic', revenue: 225600, percentage: 18.0 },
        { plan: 'standard', revenue: 536800, percentage: 42.7 },
        { plan: 'premium', revenue: 360000, percentage: 28.7 },
        { plan: 'enterprise', revenue: 133600, percentage: 10.6 }
      ],
      paymentMethods: {
        alipay: 45.2,
        wechat: 38.7,
        bank: 12.1,
        credit: 4.0
      },
      metrics: {
        arpu: 8051, // 平均每用户收入
        ltv: 32204, // 用户生命周期价值
        churnRate: 2.3,
        renewalRate: 89.7
      }
    };

    return ResponseHelper.success(res, billing);
  } catch (error) {
    console.error('获取计费信息失败:', error);
    return ResponseHelper.error(res, '获取计费信息失败');
  }
};

module.exports = {
  getPlatformStatistics,
  getPlatformAnalytics,
  getRevenueAnalytics,
  getUsageAnalytics,
  getSystemHealth,
  getSystemLogs,
  getErrorLogs,
  getSubscriptions,
  getSubscriptionPlans,
  getBillingInfo
};