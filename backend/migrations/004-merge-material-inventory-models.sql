-- 合并Material和Inventory模型数据
-- Migration: 004-merge-material-inventory-models.sql

-- 1. 创建统一的库存管理表
CREATE TABLE IF NOT EXISTS unified_inventory (
  id INT AUTO_INCREMENT PRIMARY KEY,
  
  -- 基础信息
  user_id INT NOT NULL,
  name VARCHAR(100) NOT NULL,
  category ENUM('feed', 'medicine', 'equipment', 'materials', 'other') NOT NULL,
  specification VARCHAR(100),
  unit VARCHAR(20) NOT NULL DEFAULT '个',
  
  -- 库存管理
  current_stock DECIMAL(12, 3) NOT NULL DEFAULT 0,
  min_stock DECIMAL(12, 3) DEFAULT 0,
  max_stock DECIMAL(12, 3),
  
  -- 价格信息
  unit_price DECIMAL(10, 2),
  total_value DECIMAL(15, 2),
  
  -- 供应商信息
  supplier VARCHAR(100),
  supplier_contact VARCHAR(100),
  
  -- 日期信息
  purchase_date DATE,
  expiry_date DATE,
  last_update_date DATE,
  
  -- 状态和位置
  status ENUM('normal', 'low_stock', 'out_of_stock', 'expired', 'warning', 'inactive') NOT NULL DEFAULT 'normal',
  location VARCHAR(100),
  
  -- 其他信息
  description TEXT,
  batch_number VARCHAR(50),
  
  -- 审计信息
  created_by INT,
  updated_by INT,
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- 外键约束
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  
  -- 索引
  INDEX idx_user_id (user_id),
  INDEX idx_category (category),
  INDEX idx_status (status),
  INDEX idx_name (name),
  INDEX idx_expiry_date (expiry_date),
  INDEX idx_purchase_date (purchase_date),
  INDEX idx_current_stock (current_stock),
  INDEX idx_user_category (user_id, category),
  INDEX idx_user_status (user_id, status),
  INDEX idx_low_stock_check (user_id, current_stock, min_stock),
  INDEX idx_expiry_check (status, expiry_date),
  INDEX idx_name_category (name, category)
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='统一库存管理表';

-- 2. 迁移materials表数据到unified_inventory
INSERT INTO unified_inventory (
  user_id, name, category, specification, unit,
  current_stock, min_stock, max_stock, unit_price,
  supplier, supplier_contact, purchase_date, expiry_date,
  status, location, description, created_by, created_at, updated_at
)
SELECT 
  user_id,
  name,
  CASE 
    WHEN category = 'feed' THEN 'feed'
    WHEN category = 'medicine' THEN 'medicine' 
    ELSE 'materials'
  END as category,
  spec as specification,
  unit,
  stock as current_stock,
  min_stock,
  max_stock,
  unit_price,
  supplier,
  supplier_contact,
  purchase_date,
  expiry_date,
  CASE 
    WHEN status = 'normal' THEN 'normal'
    WHEN status = 'warning' THEN 'warning'
    WHEN status = 'danger' THEN 'low_stock'
    WHEN status = 'expired' THEN 'expired'
    ELSE 'normal'
  END as status,
  location,
  description,
  user_id as created_by,
  created_at,
  updated_at
FROM materials 
WHERE EXISTS (SELECT 1 FROM materials);

-- 3. 迁移inventory表数据到unified_inventory (仅在表存在时执行)
-- 注意：当前环境中inventory表不存在，跳过此步骤

-- 4. 更新总价值字段（自动计算）
UPDATE unified_inventory 
SET total_value = current_stock * unit_price 
WHERE unit_price IS NOT NULL AND current_stock IS NOT NULL;

-- 5. 更新状态（基于库存数量）
UPDATE unified_inventory 
SET status = CASE 
  WHEN current_stock <= 0 THEN 'out_of_stock'
  WHEN min_stock IS NOT NULL AND current_stock <= min_stock THEN 'low_stock'
  WHEN expiry_date IS NOT NULL AND expiry_date < CURDATE() THEN 'expired'
  ELSE 'normal'
END;

-- 6. 创建备份表
CREATE TABLE IF NOT EXISTS materials_backup AS SELECT * FROM materials;
-- inventory表不存在，无需备份

-- 7. 添加迁移记录注释
ALTER TABLE unified_inventory COMMENT='统一库存管理表 - 合并materials功能';

-- 验证迁移结果
SELECT 
  'Migration Summary' as info,
  (SELECT COUNT(*) FROM materials) as original_materials_count,
  0 as original_inventory_count,
  (SELECT COUNT(*) FROM unified_inventory) as unified_inventory_count,
  (SELECT COUNT(*) FROM unified_inventory WHERE category = 'feed') as feed_items,
  (SELECT COUNT(*) FROM unified_inventory WHERE category = 'medicine') as medicine_items,
  (SELECT COUNT(*) FROM unified_inventory WHERE status = 'low_stock') as low_stock_items,
  (SELECT COUNT(*) FROM unified_inventory WHERE status = 'expired') as expired_items;