-- 标准化inventory表结构（snake_case命名）
-- Migration: 002-standardize-inventory-table.sql

-- 如果表已存在，先备份数据
CREATE TABLE IF NOT EXISTS inventory_backup AS SELECT * FROM inventory;

-- 删除旧表（如果存在）
DROP TABLE IF EXISTS inventory;

-- 创建标准化的inventory表
CREATE TABLE IF NOT EXISTS inventory (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  name VARCHAR(100) NOT NULL,
  category VARCHAR(50) NOT NULL COMMENT '物料类别 (feed, medicine, equipment, other)',
  specification VARCHAR(100) COMMENT '规格型号',
  unit VARCHAR(20) NOT NULL COMMENT '计量单位',
  
  -- 库存数量相关
  current_stock DECIMAL(10, 2) DEFAULT 0 COMMENT '当前库存',
  min_stock DECIMAL(10, 2) DEFAULT 0 COMMENT '最低库存警戒线',
  max_stock DECIMAL(10, 2) COMMENT '最高库存',
  
  -- 价格相关
  unit_price DECIMAL(10, 2) COMMENT '单价',
  total_value DECIMAL(12, 2) COMMENT '总价值',
  
  -- 供应商相关
  supplier VARCHAR(100) COMMENT '供应商',
  supplier_contact VARCHAR(100) COMMENT '供应商联系方式',
  
  -- 日期相关
  purchase_date DATE COMMENT '采购日期',
  expiry_date DATE COMMENT '过期日期',
  last_update_date DATE COMMENT '最后更新日期',
  
  -- 状态和备注
  status ENUM('available', 'low_stock', 'out_of_stock', 'expired') DEFAULT 'available',
  location VARCHAR(100) COMMENT '存放位置',
  notes TEXT COMMENT '备注',
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- 外键约束
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  
  -- 索引优化
  INDEX idx_user_id (user_id),
  INDEX idx_category (category),
  INDEX idx_status (status),
  INDEX idx_name (name),
  INDEX idx_expiry_date (expiry_date),
  INDEX idx_current_stock (current_stock),
  
  -- 复合索引
  INDEX idx_user_category (user_id, category),
  INDEX idx_user_status (user_id, status),
  INDEX idx_low_stock (user_id, current_stock, min_stock)
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存管理表';

-- 如果有备份数据，可以选择性恢复
-- INSERT INTO inventory SELECT * FROM inventory_backup;