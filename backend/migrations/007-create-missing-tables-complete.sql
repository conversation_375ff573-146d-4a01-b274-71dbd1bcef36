-- 智慧养鹅全栈系统完整数据库表创建脚本
-- Complete Database Tables Creation Script for Smart Goose Management System
-- 创建日期: 2024年12月
-- 目标: 创建所有缺失的数据表，确保前后端功能完整性

-- =============================================
-- 第一部分: 基础系统表
-- =============================================

-- 1. AI配置表
CREATE TABLE IF NOT EXISTS ai_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL COMMENT '用户ID（null表示全局配置）',
    service_type ENUM('chat', 'diagnosis', 'analysis', 'recommendation') NOT NULL COMMENT 'AI服务类型',
    provider ENUM('openai', 'claude', 'baidu', 'aliyun', 'custom') NOT NULL COMMENT 'AI服务提供商',
    model_name VARCHAR(100) NULL COMMENT '模型名称',
    api_key VARCHAR(500) NULL COMMENT 'API密钥（加密存储）',
    api_url VARCHAR(500) NULL COMMENT 'API地址',
    config_data JSON NULL COMMENT '配置数据',
    status ENUM('active', 'inactive', 'testing') DEFAULT 'active' COMMENT '配置状态',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_service_type (service_type),
    INDEX idx_provider (provider),
    INDEX idx_status (status),
    INDEX idx_is_default (is_default)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI配置表';

-- 2. AI使用统计表
CREATE TABLE IF NOT EXISTS ai_usage_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL COMMENT '用户ID',
    service_type ENUM('chat', 'diagnosis', 'analysis', 'recommendation') NOT NULL COMMENT 'AI服务类型',
    provider ENUM('openai', 'claude', 'baidu', 'aliyun', 'custom') NOT NULL COMMENT 'AI服务提供商',
    model_name VARCHAR(100) NULL COMMENT '模型名称',
    request_count INT DEFAULT 0 COMMENT '请求次数',
    token_consumed INT DEFAULT 0 COMMENT '消耗token数',
    success_count INT DEFAULT 0 COMMENT '成功次数',
    error_count INT DEFAULT 0 COMMENT '错误次数',
    total_cost DECIMAL(10, 4) DEFAULT 0 COMMENT '总成本',
    stats_date DATE NOT NULL COMMENT '统计日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_service_type (service_type),
    INDEX idx_stats_date (stats_date),
    INDEX idx_user_date (user_id, stats_date),
    UNIQUE KEY unique_user_service_date (user_id, service_type, provider, stats_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI使用统计表';

-- 3. 公告表
CREATE TABLE IF NOT EXISTS announcements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '公告标题',
    content TEXT NOT NULL COMMENT '公告内容',
    type ENUM('notice', 'maintenance', 'feature', 'urgent') DEFAULT 'notice' COMMENT '公告类型',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
    target_audience ENUM('all', 'admins', 'users', 'managers') DEFAULT 'all' COMMENT '目标受众',
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '状态',
    publish_at DATETIME NULL COMMENT '发布时间',
    expires_at DATETIME NULL COMMENT '过期时间',
    author_id INT NOT NULL COMMENT '作者ID',
    attachments JSON NULL COMMENT '附件信息',
    read_count INT DEFAULT 0 COMMENT '阅读次数',
    is_pinned BOOLEAN DEFAULT FALSE COMMENT '是否置顶',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_status (status),
    INDEX idx_type (type),
    INDEX idx_priority (priority),
    INDEX idx_target_audience (target_audience),
    INDEX idx_publish_at (publish_at),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_pinned (is_pinned)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公告表';

-- =============================================
-- 第二部分: OA工作台系统表
-- =============================================

-- 4. 任务表
CREATE TABLE IF NOT EXISTS tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '创建者ID',
    assigned_to INT NULL COMMENT '分配给用户ID',
    title VARCHAR(200) NOT NULL COMMENT '任务标题',
    description TEXT NULL COMMENT '任务描述',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '任务状态',
    category VARCHAR(50) NULL COMMENT '任务分类',
    due_date DATETIME NULL COMMENT '截止日期',
    completed_at DATETIME NULL COMMENT '完成时间',
    progress INT DEFAULT 0 COMMENT '完成进度(0-100)',
    attachments JSON NULL COMMENT '附件列表',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_due_date (due_date),
    INDEX idx_category (category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';

-- 5. 财务申请表
CREATE TABLE IF NOT EXISTS finance_applications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_no VARCHAR(50) NOT NULL UNIQUE COMMENT '申请编号',
    type ENUM('expense', 'purchase', 'payment', 'contract', 'activity', 'reserve') NOT NULL COMMENT '申请类型',
    title VARCHAR(200) NOT NULL COMMENT '申请标题',
    description TEXT NULL COMMENT '申请描述',
    amount DECIMAL(15, 2) NOT NULL COMMENT '申请金额',
    currency VARCHAR(10) DEFAULT 'CNY' COMMENT '货币类型',
    applicant_id INT NOT NULL COMMENT '申请人ID',
    department_id INT NULL COMMENT '部门ID',
    status ENUM('draft', 'pending', 'approved', 'rejected', 'cancelled', 'processing', 'completed') DEFAULT 'draft' COMMENT '申请状态',
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal' COMMENT '优先级',
    application_date DATE NOT NULL COMMENT '申请日期',
    expected_date DATE NULL COMMENT '期望完成日期',
    approved_date DATETIME NULL COMMENT '批准日期',
    approved_by INT NULL COMMENT '批准人ID',
    attachments JSON NULL COMMENT '附件信息',
    metadata JSON NULL COMMENT '扩展元数据',
    remarks TEXT NULL COMMENT '备注信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (applicant_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_application_no (application_no),
    INDEX idx_type (type),
    INDEX idx_applicant_id (applicant_id),
    INDEX idx_status (status),
    INDEX idx_application_date (application_date),
    INDEX idx_priority (priority)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='财务申请表';

-- 6. 审批流程表
CREATE TABLE IF NOT EXISTS approval_workflows (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_id INT NOT NULL COMMENT '申请ID',
    application_type ENUM('expense', 'purchase', 'payment', 'contract', 'activity', 'reserve') NOT NULL COMMENT '申请类型',
    step_number INT NOT NULL COMMENT '审批步骤',
    step_name VARCHAR(100) NOT NULL COMMENT '步骤名称',
    approver_id INT NULL COMMENT '审批人ID',
    approver_role ENUM('manager', 'finance', 'admin', 'hr', 'legal') NOT NULL COMMENT '审批人角色',
    status ENUM('pending', 'approved', 'rejected', 'skipped') DEFAULT 'pending' COMMENT '审批状态',
    decision_reason TEXT NULL COMMENT '审批决定原因',
    approved_at DATETIME NULL COMMENT '审批时间',
    is_required BOOLEAN DEFAULT TRUE COMMENT '是否必须审批',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (application_id) REFERENCES finance_applications(id) ON DELETE CASCADE,
    FOREIGN KEY (approver_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_application_id (application_id),
    INDEX idx_application_type (application_type),
    INDEX idx_approver_id (approver_id),
    INDEX idx_status (status),
    INDEX idx_step_number (step_number),
    UNIQUE KEY unique_application_step (application_id, step_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审批流程表';

-- 7. 审批流程模板表
CREATE TABLE IF NOT EXISTS approval_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '模板名称',
    application_type ENUM('expense', 'purchase', 'payment', 'contract', 'activity', 'reserve') NOT NULL COMMENT '申请类型',
    amount_range JSON NULL COMMENT '金额范围 {"min": 0, "max": 50000}',
    steps JSON NOT NULL COMMENT '审批步骤配置',
    description TEXT NULL COMMENT '模板描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_by INT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_application_type (application_type),
    INDEX idx_is_active (is_active),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审批流程模板表';

-- 8. 财务记录表（扩展版）
CREATE TABLE IF NOT EXISTS finance_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    related_flock_id INT NULL COMMENT '关联鹅群ID',
    related_application_id INT NULL COMMENT '关联财务申请ID',
    type ENUM('income', 'expense') NOT NULL COMMENT '类型：收入/支出',
    category ENUM('feed', 'medicine', 'equipment', 'labor', 'utilities', 'sales', 'other') NOT NULL COMMENT '分类',
    subcategory VARCHAR(50) NULL COMMENT '子分类',
    amount DECIMAL(15, 2) NOT NULL COMMENT '金额',
    currency VARCHAR(10) DEFAULT 'CNY' COMMENT '货币类型',
    description TEXT NULL COMMENT '描述',
    record_date DATE NOT NULL COMMENT '记录日期',
    payment_method ENUM('cash', 'bank_transfer', 'alipay', 'wechat', 'credit_card', 'other') NULL COMMENT '支付方式',
    invoice_number VARCHAR(100) NULL COMMENT '发票号码',
    supplier_customer VARCHAR(100) NULL COMMENT '供应商/客户',
    attachments JSON NULL COMMENT '附件信息',
    tags JSON NULL COMMENT '标签',
    notes TEXT NULL COMMENT '备注',
    status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'confirmed' COMMENT '状态',
    created_by INT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (related_flock_id) REFERENCES flocks(id) ON DELETE SET NULL,
    FOREIGN KEY (related_application_id) REFERENCES finance_applications(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_category (category),
    INDEX idx_record_date (record_date),
    INDEX idx_status (status),
    INDEX idx_related_flock_id (related_flock_id),
    INDEX idx_related_application_id (related_application_id),
    INDEX idx_user_date (user_id, record_date),
    INDEX idx_type_category (type, category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='财务记录表';

-- =============================================
-- 第三部分: 商城系统表
-- =============================================

-- 9. 商品表
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL COMMENT '商品名称',
    description TEXT NULL COMMENT '商品描述',
    category ENUM('feed', 'medicine', 'equipment', 'supplies', 'books', 'other') NOT NULL COMMENT '商品分类',
    subcategory VARCHAR(50) NULL COMMENT '子分类',
    brand VARCHAR(100) NULL COMMENT '品牌',
    model VARCHAR(100) NULL COMMENT '型号规格',
    price DECIMAL(10, 2) NOT NULL COMMENT '价格',
    original_price DECIMAL(10, 2) NULL COMMENT '原价',
    cost_price DECIMAL(10, 2) NULL COMMENT '成本价',
    stock_quantity INT DEFAULT 0 COMMENT '库存数量',
    min_stock_quantity INT DEFAULT 0 COMMENT '最低库存量',
    unit VARCHAR(20) DEFAULT '个' COMMENT '计量单位',
    weight DECIMAL(8, 3) NULL COMMENT '重量(kg)',
    dimensions VARCHAR(100) NULL COMMENT '尺寸',
    images JSON NULL COMMENT '商品图片',
    specifications JSON NULL COMMENT '规格参数',
    features JSON NULL COMMENT '特性标签',
    status ENUM('active', 'inactive', 'out_of_stock', 'discontinued') DEFAULT 'active' COMMENT '状态',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐商品',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    sales_count INT DEFAULT 0 COMMENT '销量',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    rating DECIMAL(3, 2) DEFAULT 0.00 COMMENT '评分',
    review_count INT DEFAULT 0 COMMENT '评价数量',
    supplier_id INT NULL COMMENT '供应商ID',
    created_by INT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (supplier_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_name (name),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_price (price),
    INDEX idx_is_featured (is_featured),
    INDEX idx_sort_order (sort_order),
    INDEX idx_sales_count (sales_count),
    INDEX idx_created_at (created_at),
    INDEX idx_category_status (category, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 10. 订单表
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    user_id INT NOT NULL COMMENT '用户ID',
    total_amount DECIMAL(12, 2) NOT NULL COMMENT '订单总金额',
    discount_amount DECIMAL(10, 2) DEFAULT 0 COMMENT '优惠金额',
    shipping_fee DECIMAL(10, 2) DEFAULT 0 COMMENT '运费',
    final_amount DECIMAL(12, 2) NOT NULL COMMENT '实付金额',
    status ENUM('pending', 'paid', 'shipped', 'delivered', 'completed', 'cancelled', 'refunded') DEFAULT 'pending' COMMENT '订单状态',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending' COMMENT '支付状态',
    payment_method ENUM('wechat', 'alipay', 'bank_card', 'cash', 'other') NULL COMMENT '支付方式',
    payment_time DATETIME NULL COMMENT '支付时间',
    shipping_address JSON NOT NULL COMMENT '收货地址信息',
    shipping_method VARCHAR(50) NULL COMMENT '配送方式',
    tracking_number VARCHAR(100) NULL COMMENT '物流单号',
    shipping_time DATETIME NULL COMMENT '发货时间',
    delivery_time DATETIME NULL COMMENT '收货时间',
    notes TEXT NULL COMMENT '订单备注',
    cancel_reason TEXT NULL COMMENT '取消原因',
    refund_reason TEXT NULL COMMENT '退款原因',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_order_number (order_number),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at),
    INDEX idx_user_status (user_id, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 11. 订单商品表
CREATE TABLE IF NOT EXISTS order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL COMMENT '订单ID',
    product_id INT NOT NULL COMMENT '商品ID',
    product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    product_image VARCHAR(500) NULL COMMENT '商品图片',
    specification VARCHAR(200) NULL COMMENT '商品规格',
    quantity INT NOT NULL COMMENT '购买数量',
    unit_price DECIMAL(10, 2) NOT NULL COMMENT '单价',
    total_price DECIMAL(12, 2) NOT NULL COMMENT '小计',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    
    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单商品表';

-- 12. 购物车表
CREATE TABLE IF NOT EXISTS shopping_cart (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    product_id INT NOT NULL COMMENT '商品ID',
    quantity INT NOT NULL DEFAULT 1 COMMENT '数量',
    specification VARCHAR(200) NULL COMMENT '选中规格',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    UNIQUE KEY unique_user_product (user_id, product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购物车表';

-- =============================================
-- 第四部分: 扩展功能表
-- =============================================

-- 13. 知识库表
CREATE TABLE IF NOT EXISTS knowledge_base (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '标题',
    content TEXT NOT NULL COMMENT '内容',
    category ENUM('breeding', 'health', 'feed', 'disease', 'management', 'marketing', 'other') NOT NULL COMMENT '分类',
    tags JSON NULL COMMENT '标签',
    difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner' COMMENT '难度等级',
    views INT DEFAULT 0 COMMENT '浏览次数',
    likes INT DEFAULT 0 COMMENT '点赞数',
    status ENUM('draft', 'published', 'archived') DEFAULT 'published' COMMENT '状态',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    author_id INT NOT NULL COMMENT '作者ID',
    attachments JSON NULL COMMENT '附件信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_is_featured (is_featured),
    INDEX idx_author_id (author_id),
    INDEX idx_views (views),
    INDEX idx_likes (likes),
    FULLTEXT idx_content (title, content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库表';

-- 14. 帮助中心分类表
CREATE TABLE IF NOT EXISTS help_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description TEXT NULL COMMENT '分类描述',
    icon VARCHAR(100) NULL COMMENT '分类图标',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帮助中心分类表';

-- 15. 帮助中心文章表
CREATE TABLE IF NOT EXISTS help_articles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL COMMENT '分类ID',
    title VARCHAR(200) NOT NULL COMMENT '文章标题',
    content TEXT NOT NULL COMMENT '文章内容',
    summary VARCHAR(500) NULL COMMENT '文章摘要',
    tags JSON NULL COMMENT '标签',
    views INT DEFAULT 0 COMMENT '浏览次数',
    helpful_count INT DEFAULT 0 COMMENT '有用数',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    status ENUM('draft', 'published', 'archived') DEFAULT 'published' COMMENT '状态',
    author_id INT NOT NULL COMMENT '作者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (category_id) REFERENCES help_categories(id) ON DELETE CASCADE,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order),
    INDEX idx_author_id (author_id),
    INDEX idx_views (views),
    FULLTEXT idx_content (title, content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帮助中心文章表';

-- 16. 用户组表
CREATE TABLE IF NOT EXISTS user_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '用户组名称',
    description TEXT NULL COMMENT '描述',
    permissions JSON NULL COMMENT '权限列表',
    is_system_group BOOLEAN DEFAULT FALSE COMMENT '是否系统用户组',
    created_by INT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_name (name),
    INDEX idx_is_system_group (is_system_group)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户组表';

-- 17. 用户组成员表
CREATE TABLE IF NOT EXISTS user_group_members (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    group_id INT NOT NULL COMMENT '用户组ID',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES user_groups(id) ON DELETE CASCADE,
    
    INDEX idx_user_id (user_id),
    INDEX idx_group_id (group_id),
    UNIQUE KEY unique_user_group (user_id, group_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户组成员表';

-- 18. 权限表
CREATE TABLE IF NOT EXISTS permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '权限名称',
    display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
    description TEXT NULL COMMENT '权限描述',
    resource VARCHAR(100) NOT NULL COMMENT '资源名',
    action VARCHAR(50) NOT NULL COMMENT '动作',
    module VARCHAR(50) NOT NULL COMMENT '模块',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统权限',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_name (name),
    INDEX idx_resource (resource),
    INDEX idx_module (module),
    INDEX idx_is_system (is_system)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 19. 审计日志表
CREATE TABLE IF NOT EXISTS audit_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL COMMENT '操作用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) NULL COMMENT '资源类型',
    resource_id VARCHAR(50) NULL COMMENT '资源ID',
    details JSON NULL COMMENT '操作详情',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    user_agent TEXT NULL COMMENT '用户代理',
    status ENUM('success', 'failed', 'error') NULL COMMENT '操作状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource_type (resource_type),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审计日志表';

-- 20. 通知表
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '接收用户ID',
    title VARCHAR(200) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    type ENUM('system', 'task', 'approval', 'order', 'health', 'production', 'other') DEFAULT 'system' COMMENT '通知类型',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
    status ENUM('unread', 'read', 'archived') DEFAULT 'unread' COMMENT '状态',
    related_type VARCHAR(50) NULL COMMENT '关联资源类型',
    related_id INT NULL COMMENT '关联资源ID',
    action_url VARCHAR(500) NULL COMMENT '操作链接',
    expires_at DATETIME NULL COMMENT '过期时间',
    read_at DATETIME NULL COMMENT '阅读时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at),
    INDEX idx_user_status (user_id, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

-- =============================================
-- 第五部分: 插入初始数据
-- =============================================

-- 插入默认帮助中心分类
INSERT IGNORE INTO help_categories (name, description, icon, sort_order) VALUES
('快速入门', '帮助新用户快速了解系统功能', 'icon_start', 1),
('鹅群管理', '鹅群建立、管理相关帮助', 'icon_flock', 2),
('健康管理', '健康记录、诊断相关帮助', 'icon_health', 3),
('生产管理', '生产记录、统计相关帮助', 'icon_production', 4),
('库存管理', '饲料、药品等库存管理帮助', 'icon_inventory', 5),
('财务管理', '财务记录、报表相关帮助', 'icon_finance', 6),
('商城购买', '商城购物、订单相关帮助', 'icon_shop', 7),
('常见问题', '用户常见问题解答', 'icon_faq', 8);

-- 插入默认知识库内容
INSERT IGNORE INTO knowledge_base (title, content, category, tags, author_id) VALUES
('鹅的基本饲养知识', '鹅是水禽类家畜，具有适应性强、生长快的特点...', 'breeding', '["基础知识", "饲养"]', 1),
('常见鹅病预防与治疗', '鹅的常见疾病包括感冒、腹泻等，预防措施包括...', 'health', '["疾病预防", "治疗"]', 1),
('优化鹅蛋产量的方法', '提高鹅蛋产量需要从饲料配比、环境控制等方面入手...', 'management', '["产蛋", "管理"]', 1);

-- 插入基础权限
INSERT IGNORE INTO permissions (name, display_name, description, resource, action, module) VALUES
-- 用户管理权限
('user.view', '查看用户', '查看用户列表和详情', 'user', 'view', 'user'),
('user.create', '创建用户', '创建新用户', 'user', 'create', 'user'),
('user.edit', '编辑用户', '编辑用户信息', 'user', 'edit', 'user'),
('user.delete', '删除用户', '删除用户账号', 'user', 'delete', 'user'),

-- 鹅群管理权限
('flock.view', '查看鹅群', '查看鹅群列表和详情', 'flock', 'view', 'flock'),
('flock.create', '创建鹅群', '创建新鹅群', 'flock', 'create', 'flock'),
('flock.edit', '编辑鹅群', '编辑鹅群信息', 'flock', 'edit', 'flock'),
('flock.delete', '删除鹅群', '删除鹅群记录', 'flock', 'delete', 'flock'),

-- 健康管理权限
('health.view', '查看健康记录', '查看健康记录列表', 'health', 'view', 'health'),
('health.create', '创建健康记录', '创建新健康记录', 'health', 'create', 'health'),
('health.edit', '编辑健康记录', '编辑健康记录', 'health', 'edit', 'health'),
('health.delete', '删除健康记录', '删除健康记录', 'health', 'delete', 'health'),

-- 生产管理权限
('production.view', '查看生产记录', '查看生产记录列表', 'production', 'view', 'production'),
('production.create', '创建生产记录', '创建新生产记录', 'production', 'create', 'production'),
('production.edit', '编辑生产记录', '编辑生产记录', 'production', 'edit', 'production'),
('production.delete', '删除生产记录', '删除生产记录', 'production', 'delete', 'production'),

-- 库存管理权限
('inventory.view', '查看库存', '查看库存列表', 'inventory', 'view', 'inventory'),
('inventory.create', '添加库存', '添加新库存记录', 'inventory', 'create', 'inventory'),
('inventory.edit', '编辑库存', '编辑库存信息', 'inventory', 'edit', 'inventory'),
('inventory.delete', '删除库存', '删除库存记录', 'inventory', 'delete', 'inventory'),

-- 财务管理权限
('finance.view', '查看财务记录', '查看财务记录列表', 'finance', 'view', 'finance'),
('finance.create', '创建财务记录', '创建新财务记录', 'finance', 'create', 'finance'),
('finance.approve', '审批财务申请', '审批财务相关申请', 'finance', 'approve', 'finance'),

-- 系统管理权限
('system.manage', '系统管理', '系统配置和管理', 'system', 'manage', 'system'),
('system.audit', '审计日志', '查看系统审计日志', 'system', 'audit', 'system');

-- 插入默认用户组
INSERT IGNORE INTO user_groups (name, description, is_system_group, created_by) VALUES
('超级管理员', '拥有所有权限的超级管理员组', TRUE, 1),
('普通管理员', '拥有大部分管理权限的管理员组', TRUE, 1),
('财务人员', '负责财务相关工作的用户组', TRUE, 1),
('普通用户', '标准用户权限组', TRUE, 1);

-- 插入默认审批流程模板
INSERT IGNORE INTO approval_templates (name, application_type, amount_range, steps, description, created_by) VALUES
('费用报销审批流程', 'expense', '{"min": 0, "max": 50000}', '[{"step": 1, "name": "直接上级审批", "role": "manager"}, {"step": 2, "name": "财务审批", "role": "finance"}, {"step": 3, "name": "管理员最终审批", "role": "admin"}]', '标准费用报销审批流程', 1),
('采购申请审批流程', 'purchase', '{"min": 0, "max": 100000}', '[{"step": 1, "name": "部门经理审批", "role": "manager"}, {"step": 2, "name": "财务审批", "role": "finance"}, {"step": 3, "name": "管理员审批", "role": "admin"}]', '标准采购申请审批流程', 1),
('付款申请审批流程', 'payment', '{"min": 0, "max": 200000}', '[{"step": 1, "name": "财务初审", "role": "finance"}, {"step": 2, "name": "管理员审批", "role": "admin"}]', '付款申请审批流程', 1);

-- =============================================
-- 第六部分: 创建视图和存储过程
-- =============================================

-- 创建用户鹅群统计视图
CREATE OR REPLACE VIEW user_flock_summary AS
SELECT 
    u.id as user_id,
    u.username,
    u.name,
    COUNT(f.id) as total_flocks,
    SUM(f.current_count) as total_geese,
    AVG(f.current_count) as avg_flock_size,
    COUNT(CASE WHEN f.status = 'active' THEN 1 END) as active_flocks
FROM users u
LEFT JOIN flocks f ON u.id = f.user_id
GROUP BY u.id, u.username, u.name;

-- 创建财务统计视图
CREATE OR REPLACE VIEW finance_summary AS
SELECT 
    user_id,
    DATE_FORMAT(record_date, '%Y-%m') as month,
    SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as total_income,
    SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as total_expense,
    SUM(CASE WHEN type = 'income' THEN amount ELSE -amount END) as net_amount,
    COUNT(*) as record_count
FROM finance_records
WHERE status = 'confirmed'
GROUP BY user_id, DATE_FORMAT(record_date, '%Y-%m');

-- 创建库存警报视图
CREATE OR REPLACE VIEW inventory_alerts AS
SELECT 
    ui.*,
    u.username,
    u.name as user_name,
    CASE 
        WHEN ui.current_stock <= ui.min_stock THEN 'low_stock'
        WHEN ui.expiry_date <= CURDATE() + INTERVAL 30 DAY THEN 'expiring_soon'
        WHEN ui.expiry_date <= CURDATE() THEN 'expired'
        ELSE 'normal'
    END as alert_type
FROM unified_inventory ui
JOIN users u ON ui.user_id = u.id
WHERE ui.status = 'normal'
    AND (ui.current_stock <= ui.min_stock 
         OR ui.expiry_date <= CURDATE() + INTERVAL 30 DAY);

-- =============================================
-- 完成提示
-- =============================================

SELECT 
    '智慧养鹅全栈系统数据库表创建完成' as status,
    NOW() as completion_time,
    '请运行应用程序验证功能完整性' as next_step;

-- 显示创建的表统计
SELECT 
    TABLE_NAME as table_name,
    TABLE_COMMENT as comment,
    TABLE_ROWS as estimated_rows
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;