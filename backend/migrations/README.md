# 数据库约束迁移指南

## 概述

这个目录包含了智慧养鹅系统的数据库约束迁移文件，用于确保数据完整性和一致性。

## 迁移文件说明

### 001-create-flocks-table.sql

- 创建标准化的鹅群表
- 包含基础外键约束和索引

### 002-standardize-inventory-table.sql

- 标准化库存表结构
- 使用snake_case命名规范

### 003-standardize-users-table.sql

- 标准化用户表结构
- 修改字段命名为snake_case

### 004-merge-material-inventory-models.sql

- 合并Material和Inventory模型
- 创建统一的库存管理表

### 005-add-database-constraints.sql ⭐ **最新**

- 添加全面的数据库约束
- 包含CHECK约束、触发器、数据验证视图等

## 约束类型

### 1. CHECK约束

- **用户表约束**：用户名格式、邮箱格式、手机号格式
- **鹅群表约束**：数量逻辑、生产数据合理性、日期逻辑
- **库存表约束**：库存数量逻辑、价格数据、状态一致性

### 2. 外键约束

- 确保表间关系的完整性
- 级联删除设置

### 3. 触发器

- **鹅群数据一致性触发器**：自动验证和更新数据
- **库存状态自动更新触发器**：根据库存量自动更新状态

### 4. 数据验证

- **数据质量检查视图**：监控数据完整性
- **约束验证存储过程**：批量检查数据质量

## 使用方法

### 1. 执行约束迁移

```bash
# 进入backend目录
cd backend

# 安装依赖（如果还没有安装）
npm install

# 执行约束迁移
node scripts/run-constraints-migration.js

# 或者只检查现有约束（不执行迁移）
node scripts/run-constraints-migration.js --check-only
```

### 2. 测试约束效果

```bash
# 运行约束测试
node scripts/test-constraints.js
```

### 3. 手动执行SQL文件

```bash
# 使用MySQL命令行执行
mysql -u root -p smart_goose < migrations/005-add-database-constraints.sql
```

## 环境变量配置

在执行脚本前，请确保设置了正确的数据库连接信息：

```bash
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=root
export DB_PASSWORD=your_password
export DB_NAME=smart_goose
```

或者在项目根目录创建`.env`文件：

```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=smart_goose
```

## 约束验证

### 自动验证

执行迁移后，系统会自动运行数据完整性检查：

```sql
CALL ValidateDataIntegrity();
```

### 手动检查数据质量

查看数据质量检查视图：

```sql
SELECT * FROM v_data_quality_check WHERE issue_count > 0;
```

## 约束示例

### 用户名格式约束

```sql
-- ✅ 有效用户名
INSERT INTO users (username, password) VALUES ('user123', 'password');
INSERT INTO users (username, password) VALUES ('test_user', 'password');

-- ❌ 无效用户名（会被拒绝）
INSERT INTO users (username, password) VALUES ('ab', 'password');      -- 太短
INSERT INTO users (username, password) VALUES ('user@#', 'password');   -- 特殊字符
```

### 鹅群数量约束

```sql
-- ✅ 有效数据
INSERT INTO flocks (user_id, name, batch_number, breed, total_count, current_count, male_count, female_count, established_date)
VALUES (1, 'test_flock', 'BATCH001', 'breed1', 100, 90, 40, 45, '2024-01-01');

-- ❌ 无效数据（会被拒绝）
INSERT INTO flocks (user_id, name, batch_number, breed, total_count, current_count, established_date)
VALUES (1, 'test_flock2', 'BATCH002', 'breed1', 100, 150, '2024-01-01'); -- current_count > total_count
```

### 库存约束

```sql
-- ✅ 有效数据
INSERT INTO unified_inventory (user_id, name, category, unit, current_stock, min_stock, unit_price)
VALUES (1, 'feed1', 'feed', 'kg', 100, 10, 25.50);

-- ❌ 无效数据（会被拒绝）
INSERT INTO unified_inventory (user_id, name, category, unit, current_stock, unit_price)
VALUES (1, 'feed2', 'feed', 'kg', -10, 25.50); -- 负数库存
```

## 故障排除

### 常见错误

1. **约束重复错误**：`ER_CHECK_CONSTRAINT_DUP_NAME`
   - 解决方案：约束已存在，可以忽略或先删除再添加

2. **表不存在错误**：`ER_NO_SUCH_TABLE`
   - 解决方案：确保已执行之前的迁移文件

3. **外键约束错误**：`ER_NO_REFERENCED_ROW_2`
   - 解决方案：确保引用的数据存在

### 查看约束状态

```sql
-- 查看所有CHECK约束
SELECT TABLE_NAME, CONSTRAINT_NAME, CHECK_CLAUSE
FROM information_schema.CHECK_CONSTRAINTS
WHERE CONSTRAINT_SCHEMA = 'smart_goose';

-- 查看外键约束
SELECT TABLE_NAME, CONSTRAINT_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME
FROM information_schema.KEY_COLUMN_USAGE
WHERE CONSTRAINT_SCHEMA = 'smart_goose'
AND REFERENCED_TABLE_NAME IS NOT NULL;
```

## 回滚约束

如果需要移除约束：

```sql
-- 移除CHECK约束
ALTER TABLE flocks DROP CONSTRAINT chk_flock_counts;

-- 移除触发器
DROP TRIGGER IF EXISTS tr_flocks_before_update;

-- 移除视图
DROP VIEW IF EXISTS v_data_quality_check;
```

## 性能影响

- CHECK约束对INSERT和UPDATE操作有轻微性能影响
- 触发器会在每次数据修改时执行
- 建议在生产环境中监控性能指标

## 维护建议

1. **定期检查数据质量**：

   ```sql
   CALL ValidateDataIntegrity();
   ```

2. **监控约束违反情况**：
   - 查看错误日志
   - 定期运行测试脚本

3. **更新约束**：
   - 根据业务需求调整约束条件
   - 版本化约束变更

## 注意事项

⚠️ **重要提醒**：

- 在生产环境执行前，请先在测试环境验证
- 建议在维护窗口期间执行约束迁移
- 执行前请备份数据库
- 某些约束可能会拒绝现有的不符合条件的数据

## 支持

如果遇到问题，请：

1. 检查错误日志
2. 运行测试脚本验证
3. 查看本文档的故障排除部分
4. 联系开发团队
