-- 多租户支持迁移脚本
-- Multi-Tenant Support Migration Script
-- 为所有相关表添加tenant_id字段以支持SaaS多租户架构

-- =============================================
-- 第一部分: 创建租户相关核心表
-- =============================================

-- 1. 租户表（如果不存在）
CREATE TABLE IF NOT EXISTS tenants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_code VARCHAR(50) NOT NULL UNIQUE COMMENT '租户代码',
    company_name VARCHAR(200) NOT NULL COMMENT '公司名称',
    contact_name VARCHAR(100) NULL COMMENT '联系人姓名',
    contact_phone VARCHAR(20) NULL COMMENT '联系电话',
    contact_email VARCHAR(100) NULL COMMENT '联系邮箱',
    address TEXT NULL COMMENT '地址',
    subscription_plan ENUM('trial', 'basic', 'standard', 'premium', 'enterprise') DEFAULT 'trial' COMMENT '订阅计划',
    scale ENUM('small', 'medium', 'large', 'enterprise') DEFAULT 'small' COMMENT '企业规模',
    status ENUM('active', 'inactive', 'suspended', 'trial', 'pending') DEFAULT 'trial' COMMENT '状态',
    trial_start_date DATE NULL COMMENT '试用开始日期',
    trial_end_date DATE NULL COMMENT '试用结束日期',
    subscription_start_date DATE NULL COMMENT '订阅开始日期',
    subscription_end_date DATE NULL COMMENT '订阅结束日期',
    max_users INT DEFAULT 10 COMMENT '最大用户数',
    max_storage_gb INT DEFAULT 5 COMMENT '最大存储空间(GB)',
    max_api_calls_per_month INT DEFAULT 10000 COMMENT '每月API调用限制',
    last_active_at TIMESTAMP NULL COMMENT '最后活跃时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_tenant_code (tenant_code),
    INDEX idx_status (status),
    INDEX idx_subscription_plan (subscription_plan),
    INDEX idx_last_active_at (last_active_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户表';

-- 2. 租户配置表
CREATE TABLE IF NOT EXISTS tenant_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL COMMENT '租户ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT NULL COMMENT '配置值',
    data_type ENUM('string', 'number', 'boolean', 'json', 'date') DEFAULT 'string' COMMENT '数据类型',
    description TEXT NULL COMMENT '配置描述',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY uk_tenant_config (tenant_id, config_key),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户配置表';

-- =============================================
-- 第二部分: 为现有表添加tenant_id字段
-- =============================================

-- 用户表 - 如果没有tenant_id字段则添加
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS tenant_id INT NULL COMMENT '租户ID' AFTER id,
ADD CONSTRAINT fk_users_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
ADD INDEX IF NOT EXISTS idx_users_tenant_id (tenant_id);

-- 鹅群表
ALTER TABLE flocks 
ADD COLUMN IF NOT EXISTS tenant_id INT NULL COMMENT '租户ID' AFTER id,
ADD CONSTRAINT fk_flocks_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
ADD INDEX IF NOT EXISTS idx_flocks_tenant_id (tenant_id);

-- 健康记录表
ALTER TABLE health_records 
ADD COLUMN IF NOT EXISTS tenant_id INT NULL COMMENT '租户ID' AFTER id,
ADD CONSTRAINT fk_health_records_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
ADD INDEX IF NOT EXISTS idx_health_records_tenant_id (tenant_id);

-- 生产记录表
ALTER TABLE production_records 
ADD COLUMN IF NOT EXISTS tenant_id INT NULL COMMENT '租户ID' AFTER id,
ADD CONSTRAINT fk_production_records_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
ADD INDEX IF NOT EXISTS idx_production_records_tenant_id (tenant_id);

-- 库存表
ALTER TABLE inventory 
ADD COLUMN IF NOT EXISTS tenant_id INT NULL COMMENT '租户ID' AFTER id,
ADD CONSTRAINT fk_inventory_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
ADD INDEX IF NOT EXISTS idx_inventory_tenant_id (tenant_id);

-- AI配置表
ALTER TABLE ai_configs 
ADD COLUMN IF NOT EXISTS tenant_id INT NULL COMMENT '租户ID' AFTER id,
ADD CONSTRAINT fk_ai_configs_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
ADD INDEX IF NOT EXISTS idx_ai_configs_tenant_id (tenant_id);

-- AI使用统计表
ALTER TABLE ai_usage_stats 
ADD COLUMN IF NOT EXISTS tenant_id INT NULL COMMENT '租户ID' AFTER id,
ADD CONSTRAINT fk_ai_usage_stats_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
ADD INDEX IF NOT EXISTS idx_ai_usage_stats_tenant_id (tenant_id);

-- 财务申请表
ALTER TABLE finance_applications 
ADD COLUMN IF NOT EXISTS tenant_id INT NULL COMMENT '租户ID' AFTER id,
ADD CONSTRAINT fk_finance_applications_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
ADD INDEX IF NOT EXISTS idx_finance_applications_tenant_id (tenant_id);

-- 财务记录表
ALTER TABLE finance_records 
ADD COLUMN IF NOT EXISTS tenant_id INT NULL COMMENT '租户ID' AFTER id,
ADD CONSTRAINT fk_finance_records_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
ADD INDEX IF NOT EXISTS idx_finance_records_tenant_id (tenant_id);

-- 产品表
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS tenant_id INT NULL COMMENT '租户ID' AFTER id,
ADD CONSTRAINT fk_products_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
ADD INDEX IF NOT EXISTS idx_products_tenant_id (tenant_id);

-- 订单表
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS tenant_id INT NULL COMMENT '租户ID' AFTER id,
ADD CONSTRAINT fk_orders_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
ADD INDEX IF NOT EXISTS idx_orders_tenant_id (tenant_id);

-- 购物车表
ALTER TABLE shopping_cart 
ADD COLUMN IF NOT EXISTS tenant_id INT NULL COMMENT '租户ID' AFTER id,
ADD CONSTRAINT fk_shopping_cart_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
ADD INDEX IF NOT EXISTS idx_shopping_cart_tenant_id (tenant_id);

-- 任务表
ALTER TABLE tasks 
ADD COLUMN IF NOT EXISTS tenant_id INT NULL COMMENT '租户ID' AFTER id,
ADD CONSTRAINT fk_tasks_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
ADD INDEX IF NOT EXISTS idx_tasks_tenant_id (tenant_id);

-- 通知表
ALTER TABLE notifications 
ADD COLUMN IF NOT EXISTS tenant_id INT NULL COMMENT '租户ID' AFTER id,
ADD CONSTRAINT fk_notifications_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
ADD INDEX IF NOT EXISTS idx_notifications_tenant_id (tenant_id);

-- 审计日志表
ALTER TABLE audit_logs 
ADD COLUMN IF NOT EXISTS tenant_id INT NULL COMMENT '租户ID' AFTER id,
ADD CONSTRAINT fk_audit_logs_tenant_id FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
ADD INDEX IF NOT EXISTS idx_audit_logs_tenant_id (tenant_id);

-- =============================================
-- 第三部分: 创建租户专用的新表
-- =============================================

-- 3. 租户用户角色表（租户内用户角色管理）
CREATE TABLE IF NOT EXISTS tenant_user_roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL COMMENT '租户ID',
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    role_code VARCHAR(30) NOT NULL COMMENT '角色代码',
    description TEXT NULL COMMENT '角色描述',
    permissions JSON NULL COMMENT '权限列表',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认角色',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统角色',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY uk_tenant_role (tenant_id, role_code),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_role_code (role_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户用户角色表';

-- 4. 租户用户角色关联表
CREATE TABLE IF NOT EXISTS tenant_user_role_assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL COMMENT '租户ID',
    user_id INT NOT NULL COMMENT '用户ID',
    role_id INT NOT NULL COMMENT '角色ID',
    assigned_by INT NULL COMMENT '分配人ID',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES tenant_user_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY uk_tenant_user_role (tenant_id, user_id, role_id),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户用户角色关联表';

-- 5. 租户存储使用统计表
CREATE TABLE IF NOT EXISTS tenant_storage_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL COMMENT '租户ID',
    storage_type ENUM('file', 'image', 'document', 'backup', 'other') NOT NULL COMMENT '存储类型',
    used_bytes BIGINT DEFAULT 0 COMMENT '使用字节数',
    file_count INT DEFAULT 0 COMMENT '文件数量',
    stats_date DATE NOT NULL COMMENT '统计日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY uk_tenant_storage_date (tenant_id, storage_type, stats_date),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_storage_type (storage_type),
    INDEX idx_stats_date (stats_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户存储使用统计表';

-- 6. 租户API使用统计表
CREATE TABLE IF NOT EXISTS tenant_api_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT NOT NULL COMMENT '租户ID',
    api_path VARCHAR(200) NOT NULL COMMENT 'API路径',
    method ENUM('GET', 'POST', 'PUT', 'DELETE', 'PATCH') NOT NULL COMMENT 'HTTP方法',
    request_count INT DEFAULT 0 COMMENT '请求次数',
    success_count INT DEFAULT 0 COMMENT '成功次数',
    error_count INT DEFAULT 0 COMMENT '错误次数',
    avg_response_time_ms INT DEFAULT 0 COMMENT '平均响应时间(毫秒)',
    stats_date DATE NOT NULL COMMENT '统计日期',
    stats_hour TINYINT NOT NULL COMMENT '统计小时(0-23)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY uk_tenant_api_hour (tenant_id, api_path, method, stats_date, stats_hour),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_api_path (api_path),
    INDEX idx_stats_date (stats_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户API使用统计表';

-- =============================================
-- 第四部分: 插入默认数据
-- =============================================

-- 插入默认租户角色
INSERT IGNORE INTO tenant_user_roles (tenant_id, role_name, role_code, description, permissions, is_default, is_system) VALUES
(1, '管理员', 'admin', '租户管理员，拥有所有权限', '["*"]', TRUE, TRUE),
(1, '经理', 'manager', '业务经理，拥有大部分业务权限', '["users.read", "users.write", "flocks.read", "flocks.write", "production.read", "production.write", "health.read", "health.write", "inventory.read", "inventory.write", "oa.read", "oa.write", "finance.read"]', FALSE, TRUE),
(1, '员工', 'employee', '普通员工，拥有基本权限', '["flocks.read", "production.read", "production.write", "health.read", "health.write", "inventory.read", "oa.read"]', FALSE, TRUE),
(1, '观察者', 'viewer', '只读权限', '["flocks.read", "production.read", "health.read", "inventory.read"]', FALSE, TRUE);

-- =============================================
-- 第五部分: 数据迁移提示
-- =============================================

-- 注意：运行此脚本后，需要手动更新现有数据的tenant_id字段
-- 可以根据具体业务需求设置租户ID
-- 例如：UPDATE users SET tenant_id = 1 WHERE tenant_id IS NULL;

COMMIT;