-- 创建标准化的flocks表（snake_case命名）
-- Migration: 001-create-flocks-table.sql

CREATE TABLE IF NOT EXISTS flocks (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  name VARCHAR(100) NOT NULL,
  batch_number VARCHAR(50) NOT NULL UNIQUE,
  breed VARCHAR(50) NOT NULL,
  total_count INT NOT NULL DEFAULT 0,
  current_count INT NOT NULL DEFAULT 0,
  male_count INT NOT NULL DEFAULT 0,
  female_count INT NOT NULL DEFAULT 0,
  age_group ENUM('young', 'adult', 'breeding', 'retired') NOT NULL DEFAULT 'young',
  status ENUM('active', 'inactive', 'sold', 'deceased') NOT NULL DEFAULT 'active',
  established_date DATE NOT NULL,
  location VARCHAR(200),
  description TEXT,
  
  -- 健康相关字段
  last_health_check_date DATE,
  health_status ENUM('excellent', 'good', 'fair', 'poor') DEFAULT 'good',
  
  -- 生产相关字段  
  avg_daily_egg_production DECIMAL(8,2) DEFAULT 0,
  peak_production_rate DECIMAL(5,2) DEFAULT 0,
  
  -- 经济相关字段
  purchase_price DECIMAL(10,2),
  current_value DECIMAL(10,2),
  
  -- 环境相关字段
  housing_type ENUM('indoor', 'outdoor', 'free_range', 'mixed') DEFAULT 'indoor',
  feed_type VARCHAR(100),
  vaccination_status ENUM('up_to_date', 'overdue', 'none') DEFAULT 'none',
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- 外键约束
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  
  -- 索引优化
  INDEX idx_user_id (user_id),
  INDEX idx_batch_number (batch_number),
  INDEX idx_status (status),
  INDEX idx_age_group (age_group),
  INDEX idx_health_status (health_status),
  INDEX idx_established_date (established_date),
  
  -- 复合索引
  INDEX idx_user_status (user_id, status),
  INDEX idx_user_age_group (user_id, age_group)
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='鹅群管理表';