-- 统一数据库模型迁移脚本
-- Unified Database Models Migration Script
-- 执行时间：2024年12月
-- 目标：统一模型定义，解决字段命名不一致和模型重复问题

-- ====================================================
-- 第一阶段：创建统一模型表
-- ====================================================

-- 1. 创建统一库存管理表（替代materials、inventory、unified_inventory）
CREATE TABLE IF NOT EXISTS unified_inventory_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    flock_id INT NULL COMMENT '关联鹅群ID',
    name VARCHAR(100) NOT NULL COMMENT '物料名称',
    item_name VARCHAR(100) NULL COMMENT '物品名称（别名）',
    category ENUM('feed', 'medicine', 'equipment', 'materials', 'other') NOT NULL COMMENT '物料类别',
    specification VARCHAR(100) NULL COMMENT '规格型号',
    unit VARCHAR(20) NOT NULL DEFAULT '个' COMMENT '计量单位',
    
    -- 库存数量管理
    current_stock DECIMAL(12,3) DEFAULT 0 COMMENT '当前库存',
    min_stock DECIMAL(12,3) DEFAULT 0 COMMENT '最低库存警戒线',
    max_stock DECIMAL(12,3) NULL COMMENT '最高库存',
    quantity INT DEFAULT 0 COMMENT '数量（整数型，兼容旧数据）',
    
    -- 价格管理
    unit_price DECIMAL(10,2) NULL COMMENT '单价',
    total_value DECIMAL(15,2) NULL COMMENT '总价值',
    
    -- 供应商信息
    supplier VARCHAR(100) NULL COMMENT '供应商',
    supplier_contact VARCHAR(100) NULL COMMENT '供应商联系方式',
    
    -- 日期管理
    purchase_date DATE NULL COMMENT '采购日期',
    expiry_date DATE NULL COMMENT '过期日期',
    last_update_date DATE NULL COMMENT '最后更新日期',
    
    -- 状态和位置
    status ENUM('normal', 'low_stock', 'out_of_stock', 'expired', 'warning', 'inactive') DEFAULT 'normal' COMMENT '库存状态',
    location VARCHAR(100) NULL COMMENT '存放位置',
    
    -- 其他信息
    description TEXT NULL COMMENT '描述信息',
    batch_number VARCHAR(50) NULL COMMENT '批次号',
    
    -- 审计字段
    created_by INT NULL COMMENT '创建者ID',
    updated_by INT NULL COMMENT '更新者ID',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (flock_id) REFERENCES flocks(id) ON DELETE SET NULL,
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_flock_id (flock_id),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_name (name),
    INDEX idx_expiry_date (expiry_date),
    INDEX idx_purchase_date (purchase_date),
    INDEX idx_current_stock (current_stock),
    INDEX idx_user_category (user_id, category),
    INDEX idx_user_status (user_id, status),
    INDEX idx_low_stock_check (user_id, current_stock, min_stock),
    INDEX idx_expiry_check (status, expiry_date),
    INDEX idx_name_category (name, category)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='统一库存管理表';

-- 2. 创建标准化用户表（规范字段命名）
CREATE TABLE IF NOT EXISTS users_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    name VARCHAR(100) NULL COMMENT '真实姓名',
    farm_name VARCHAR(100) NULL COMMENT '养殖场名称',
    phone VARCHAR(20) NULL COMMENT '手机号',
    email VARCHAR(100) UNIQUE NULL COMMENT '邮箱',
    role ENUM('platform_super_admin', 'platform_admin', 'platform_operator', 'platform_support', 'owner', 'admin', 'manager', 'finance', 'hr', 'user') DEFAULT 'user' COMMENT '用户角色',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '用户状态',
    avatar VARCHAR(500) NULL COMMENT '头像URL',
    last_login_at DATETIME NULL COMMENT '最后登录时间',
    permissions JSON NULL COMMENT '自定义权限列表',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_phone (phone)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 3. 创建标准化健康记录表
CREATE TABLE IF NOT EXISTS health_records_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    flock_id INT NULL COMMENT '鹅群ID',
    goose_id VARCHAR(50) NULL COMMENT '鹅只ID',
    symptoms TEXT NULL COMMENT '症状描述',
    diagnosis TEXT NULL COMMENT '诊断结果',
    treatment TEXT NULL COMMENT '治疗方案',
    status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '处理状态',
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low' COMMENT '严重程度',
    veterinarian VARCHAR(100) NULL COMMENT '兽医姓名',
    treatment_cost DECIMAL(10,2) NULL COMMENT '治疗费用',
    follow_up_date DATE NULL COMMENT '复查日期',
    notes TEXT NULL COMMENT '备注信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (flock_id) REFERENCES flocks(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_flock_id (flock_id),
    INDEX idx_goose_id (goose_id),
    INDEX idx_status (status),
    INDEX idx_severity (severity),
    INDEX idx_created_at (created_at)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='健康记录表';

-- 4. 创建标准化生产记录表
CREATE TABLE IF NOT EXISTS production_records_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    flock_id INT NULL COMMENT '鹅群ID',
    record_date DATE NOT NULL COMMENT '记录日期',
    
    -- 产蛋数据
    egg_count INT DEFAULT 0 COMMENT '产蛋数量',
    egg_weight DECIMAL(8,2) NULL COMMENT '鹅蛋总重量(kg)',
    
    -- 饲料消耗
    feed_consumption DECIMAL(10,2) DEFAULT 0 COMMENT '饲料消耗量(kg)',
    feed_type VARCHAR(100) NULL COMMENT '饲料类型',
    
    -- 环境数据
    temperature DECIMAL(5,2) NULL COMMENT '温度(℃)',
    humidity DECIMAL(5,2) NULL COMMENT '湿度(%)',
    
    -- 健康状态
    mortality_count INT DEFAULT 0 COMMENT '死亡数量',
    sick_count INT DEFAULT 0 COMMENT '生病数量',
    
    -- 体重监测
    average_weight DECIMAL(8,2) NULL COMMENT '平均体重(kg)',
    weight_gain DECIMAL(8,2) NULL COMMENT '增重(kg)',
    
    -- 其他信息
    notes TEXT NULL COMMENT '备注信息',
    weather VARCHAR(50) NULL COMMENT '天气情况',
    recorded_by INT NULL COMMENT '记录人ID',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (flock_id) REFERENCES flocks(id) ON DELETE SET NULL,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_flock_id (flock_id),
    INDEX idx_record_date (record_date),
    INDEX idx_user_date (user_id, record_date),
    INDEX idx_created_at (created_at)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生产记录表';

-- ====================================================
-- 第二阶段：数据迁移
-- ====================================================

-- 1. 迁移用户数据（处理字段命名差异）
INSERT INTO users_new (
    id, username, password, name, farm_name, phone, email, role, status, 
    avatar, last_login_at, permissions, created_at, updated_at
)
SELECT 
    id, 
    username, 
    password, 
    name,
    farm_name,
    phone,
    email,
    CASE 
        WHEN role = 'admin' THEN 'admin'
        WHEN role = 'manager' THEN 'manager'
        WHEN role = 'user' THEN 'user'
        ELSE 'user'
    END as role,
    CASE 
        WHEN status IS NULL THEN 'active'
        ELSE status
    END as status,
    avatar,
    last_login_at,
    permissions,
    created_at,
    updated_at
FROM users
WHERE NOT EXISTS (SELECT 1 FROM users_new WHERE users_new.id = users.id);

-- 2. 迁移统一库存数据
-- 2.1 从materials表迁移数据
INSERT INTO unified_inventory_new (
    user_id, name, category, specification, unit, current_stock, min_stock, 
    unit_price, total_value, supplier, supplier_contact, purchase_date, expiry_date,
    status, location, description, created_by, updated_by, created_at, updated_at
)
SELECT 
    user_id,
    name,
    CASE 
        WHEN category IN ('feed', 'medicine', 'equipment', 'materials', 'other') THEN category
        ELSE 'materials'
    END as category,
    specification,
    COALESCE(unit, '个') as unit,
    COALESCE(current_stock, quantity, 0) as current_stock,
    COALESCE(min_stock, 0) as min_stock,
    unit_price,
    total_value,
    supplier,
    supplier_contact,
    purchase_date,
    expiry_date,
    CASE 
        WHEN status = 'active' THEN 'normal'
        WHEN status = 'inactive' THEN 'inactive'
        ELSE 'normal'
    END as status,
    location,
    description,
    created_by,
    updated_by,
    created_at,
    updated_at
FROM materials
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'materials');

-- 2.2 从inventory表迁移数据（如果存在且不重复）
INSERT INTO unified_inventory_new (
    user_id, name, category, specification, unit, current_stock, min_stock,
    unit_price, total_value, supplier, supplier_contact, purchase_date, expiry_date,
    status, location, description, created_by, updated_by, created_at, updated_at
)
SELECT 
    user_id,
    name,
    CASE 
        WHEN category IN ('feed', 'medicine', 'equipment', 'materials', 'other') THEN category
        ELSE 'materials'
    END as category,
    specification,
    COALESCE(unit, '个') as unit,
    COALESCE(current_stock, quantity, 0) as current_stock,
    COALESCE(min_stock, 0) as min_stock,
    unit_price,
    total_value,
    supplier,
    supplier_contact,
    purchase_date,
    expiry_date,
    CASE 
        WHEN status = 'active' THEN 'normal'
        WHEN status = 'inactive' THEN 'inactive'
        ELSE 'normal'
    END as status,
    location,
    description,
    created_by,
    updated_by,
    created_at,
    updated_at
FROM inventory
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'inventory')
AND NOT EXISTS (
    SELECT 1 FROM unified_inventory_new uin 
    WHERE uin.user_id = inventory.user_id 
    AND uin.name = inventory.name 
    AND uin.category = inventory.category
);

-- 2.3 从existing unified_inventory表迁移数据（如果存在）
INSERT INTO unified_inventory_new (
    user_id, flock_id, name, item_name, category, specification, unit, 
    current_stock, min_stock, max_stock, quantity, unit_price, total_value,
    supplier, supplier_contact, purchase_date, expiry_date, last_update_date,
    status, location, description, batch_number, created_by, updated_by, 
    created_at, updated_at
)
SELECT 
    user_id, flock_id, name, item_name, category, specification, unit,
    current_stock, min_stock, max_stock, quantity, unit_price, total_value,
    supplier, supplier_contact, purchase_date, expiry_date, last_update_date,
    status, location, description, batch_number, created_by, updated_by,
    created_at, updated_at
FROM unified_inventory
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'unified_inventory')
AND NOT EXISTS (
    SELECT 1 FROM unified_inventory_new uin 
    WHERE uin.user_id = unified_inventory.user_id 
    AND uin.name = unified_inventory.name 
    AND uin.category = unified_inventory.category
    AND COALESCE(uin.batch_number, '') = COALESCE(unified_inventory.batch_number, '')
);

-- 3. 迁移健康记录数据
INSERT INTO health_records_new (
    id, user_id, flock_id, goose_id, symptoms, diagnosis, treatment, status,
    severity, veterinarian, treatment_cost, follow_up_date, notes, created_at, updated_at
)
SELECT 
    id, user_id, flock_id, goose_id, symptoms, diagnosis, treatment,
    CASE 
        WHEN status IN ('pending', 'processing', 'completed', 'cancelled') THEN status
        ELSE 'pending'
    END as status,
    COALESCE(severity, 'low') as severity,
    veterinarian,
    treatment_cost,
    follow_up_date,
    notes,
    created_at,
    updated_at
FROM health_records
WHERE NOT EXISTS (SELECT 1 FROM health_records_new WHERE health_records_new.id = health_records.id);

-- 4. 迁移生产记录数据
INSERT INTO production_records_new (
    id, user_id, flock_id, record_date, egg_count, egg_weight, feed_consumption, feed_type,
    temperature, humidity, mortality_count, sick_count, average_weight, weight_gain,
    notes, weather, recorded_by, created_at, updated_at
)
SELECT 
    id, user_id, flock_id,
    COALESCE(record_date, recorded_date, DATE(created_at)) as record_date,
    COALESCE(egg_count, 0) as egg_count,
    egg_weight,
    COALESCE(feed_consumption, 0) as feed_consumption,
    feed_type,
    temperature,
    humidity,
    COALESCE(mortality_count, 0) as mortality_count,
    COALESCE(sick_count, 0) as sick_count,
    average_weight,
    weight_gain,
    notes,
    weather,
    recorded_by,
    created_at,
    updated_at
FROM production_records
WHERE NOT EXISTS (SELECT 1 FROM production_records_new WHERE production_records_new.id = production_records.id);

-- ====================================================
-- 第三阶段：原子性表替换
-- ====================================================

-- 备份原表并替换为新表
-- 注意：在生产环境中，建议先备份数据，然后在维护窗口期间执行

-- 1. 备份并替换库存表
DROP TABLE IF EXISTS materials_backup;
DROP TABLE IF EXISTS inventory_backup;
DROP TABLE IF EXISTS unified_inventory_backup;

-- 创建备份表（如果原表存在）
CREATE TABLE IF NOT EXISTS materials_backup AS SELECT * FROM materials WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'materials');
CREATE TABLE IF NOT EXISTS inventory_backup AS SELECT * FROM inventory WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'inventory');
CREATE TABLE IF NOT EXISTS unified_inventory_backup AS SELECT * FROM unified_inventory WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'unified_inventory');

-- 删除原表
DROP TABLE IF EXISTS materials;
DROP TABLE IF EXISTS inventory;
DROP TABLE IF EXISTS unified_inventory;

-- 重命名新表
ALTER TABLE unified_inventory_new RENAME TO unified_inventory;

-- 2. 备份并替换用户表
DROP TABLE IF EXISTS users_backup;
CREATE TABLE users_backup AS SELECT * FROM users;

-- 使用事务确保原子性
START TRANSACTION;

DROP TABLE users;
ALTER TABLE users_new RENAME TO users;

COMMIT;

-- 3. 备份并替换健康记录表
DROP TABLE IF EXISTS health_records_backup;
CREATE TABLE health_records_backup AS SELECT * FROM health_records;

START TRANSACTION;

DROP TABLE health_records;
ALTER TABLE health_records_new RENAME TO health_records;

COMMIT;

-- 4. 备份并替换生产记录表
DROP TABLE IF EXISTS production_records_backup;
CREATE TABLE production_records_backup AS SELECT * FROM production_records;

START TRANSACTION;

DROP TABLE production_records;
ALTER TABLE production_records_new RENAME TO production_records;

COMMIT;

-- ====================================================
-- 第四阶段：数据验证和清理
-- ====================================================

-- 验证数据迁移结果
SELECT 
    '统一库存表' as table_name,
    COUNT(*) as record_count,
    COUNT(DISTINCT user_id) as user_count,
    COUNT(DISTINCT category) as category_count
FROM unified_inventory

UNION ALL

SELECT 
    '用户表' as table_name,
    COUNT(*) as record_count,
    COUNT(DISTINCT role) as role_count,
    COUNT(DISTINCT status) as status_count
FROM users

UNION ALL

SELECT 
    '健康记录表' as table_name,
    COUNT(*) as record_count,
    COUNT(DISTINCT user_id) as user_count,
    COUNT(DISTINCT status) as status_count
FROM health_records

UNION ALL

SELECT 
    '生产记录表' as table_name,
    COUNT(*) as record_count,
    COUNT(DISTINCT user_id) as user_count,
    0 as category_count
FROM production_records;

-- 检查数据完整性
-- 检查外键约束
SELECT 
    'unified_inventory 孤儿记录' as check_type,
    COUNT(*) as count
FROM unified_inventory ui
LEFT JOIN users u ON ui.user_id = u.id
WHERE u.id IS NULL

UNION ALL

SELECT 
    'health_records 孤儿记录' as check_type,
    COUNT(*) as count
FROM health_records hr
LEFT JOIN users u ON hr.user_id = u.id
WHERE u.id IS NULL

UNION ALL

SELECT 
    'production_records 孤儿记录' as check_type,
    COUNT(*) as count
FROM production_records pr
LEFT JOIN users u ON pr.user_id = u.id
WHERE u.id IS NULL;

-- ====================================================
-- 完成提示
-- ====================================================

SELECT 
    '数据库模型统一迁移完成' as status,
    NOW() as completion_time,
    '请验证应用程序功能正常' as next_step;