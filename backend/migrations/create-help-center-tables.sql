-- 帮助中心数据库设计
-- 创建时间: 2024-01-20
-- 描述: 创建帮助中心相关的数据表

-- 1. 帮助分类表
CREATE TABLE IF NOT EXISTS help_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    icon VARCHAR(50) COMMENT '分类图标(emoji或图标名称)',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT true COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by INT COMMENT '创建者ID',
    updated_by INT COMMENT '更新者ID',
    INDEX idx_sort_order (sort_order),
    INDEX idx_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帮助分类表';

-- 2. 帮助文章表
CREATE TABLE IF NOT EXISTS help_articles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL COMMENT '分类ID',
    title VARCHAR(200) NOT NULL COMMENT '文章标题',
    summary TEXT COMMENT '文章摘要',
    content LONGTEXT COMMENT '文章内容(支持富文本/Markdown)',
    content_type ENUM('text', 'markdown', 'html') DEFAULT 'text' COMMENT '内容类型',
    featured_image VARCHAR(500) COMMENT '特色图片URL',
    tags JSON COMMENT '标签数组',
    meta_keywords VARCHAR(500) COMMENT 'SEO关键词',
    meta_description VARCHAR(500) COMMENT 'SEO描述',
    view_count INT DEFAULT 0 COMMENT '阅读次数',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    helpful_count INT DEFAULT 0 COMMENT '有帮助数',
    unhelpful_count INT DEFAULT 0 COMMENT '没帮助数',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_published BOOLEAN DEFAULT false COMMENT '是否发布',
    is_featured BOOLEAN DEFAULT false COMMENT '是否推荐',
    publish_at TIMESTAMP NULL COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by INT COMMENT '创建者ID',
    updated_by INT COMMENT '更新者ID',
    FOREIGN KEY (category_id) REFERENCES help_categories(id) ON DELETE CASCADE,
    INDEX idx_category (category_id),
    INDEX idx_published (is_published),
    INDEX idx_featured (is_featured),
    INDEX idx_view_count (view_count),
    INDEX idx_created_at (created_at),
    INDEX idx_publish_at (publish_at),
    FULLTEXT idx_content_search (title, summary, content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帮助文章表';

-- 3. 常见问题表
CREATE TABLE IF NOT EXISTS help_faqs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT COMMENT '分类ID(可为空)',
    question VARCHAR(500) NOT NULL COMMENT '问题',
    answer LONGTEXT NOT NULL COMMENT '答案',
    tags JSON COMMENT '标签数组',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    helpful_count INT DEFAULT 0 COMMENT '有帮助数',
    unhelpful_count INT DEFAULT 0 COMMENT '没帮助数',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_published BOOLEAN DEFAULT true COMMENT '是否发布',
    is_featured BOOLEAN DEFAULT false COMMENT '是否为热门FAQ',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by INT COMMENT '创建者ID',
    updated_by INT COMMENT '更新者ID',
    FOREIGN KEY (category_id) REFERENCES help_categories(id) ON DELETE SET NULL,
    INDEX idx_category (category_id),
    INDEX idx_published (is_published),
    INDEX idx_featured (is_featured),
    INDEX idx_sort_order (sort_order),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_faq_search (question, answer)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='常见问题表';

-- 4. 教程表
CREATE TABLE IF NOT EXISTS help_tutorials (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT COMMENT '分类ID',
    title VARCHAR(200) NOT NULL COMMENT '教程标题',
    description TEXT COMMENT '教程描述',
    thumbnail VARCHAR(500) COMMENT '缩略图URL',
    video_url VARCHAR(500) COMMENT '视频链接',
    content LONGTEXT COMMENT '教程内容',
    duration VARCHAR(20) COMMENT '时长(如"5分钟")',
    difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner' COMMENT '难度级别',
    steps JSON COMMENT '教程步骤(JSON格式)',
    view_count INT DEFAULT 0 COMMENT '观看次数',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    tags JSON COMMENT '标签数组',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_published BOOLEAN DEFAULT false COMMENT '是否发布',
    is_featured BOOLEAN DEFAULT false COMMENT '是否推荐',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by INT COMMENT '创建者ID',
    updated_by INT COMMENT '更新者ID',
    FOREIGN KEY (category_id) REFERENCES help_categories(id) ON DELETE SET NULL,
    INDEX idx_category (category_id),
    INDEX idx_published (is_published),
    INDEX idx_featured (is_featured),
    INDEX idx_difficulty (difficulty),
    INDEX idx_view_count (view_count),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_tutorial_search (title, description, content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='教程表';

-- 5. 搜索历史表
CREATE TABLE IF NOT EXISTS help_search_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT COMMENT '用户ID(可为空,匿名搜索)',
    keyword VARCHAR(200) NOT NULL COMMENT '搜索关键词',
    result_count INT DEFAULT 0 COMMENT '搜索结果数量',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '搜索时间',
    INDEX idx_keyword (keyword),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索历史表';

-- 6. 用户反馈表
CREATE TABLE IF NOT EXISTS help_feedback (
    id INT PRIMARY KEY AUTO_INCREMENT,
    type ENUM('article', 'faq', 'tutorial', 'general') NOT NULL COMMENT '反馈类型',
    target_id INT COMMENT '目标内容ID',
    user_id INT COMMENT '用户ID',
    rating TINYINT COMMENT '评分(1-5)',
    is_helpful BOOLEAN COMMENT '是否有帮助',
    content TEXT COMMENT '反馈内容',
    contact_info VARCHAR(200) COMMENT '联系方式',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    status ENUM('pending', 'reviewed', 'resolved') DEFAULT 'pending' COMMENT '处理状态',
    admin_reply TEXT COMMENT '管理员回复',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '反馈时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_type_target (type, target_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户反馈表';

-- 7. 帮助中心设置表
CREATE TABLE IF NOT EXISTS help_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL COMMENT '设置键',
    setting_value TEXT COMMENT '设置值',
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '数据类型',
    description TEXT COMMENT '设置描述',
    is_public BOOLEAN DEFAULT false COMMENT '是否公开(前端可访问)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_public (is_public),
    INDEX idx_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帮助中心设置表';

-- 8. 帮助中心统计表
CREATE TABLE IF NOT EXISTS help_statistics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    date DATE NOT NULL COMMENT '统计日期',
    total_views INT DEFAULT 0 COMMENT '总浏览量',
    total_searches INT DEFAULT 0 COMMENT '总搜索次数',
    total_feedback INT DEFAULT 0 COMMENT '总反馈数',
    article_views INT DEFAULT 0 COMMENT '文章浏览量',
    faq_views INT DEFAULT 0 COMMENT 'FAQ浏览量',
    tutorial_views INT DEFAULT 0 COMMENT '教程浏览量',
    helpful_feedback INT DEFAULT 0 COMMENT '有帮助反馈数',
    unhelpful_feedback INT DEFAULT 0 COMMENT '没帮助反馈数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_date (date),
    INDEX idx_date (date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帮助中心统计表';

-- 插入默认分类数据
INSERT INTO help_categories (name, description, icon, sort_order) VALUES
('账户与登录', '用户账户管理、登录问题等', '👤', 1),
('健康管理', '鹅群健康监控、疾病预防等', '🏥', 2),
('生产管理', '生产数据记录、分析统计等', '🏭', 3),
('系统设置', '系统配置、参数设置等', '⚙️', 4),
('故障排除', '常见问题解决、技术支持等', '🔧', 5),
('其他问题', '其他未分类问题', '❓', 6);

-- 插入默认设置数据
INSERT INTO help_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('contact_phone', '************', 'string', '客服电话', true),
('contact_email', '<EMAIL>', 'string', '客服邮箱', true),
('work_hours', '周一至周五 9:00-18:00', 'string', '工作时间', true),
('search_suggestions_limit', '5', 'number', '搜索建议显示数量', false),
('faq_display_limit', '5', 'number', 'FAQ首页显示数量', false),
('tutorial_display_limit', '3', 'number', '教程首页显示数量', false),
('enable_feedback', 'true', 'boolean', '是否启用反馈功能', false),
('enable_search_history', 'true', 'boolean', '是否记录搜索历史', false);

-- 添加索引优化
ALTER TABLE help_articles ADD INDEX idx_tags ((CAST(tags AS CHAR(500) ARRAY)));
ALTER TABLE help_faqs ADD INDEX idx_tags ((CAST(tags AS CHAR(500) ARRAY)));
ALTER TABLE help_tutorials ADD INDEX idx_tags ((CAST(tags AS CHAR(500) ARRAY)));