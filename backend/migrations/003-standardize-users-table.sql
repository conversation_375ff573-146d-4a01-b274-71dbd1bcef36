-- 标准化users表结构（snake_case命名）
-- Migration: 003-standardize-users-table.sql

-- 修改users表，确保所有字段使用snake_case命名
ALTER TABLE users 
  CHANGE COLUMN createdAt created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  CHANGE COLUMN updatedAt updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- 确保表结构完整
ALTER TABLE users 
  MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY,
  MODIFY COLUMN username VARCHAR(50) UNIQUE NOT NULL,
  MODIFY COLUMN password VARCHAR(255) NOT NULL,
  MODIFY COLUMN email VARCHAR(100),
  MODIFY COLUMN phone VARCHAR(20),
  MODIFY COLUMN role ENUM('admin', 'manager', 'user') DEFAULT 'user',
  MODIFY COLUMN farm_name VARCHAR(100),
  MODIFY COLUMN avatar VARCHAR(255),
  MODIFY COLUMN status ENUM('active', 'inactive') DEFAULT 'active',
  MODIF<PERSON> COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- 添加缺失的索引
ALTER TABLE users 
  ADD INDEX IF NOT EXISTS idx_username (username),
  ADD INDEX IF NOT EXISTS idx_email (email),
  ADD INDEX IF NOT EXISTS idx_role (role),
  ADD INDEX IF NOT EXISTS idx_status (status),
  ADD INDEX IF NOT EXISTS idx_created_at (created_at);

-- 添加表注释
ALTER TABLE users COMMENT='用户管理表';