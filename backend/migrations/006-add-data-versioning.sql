-- 添加数据版本控制和审计功能
-- Migration: 006-add-data-versioning.sql

-- ===== 创建数据版本控制表 =====

CREATE TABLE IF NOT EXISTS data_versions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  table_name VARCHAR(64) NOT NULL,
  record_id INT NOT NULL,
  version_number INT NOT NULL DEFAULT 1,
  operation_type ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
  old_data JSON,
  new_data JSON,
  changed_fields JSON,
  user_id INT,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_table_record (table_name, record_id),
  INDEX idx_version (version_number),
  INDEX idx_operation (operation_type),
  INDEX idx_user_id (user_id),
  INDEX idx_created_at (created_at),
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据版本控制表';

-- ===== 创建系统配置表 =====

CREATE TABLE IF NOT EXISTS system_config (
  id INT AUTO_INCREMENT PRIMARY KEY,
  config_key VARCHAR(100) NOT NULL UNIQUE,
  config_value TEXT,
  config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
  description TEXT,
  is_public BOOLEAN DEFAULT FALSE,
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_config_key (config_key),
  INDEX idx_is_public (is_public),
  
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- ===== 插入默认系统配置 =====

INSERT IGNORE INTO system_config (config_key, config_value, config_type, description, is_public) VALUES
('app.name', '智慧养鹅管理系统', 'string', '应用程序名称', TRUE),
('app.version', '2.0.0', 'string', '应用程序版本', TRUE),
('app.api_version', 'v2', 'string', 'API版本', TRUE),
('database.version', '2.0', 'string', '数据库版本', FALSE),
('features.ai_diagnosis', 'true', 'boolean', '启用AI诊断功能', FALSE),
('features.advanced_analytics', 'true', 'boolean', '启用高级分析功能', FALSE),
('features.data_export', 'true', 'boolean', '启用数据导出功能', FALSE),
('security.password_min_length', '6', 'number', '密码最小长度', FALSE),
('security.session_timeout', '3600', 'number', '会话超时时间（秒）', FALSE),
('inventory.low_stock_threshold', '10', 'number', '低库存预警阈值', FALSE),
('inventory.auto_reorder', 'false', 'boolean', '自动补货功能', FALSE),
('flock.max_count_per_flock', '100000', 'number', '单个鹅群最大数量', FALSE),
('notifications.email_enabled', 'true', 'boolean', '启用邮件通知', FALSE),
('notifications.sms_enabled', 'false', 'boolean', '启用短信通知', FALSE);

-- ===== 创建数据清理配置表 =====

CREATE TABLE IF NOT EXISTS data_cleanup_rules (
  id INT AUTO_INCREMENT PRIMARY KEY,
  rule_name VARCHAR(100) NOT NULL,
  table_name VARCHAR(64) NOT NULL,
  condition_sql TEXT NOT NULL,
  retention_days INT NOT NULL DEFAULT 365,
  is_active BOOLEAN DEFAULT TRUE,
  last_executed TIMESTAMP NULL,
  next_execution TIMESTAMP NULL,
  execution_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_table_name (table_name),
  INDEX idx_is_active (is_active),
  INDEX idx_next_execution (next_execution)
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据清理规则表';

-- ===== 插入默认清理规则 =====

INSERT IGNORE INTO data_cleanup_rules (rule_name, table_name, condition_sql, retention_days) VALUES
('清理旧版本数据', 'data_versions', 'created_at < DATE_SUB(NOW(), INTERVAL ? DAY)', 90),
('清理过期会话', 'audit_logs', 'created_at < DATE_SUB(NOW(), INTERVAL ? DAY) AND action = "login"', 30),
('清理临时文件记录', 'migrations', 'executed_at < DATE_SUB(NOW(), INTERVAL ? DAY) AND migration_name LIKE "temp_%"', 7);

-- ===== 创建性能监控表 =====

CREATE TABLE IF NOT EXISTS performance_metrics (
  id INT AUTO_INCREMENT PRIMARY KEY,
  metric_name VARCHAR(100) NOT NULL,
  metric_value DECIMAL(15, 6) NOT NULL,
  metric_unit VARCHAR(20),
  tags JSON,
  recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_metric_name (metric_name),
  INDEX idx_recorded_at (recorded_at),
  INDEX idx_metric_name_time (metric_name, recorded_at)
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='性能指标监控表';

-- ===== 创建API使用统计表 =====

CREATE TABLE IF NOT EXISTS api_usage_stats (
  id INT AUTO_INCREMENT PRIMARY KEY,
  endpoint VARCHAR(255) NOT NULL,
  method VARCHAR(10) NOT NULL,
  user_id INT,
  response_time_ms INT,
  status_code INT,
  request_size INT,
  response_size INT,
  ip_address VARCHAR(45),
  user_agent TEXT,
  requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_endpoint (endpoint),
  INDEX idx_user_id (user_id),
  INDEX idx_status_code (status_code),
  INDEX idx_requested_at (requested_at),
  INDEX idx_endpoint_time (endpoint, requested_at),
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API使用统计表';

-- ===== 创建错误日志表 =====

CREATE TABLE IF NOT EXISTS error_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  error_type VARCHAR(100) NOT NULL,
  error_message TEXT,
  error_stack TEXT,
  request_url VARCHAR(500),
  request_method VARCHAR(10),
  request_data JSON,
  user_id INT,
  ip_address VARCHAR(45),
  user_agent TEXT,
  severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
  is_resolved BOOLEAN DEFAULT FALSE,
  resolved_by INT,
  resolved_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_error_type (error_type),
  INDEX idx_severity (severity),
  INDEX idx_is_resolved (is_resolved),
  INDEX idx_user_id (user_id),
  INDEX idx_created_at (created_at),
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (resolved_by) REFERENCES users(id) ON DELETE SET NULL
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='错误日志表';

-- ===== 更新数据库版本 =====

UPDATE system_config 
SET config_value = '2.1', updated_at = CURRENT_TIMESTAMP 
WHERE config_key = 'database.version';

-- ===== 验证迁移结果 =====

SELECT 
  'Migration 006 Summary' as info,
  (SELECT COUNT(*) FROM information_schema.tables 
   WHERE table_schema = DATABASE() 
   AND table_name IN ('data_versions', 'system_config', 'data_cleanup_rules', 'performance_metrics', 'api_usage_stats', 'error_logs')) as new_tables_count,
  (SELECT COUNT(*) FROM system_config) as config_entries_count,
  (SELECT config_value FROM system_config WHERE config_key = 'database.version') as database_version;