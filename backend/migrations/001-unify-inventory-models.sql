-- 数据库模型统一迁移脚本
-- 将材料和库存表数据迁移到统一库存表

-- 创建统一库存表
CREATE TABLE IF NOT EXISTS unified_inventory (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '创建用户ID',
    name VARCHAR(100) NOT NULL COMMENT '物料名称',
    category ENUM('feed', 'medicine', 'equipment', 'materials', 'other') NOT NULL COMMENT '物料类别',
    specification VARCHAR(100) COMMENT '规格型号',
    unit VARCHAR(20) NOT NULL DEFAULT '个' COMMENT '计量单位',
    current_stock DECIMAL(10,2) DEFAULT 0 COMMENT '当前库存',
    min_stock DECIMAL(10,2) DEFAULT 0 COMMENT '最低库存警戒线',
    max_stock DECIMAL(10,2) COMMENT '最高库存',
    unit_price DECIMAL(10,2) COMMENT '单价',
    supplier VARCHAR(100) COMMENT '供应商',
    supplier_contact VARCHAR(100) COMMENT '供应商联系方式',
    location VARCHAR(100) COMMENT '存放位置',
    expiry_date DATE COMMENT '过期时间',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态',
    description TEXT COMMENT '描述',
    purchase_date DATE COMMENT '采购日期',
    total_value DECIMAL(12,2) COMMENT '总价值',
    last_updated_date DATE COMMENT '最后更新日期',
    created_by INT COMMENT '创建者ID',
    updated_by INT COMMENT '更新者ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_name (name)
) ENGINE=InnoDB COMMENT='统一库存管理表';

-- 迁移materials表数据到unified_inventory (如果存在)
INSERT INTO unified_inventory (
    user_id, name, category, specification, unit, current_stock, 
    min_stock, max_stock, unit_price, supplier, supplier_contact, 
    location, expiry_date, status, description, purchase_date, 
    created_by, updated_by, created_at, updated_at
)
SELECT 
    user_id, name, 
    CASE 
        WHEN category = 'feed' THEN 'feed'
        WHEN category = 'medicine' THEN 'medicine'
        ELSE 'other'
    END as category,
    spec as specification, unit, stock as current_stock,
    min_stock, max_stock, unit_price, supplier, supplier_contact,
    location, expiry_date, 
    CASE 
        WHEN status = 'active' THEN 'active'
        WHEN status = 'inactive' THEN 'inactive'
        ELSE 'active'
    END as status,
    description, purchase_date, user_id as created_by, user_id as updated_by,
    created_at, updated_at
FROM materials 
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'materials');

-- 迁移inventory表数据到unified_inventory (如果存在且不重复)
INSERT INTO unified_inventory (
    user_id, name, category, specification, unit, current_stock, 
    min_stock, max_stock, unit_price, supplier, supplier_contact, 
    location, expiry_date, status, description, 
    created_by, updated_by, created_at, updated_at
)
SELECT 
    created_by as user_id, name,
    CASE 
        WHEN category LIKE '%feed%' THEN 'feed'
        WHEN category LIKE '%medicine%' THEN 'medicine'
        WHEN category LIKE '%equipment%' THEN 'equipment'
        ELSE 'materials'
    END as category,
    specification, unit, current_stock,
    min_stock, max_stock, unit_price, supplier, supplier_contact,
    location, expiry_date, status, description,
    created_by, updated_by, created_at, updated_at
FROM inventory 
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'inventory')
AND NOT EXISTS (
    SELECT 1 FROM unified_inventory ui 
    WHERE ui.name = inventory.name 
    AND ui.user_id = inventory.created_by
);

-- 备份旧表（重命名而不是删除，以防需要回滚）
RENAME TABLE materials TO materials_backup_old;
RENAME TABLE inventory TO inventory_backup_old;

-- 为统一表创建别名以兼容旧代码
CREATE VIEW materials AS 
SELECT 
    id, user_id, name, category, specification as spec, unit,
    current_stock as stock, min_stock, max_stock, unit_price,
    supplier, supplier_contact, location, expiry_date, status,
    description, purchase_date as purchase_date, created_at, updated_at
FROM unified_inventory;

CREATE VIEW inventory AS 
SELECT 
    id, name, category, specification, unit, current_stock,
    min_stock, max_stock, unit_price, supplier, supplier_contact,
    location, expiry_date, status, description,
    user_id as created_by, updated_by, created_at, updated_at
FROM unified_inventory;