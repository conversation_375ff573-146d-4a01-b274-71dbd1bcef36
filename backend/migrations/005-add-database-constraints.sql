-- 添加数据库级别约束
-- Migration: 005-add-database-constraints.sql

-- ===== 统一库存表约束 =====

-- 添加检查约束
ALTER TABLE unified_inventory 
  ADD CONSTRAINT chk_current_stock_positive 
  CHECK (current_stock >= 0),
  
  ADD CONSTRAINT chk_min_stock_positive 
  CHECK (min_stock IS NULL OR min_stock >= 0),
  
  ADD CONSTRAINT chk_max_stock_positive 
  CHECK (max_stock IS NULL OR max_stock >= 0),
  
  ADD CONSTRAINT chk_unit_price_positive 
  CHECK (unit_price IS NULL OR unit_price >= 0),
  
  ADD CONSTRAINT chk_total_value_positive 
  CHECK (total_value IS NULL OR total_value >= 0),
  
  ADD CONSTRAINT chk_min_max_stock_relationship 
  CHECK (min_stock IS NULL OR max_stock IS NULL OR min_stock <= max_stock),
  
  ADD CONSTRAINT chk_name_not_empty 
  CHECK (LENGTH(TRIM(name)) > 0),
  
  ADD CONSTRAINT chk_unit_not_empty 
  CHECK (LENGTH(TRIM(unit)) > 0);

-- 添加更多严格的约束
ALTER TABLE unified_inventory 
  ADD CONSTRAINT chk_current_stock_limit 
  CHECK (current_stock <= 999999999),
  
  ADD CONSTRAINT chk_unit_price_limit 
  CHECK (unit_price IS NULL OR unit_price <= 99999999.99),
  
  ADD CONSTRAINT chk_purchase_date_reasonable 
  CHECK (purchase_date IS NULL OR purchase_date >= '2020-01-01'),
  
  ADD CONSTRAINT chk_expiry_after_purchase 
  CHECK (expiry_date IS NULL OR purchase_date IS NULL OR expiry_date > purchase_date);

-- ===== 鹅群表约束 =====

-- 添加检查约束
ALTER TABLE flocks 
  ADD CONSTRAINT chk_total_count_positive 
  CHECK (total_count >= 0),
  
  ADD CONSTRAINT chk_current_count_positive 
  CHECK (current_count >= 0),
  
  ADD CONSTRAINT chk_male_count_positive 
  CHECK (male_count >= 0),
  
  ADD CONSTRAINT chk_female_count_positive 
  CHECK (female_count >= 0),
  
  ADD CONSTRAINT chk_total_count_limit 
  CHECK (total_count <= 100000),
  
  ADD CONSTRAINT chk_current_count_within_total 
  CHECK (current_count <= total_count),
  
  ADD CONSTRAINT chk_gender_count_within_current 
  CHECK (male_count + female_count <= current_count),
  
  ADD CONSTRAINT chk_flock_name_not_empty 
  CHECK (LENGTH(TRIM(name)) > 0),
  
  ADD CONSTRAINT chk_breed_not_empty 
  CHECK (LENGTH(TRIM(breed)) > 0),
  
  ADD CONSTRAINT chk_batch_number_format 
  CHECK (batch_number REGEXP '^FLOCK-[0-9]{13}$'),
  
  ADD CONSTRAINT chk_established_date_reasonable 
  CHECK (established_date >= '2020-01-01');

-- ===== 用户表约束 =====

-- 添加检查约束
ALTER TABLE users 
  ADD CONSTRAINT chk_username_not_empty 
  CHECK (LENGTH(TRIM(username)) >= 3),
  
  ADD CONSTRAINT chk_username_format 
  CHECK (username REGEXP '^[a-zA-Z0-9_\u4e00-\u9fa5]+$'),
  
  ADD CONSTRAINT chk_username_not_pure_number 
  CHECK (NOT (username REGEXP '^[0-9]+$')),
  
  ADD CONSTRAINT chk_password_not_empty 
  CHECK (LENGTH(password) >= 6),
  
  ADD CONSTRAINT chk_email_format 
  CHECK (email REGEXP '^[^@]+@[^@]+\.[^@]+$'),
  
  ADD CONSTRAINT chk_email_not_empty 
  CHECK (LENGTH(TRIM(email)) >= 5);

-- ===== 创建触发器确保数据一致性 =====

-- 统一库存自动更新总价值触发器
DELIMITER $$

CREATE TRIGGER tr_unified_inventory_calculate_total_value
  BEFORE UPDATE ON unified_inventory
  FOR EACH ROW
BEGIN
  IF NEW.unit_price IS NOT NULL AND NEW.current_stock IS NOT NULL THEN
    SET NEW.total_value = NEW.unit_price * NEW.current_stock;
  END IF;
  
  -- 自动更新状态
  IF NEW.current_stock <= 0 THEN
    SET NEW.status = 'out_of_stock';
  ELSEIF NEW.min_stock IS NOT NULL AND NEW.current_stock <= NEW.min_stock THEN
    SET NEW.status = 'low_stock';
  ELSEIF NEW.expiry_date IS NOT NULL AND NEW.expiry_date < CURRENT_DATE THEN
    SET NEW.status = 'expired';
  ELSEIF NEW.status IN ('out_of_stock', 'low_stock') AND NEW.current_stock > IFNULL(NEW.min_stock, 0) THEN
    SET NEW.status = 'normal';
  END IF;
END$$

-- 鹅群数量一致性检查触发器
CREATE TRIGGER tr_flocks_count_consistency
  BEFORE UPDATE ON flocks
  FOR EACH ROW
BEGIN
  -- 确保当前数量不超过总数量
  IF NEW.current_count > NEW.total_count THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '当前数量不能大于总数量';
  END IF;
  
  -- 确保性别数量合理
  IF NEW.male_count + NEW.female_count > NEW.current_count THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '雄性数量加雌性数量不能大于当前总数';
  END IF;
END$$

DELIMITER ;

-- ===== 创建索引优化性能 =====

-- 统一库存表索引
CREATE INDEX idx_unified_inventory_stock_status ON unified_inventory(current_stock, status);
CREATE INDEX idx_unified_inventory_expiry_check ON unified_inventory(expiry_date, status);
CREATE INDEX idx_unified_inventory_low_stock_alert ON unified_inventory(user_id, current_stock, min_stock);

-- 鹅群表索引
CREATE INDEX idx_flocks_count_status ON flocks(current_count, status);
CREATE INDEX idx_flocks_user_status ON flocks(user_id, status);
CREATE INDEX idx_flocks_age_group_status ON flocks(age_group, status);

-- 用户表索引
CREATE INDEX idx_users_role_status ON users(role, created_at);

-- ===== 约束验证报告 =====
SELECT 
  'Database Constraints Added Successfully' as status,
  (SELECT COUNT(*) FROM information_schema.table_constraints 
   WHERE table_schema = DATABASE() AND constraint_type = 'CHECK') as check_constraints_count,
  (SELECT COUNT(*) FROM information_schema.triggers 
   WHERE trigger_schema = DATABASE()) as triggers_count,
  (SELECT COUNT(*) FROM information_schema.statistics 
   WHERE table_schema = DATABASE()) as indexes_count;