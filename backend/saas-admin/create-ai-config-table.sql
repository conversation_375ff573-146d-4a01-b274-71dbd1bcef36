-- 创建AI配置表
CREATE TABLE IF NOT EXISTS `ai_configs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` enum('openai','baidu','alibaba','tencent','custom') DEFAULT 'openai' COMMENT '配置类型',
  `api_key` varchar(500) DEFAULT NULL COMMENT 'API密钥',
  `api_secret` varchar(500) DEFAULT NULL COMMENT 'API密钥',
  `endpoint_url` varchar(500) DEFAULT NULL COMMENT 'API端点',
  `model_name` varchar(100) DEFAULT NULL COMMENT '模型名称',
  `max_tokens` int DEFAULT 4000 COMMENT '最大令牌数',
  `temperature` decimal(3,2) DEFAULT 0.70 COMMENT '温度参数',
  `top_p` decimal(3,2) DEFAULT 1.00 COMMENT 'Top-P参数',
  `frequency_penalty` decimal(3,2) DEFAULT 0.00 COMMENT '频率惩罚',
  `presence_penalty` decimal(3,2) DEFAULT 0.00 COMMENT '存在惩罚',
  `system_prompt` text COMMENT '系统提示词',
  `priority` int DEFAULT 0 COMMENT '优先级',
  `status` enum('active','inactive') DEFAULT 'active' COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_config_key` (`config_key`),
  KEY `idx_config_type` (`config_type`),
  KEY `idx_priority` (`priority`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI配置表';

-- 插入默认AI配置数据
INSERT IGNORE INTO `ai_configs` (`config_name`, `config_key`, `config_type`, `model_name`, `system_prompt`, `priority`) VALUES
('OpenAI GPT-4', 'openai_gpt4', 'openai', 'gpt-4', '你是一个专业的智能养鹅助手，具备丰富的养殖经验和专业知识。', 10),
('OpenAI GPT-3.5', 'openai_gpt35', 'openai', 'gpt-3.5-turbo', '你是一个智能养鹅助手，能够提供基础的养殖建议和答疑。', 8),
('百度文心一言', 'baidu_ernie', 'baidu', 'ernie-bot', '你是一个中文AI助手，专门帮助用户解决养鹅相关问题。', 6),
('阿里通义千问', 'ali_qwen', 'alibaba', 'qwen-turbo', '你是通义千问，一个由阿里云开发的AI助手，专注于养殖行业服务。', 5),
('腾讯混元', 'tencent_hunyuan', 'tencent', 'hunyuan-lite', '你是腾讯混元助手，专业提供养鹅技术咨询和管理建议。', 4);

COMMIT;