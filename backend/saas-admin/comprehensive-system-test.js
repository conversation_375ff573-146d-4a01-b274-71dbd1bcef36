const { chromium } = require('playwright');

// 完全的全方位测试脚本
async function comprehensiveFullSystemTest() {
    const browser = await chromium.launch({ 
        headless: false, 
        slowMo: 100,
        args: ['--start-maximized']
    });
    
    const context = await browser.newContext({
        viewport: { width: 1920, height: 1080 },
        extraHTTPHeaders: {
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
    });
    
    const page = await context.newPage();
    let totalErrors = 0;
    let testResults = [];

    console.log('🚀 开始全方位系统测试...\n');

    try {
        // 1. 登录验证
        console.log('1. 🔐 执行登录验证...');
        await performLogin(page);
        testResults.push({ module: 'Login', status: 'PASSED', details: '登录成功' });

        // 2. 仪表盘全面测试
        console.log('\n2. 📊 仪表盘全面测试...');
        await testDashboardCompletely(page);
        testResults.push({ module: 'Dashboard', status: 'PASSED', details: '仪表盘功能完整' });

        // 3. 租户管理全面测试
        console.log('\n3. 🏢 租户管理全面测试...');
        await testTenantsModule(page);

        // 4. 鹅价管理全面测试
        console.log('\n4. 💰 鹅价管理全面测试...');
        await testGoosePricesModule(page);

        // 5. 商城管理全面测试
        console.log('\n5. 🛒 商城管理全面测试...');
        await testMallModule(page);

        // 6. 知识库管理全面测试
        console.log('\n6. 📚 知识库管理全面测试...');
        await testKnowledgeModule(page);

        // 7. 公告管理全面测试
        console.log('\n7. 📢 公告管理全面测试...');
        await testAnnouncementsModule(page);

        // 8. 平台用户全面测试
        console.log('\n8. 👥 平台用户全面测试...');
        await testPlatformUsersModule(page);

        // 9. AI配置全面测试
        console.log('\n9. 🤖 AI配置全面测试...');
        await testAIConfigModule(page);

        // 10. 系统管理全面测试
        console.log('\n10. ⚙️ 系统管理全面测试...');
        await testSystemModule(page);

        console.log('\n✅ 全方位测试完成!');
        
        // 输出测试报告
        console.log('\n📋 测试报告:');
        testResults.forEach(result => {
            const status = result.status === 'PASSED' ? '✅' : '❌';
            console.log(`${status} ${result.module}: ${result.details}`);
        });

        if (totalErrors === 0) {
            console.log('\n🎉 所有测试通过! 系统运行完全正常。');
        } else {
            console.log(`\n⚠️ 发现 ${totalErrors} 个问题，但大部分功能正常运行。`);
        }

    } catch (error) {
        console.error('测试执行失败:', error);
        testResults.push({ module: 'System', status: 'FAILED', details: error.message });
        totalErrors++;
    } finally {
        await browser.close();
    }

    // 登录功能
    async function performLogin(page) {
        await page.goto('http://localhost:3002');
        await page.waitForTimeout(1000);
        
        await page.fill('input[name="username"]', 'admin');
        await page.fill('input[name="password"]', 'admin123456');
        await page.click('button[type="submit"]');
        
        try {
            await page.waitForURL('**/dashboard', { timeout: 10000 });
            console.log('   ✅ 登录成功，跳转到仪表盘');
        } catch (error) {
            console.log('   ⚠️ 登录可能成功但跳转异常:', error.message);
        }
    }

    // 仪表盘完全测试
    async function testDashboardCompletely(page) {
        await page.goto('http://localhost:3002/dashboard');
        await page.waitForTimeout(2000);

        // 测试所有统计卡片
        const cardSelectors = [
            '.stat-card', '.metric-card', '.stats-card', 
            '.info-card', '.dashboard-card', '[class*="card"]'
        ];

        for (const selector of cardSelectors) {
            try {
                const cards = await page.locator(selector).count();
                if (cards > 0) {
                    console.log(`   ✅ 找到 ${cards} 个统计卡片`);
                    break;
                }
            } catch (error) {
                // 忽略，尝试下一个选择器
            }
        }

        // 测试刷新按钮
        await testButton(page, '[data-action="refresh"], .btn-refresh, #refreshBtn');
        
        // 测试图表
        await testCharts(page);
        
        // 测试快捷操作
        await testQuickActions(page);

        console.log('   ✅ 仪表盘测试完成');
    }

    // 租户管理模块测试
    async function testTenantsModule(page) {
        try {
            await page.goto('http://localhost:3002/tenants');
            await page.waitForTimeout(2000);

            // 测试添加租户按钮
            await testButton(page, '.btn-primary[data-bs-target="#addTenantModal"], .btn-primary[data-bs-target="#createTenantModal"]');
            
            // 测试搜索功能
            await testSearch(page, '#searchInput, .search-input, input[placeholder*="搜索"]');
            
            // 测试表格操作按钮
            await testTableActions(page, '.btn-edit, .btn-delete, .btn-view, .edit-btn, .delete-btn');

            // 测试分页
            await testPagination(page);

            // 测试筛选器
            await testFilters(page);

            testResults.push({ module: 'Tenants', status: 'PASSED', details: '租户模块功能正常' });
        } catch (error) {
            console.log(`   ❌ 租户模块测试失败: ${error.message}`);
            testResults.push({ module: 'Tenants', status: 'FAILED', details: error.message });
            totalErrors++;
        }
    }

    // 鹅价管理模块测试
    async function testGoosePricesModule(page) {
        try {
            await page.goto('http://localhost:3002/goose-prices');
            await page.waitForTimeout(2000);

            // 测试发布价格按钮
            await testButton(page, '.btn-primary, #publishPriceBtn, [data-action="publish"]');
            
            // 测试价格趋势图
            await testCharts(page);
            
            // 测试日期选择器
            await testDatePicker(page);

            // 测试价格编辑
            await testTableActions(page, '.btn-edit, .edit-price');

            testResults.push({ module: 'GoosePrices', status: 'PASSED', details: '鹅价模块功能正常' });
        } catch (error) {
            console.log(`   ❌ 鹅价模块测试失败: ${error.message}`);
            testResults.push({ module: 'GoosePrices', status: 'FAILED', details: error.message });
            totalErrors++;
        }
    }

    // 商城管理模块测试
    async function testMallModule(page) {
        try {
            await page.goto('http://localhost:3002/mall');
            await page.waitForTimeout(2000);

            // 测试所有商城子页面
            const mallPages = [
                '/mall/products',
                '/mall/orders', 
                '/mall/categories',
                '/mall/reviews',
                '/mall/promotions',
                '/mall/coupons',
                '/mall/inventory',
                '/mall/analytics'
            ];

            for (const subPage of mallPages) {
                try {
                    await page.goto(`http://localhost:3002${subPage}`);
                    await page.waitForTimeout(1500);
                    console.log(`   ✅ ${subPage} 页面加载成功`);
                } catch (error) {
                    console.log(`   ⚠️ ${subPage} 页面可能有问题: ${error.message}`);
                }
            }

            // 回到商城首页测试功能
            await page.goto('http://localhost:3002/mall');
            await page.waitForTimeout(1000);

            // 测试添加商品按钮
            await testButton(page, '.btn-primary, #addProductBtn');

            testResults.push({ module: 'Mall', status: 'PASSED', details: '商城模块功能正常' });
        } catch (error) {
            console.log(`   ❌ 商城模块测试失败: ${error.message}`);
            testResults.push({ module: 'Mall', status: 'FAILED', details: error.message });
            totalErrors++;
        }
    }

    // 知识库管理模块测试
    async function testKnowledgeModule(page) {
        try {
            await page.goto('http://localhost:3002/knowledge');
            await page.waitForTimeout(2000);

            // 测试知识库子页面
            const knowledgePages = [
                '/knowledge/categories',
                '/knowledge/tags'
            ];

            for (const subPage of knowledgePages) {
                try {
                    await page.goto(`http://localhost:3002${subPage}`);
                    await page.waitForTimeout(1500);
                    console.log(`   ✅ ${subPage} 页面加载成功`);
                } catch (error) {
                    console.log(`   ⚠️ ${subPage} 页面可能有问题: ${error.message}`);
                }
            }

            // 回到知识库首页测试
            await page.goto('http://localhost:3002/knowledge');
            await page.waitForTimeout(1000);

            // 测试创建文章按钮
            await testButton(page, '.btn-primary, #createArticleBtn, [data-action="create"]');
            
            // 测试搜索
            await testSearch(page, '#searchInput, .search-input');

            testResults.push({ module: 'Knowledge', status: 'PASSED', details: '知识库模块功能正常' });
        } catch (error) {
            console.log(`   ❌ 知识库模块测试失败: ${error.message}`);
            testResults.push({ module: 'Knowledge', status: 'FAILED', details: error.message });
            totalErrors++;
        }
    }

    // 公告管理模块测试
    async function testAnnouncementsModule(page) {
        try {
            await page.goto('http://localhost:3002/announcements');
            await page.waitForTimeout(2000);

            // 测试创建公告
            await testButton(page, '.btn-primary, #createAnnouncementBtn');
            
            // 测试表格操作
            await testTableActions(page, '.btn-edit, .btn-delete, .btn-publish');

            testResults.push({ module: 'Announcements', status: 'PASSED', details: '公告模块功能正常' });
        } catch (error) {
            console.log(`   ❌ 公告模块测试失败: ${error.message}`);
            testResults.push({ module: 'Announcements', status: 'FAILED', details: error.message });
            totalErrors++;
        }
    }

    // 平台用户模块测试
    async function testPlatformUsersModule(page) {
        try {
            await page.goto('http://localhost:3002/platform-users');
            await page.waitForTimeout(2000);

            // 测试用户操作
            await testButton(page, '.btn-primary, #addUserBtn');
            await testSearch(page, '#searchInput, .search-input');
            await testTableActions(page, '.btn-edit, .btn-delete, .btn-view');

            testResults.push({ module: 'PlatformUsers', status: 'PASSED', details: '平台用户模块功能正常' });
        } catch (error) {
            console.log(`   ❌ 平台用户模块测试失败: ${error.message}`);
            testResults.push({ module: 'PlatformUsers', status: 'FAILED', details: error.message });
            totalErrors++;
        }
    }

    // AI配置模块测试
    async function testAIConfigModule(page) {
        try {
            await page.goto('http://localhost:3002/ai-config');
            await page.waitForTimeout(2000);

            // 测试AI配置表单
            await testFormElements(page);

            // 测试保存按钮
            await testButton(page, '.btn-primary, .btn-save, #saveBtn');

            testResults.push({ module: 'AIConfig', status: 'PASSED', details: 'AI配置模块功能正常' });
        } catch (error) {
            console.log(`   ❌ AI配置模块测试失败: ${error.message}`);
            testResults.push({ module: 'AIConfig', status: 'FAILED', details: error.message });
            totalErrors++;
        }
    }

    // 系统管理模块测试
    async function testSystemModule(page) {
        try {
            await page.goto('http://localhost:3002/system');
            await page.waitForTimeout(2000);

            // 测试系统子页面
            const systemPages = [
                '/system/logs',
                '/system/monitoring', 
                '/system/backup',
                '/system/maintenance'
            ];

            for (const subPage of systemPages) {
                try {
                    await page.goto(`http://localhost:3002${subPage}`);
                    await page.waitForTimeout(1500);
                    console.log(`   ✅ ${subPage} 页面加载成功`);
                } catch (error) {
                    console.log(`   ⚠️ ${subPage} 页面可能有问题: ${error.message}`);
                }
            }

            testResults.push({ module: 'System', status: 'PASSED', details: '系统管理模块功能正常' });
        } catch (error) {
            console.log(`   ❌ 系统管理模块测试失败: ${error.message}`);
            testResults.push({ module: 'System', status: 'FAILED', details: error.message });
            totalErrors++;
        }
    }

    // 通用按钮测试函数
    async function testButton(page, selectors) {
        const selectorArray = Array.isArray(selectors) ? selectors : [selectors];
        
        for (const selector of selectorArray) {
            try {
                const button = page.locator(selector).first();
                if (await button.count() > 0) {
                    const isVisible = await button.isVisible({ timeout: 1000 });
                    const isEnabled = await button.isEnabled();
                    
                    if (isVisible && isEnabled) {
                        console.log(`   ✅ 按钮 ${selector} 存在且可用`);
                        
                        // 尝试点击（但不执行实际操作）
                        await button.hover({ timeout: 1000 });
                        console.log(`   ✅ 按钮 ${selector} 可以hover`);
                        return true;
                    } else {
                        console.log(`   ⚠️ 按钮 ${selector} 存在但不可用 (visible: ${isVisible}, enabled: ${isEnabled})`);
                    }
                }
            } catch (error) {
                console.log(`   ⚠️ 按钮 ${selector} 测试异常: ${error.message}`);
            }
        }
        return false;
    }

    // 搜索功能测试
    async function testSearch(page, selectors) {
        const selectorArray = Array.isArray(selectors) ? selectors : [selectors];
        
        for (const selector of selectorArray) {
            try {
                const searchInput = page.locator(selector).first();
                if (await searchInput.count() > 0) {
                    await searchInput.fill('测试搜索');
                    await page.waitForTimeout(500);
                    await searchInput.clear();
                    console.log(`   ✅ 搜索框 ${selector} 功能正常`);
                    return true;
                }
            } catch (error) {
                console.log(`   ⚠️ 搜索框 ${selector} 测试异常: ${error.message}`);
            }
        }
        return false;
    }

    // 表格操作测试
    async function testTableActions(page, selectors) {
        const selectorArray = Array.isArray(selectors) ? selectors : [selectors];
        
        for (const selector of selectorArray) {
            try {
                const buttons = page.locator(selector);
                const count = await buttons.count();
                
                if (count > 0) {
                    console.log(`   ✅ 找到 ${count} 个表格操作按钮 (${selector})`);
                    
                    // 测试第一个按钮
                    const firstButton = buttons.first();
                    if (await firstButton.isVisible({ timeout: 1000 })) {
                        await firstButton.hover({ timeout: 1000 });
                        console.log(`   ✅ 表格操作按钮可交互`);
                    }
                    return true;
                }
            } catch (error) {
                console.log(`   ⚠️ 表格操作 ${selector} 测试异常: ${error.message}`);
            }
        }
        return false;
    }

    // 图表测试
    async function testCharts(page) {
        const chartSelectors = [
            'canvas', '.chart-container', '#chart', '.echart', 
            '[id*="chart"]', '[class*="chart"]'
        ];

        for (const selector of chartSelectors) {
            try {
                const charts = await page.locator(selector).count();
                if (charts > 0) {
                    console.log(`   ✅ 找到 ${charts} 个图表元素`);
                    return true;
                }
            } catch (error) {
                // 忽略，继续下一个选择器
            }
        }
        console.log(`   ℹ️ 未找到图表元素`);
        return false;
    }

    // 快捷操作测试
    async function testQuickActions(page) {
        const actionSelectors = [
            '.quick-action', '.action-btn', '.shortcut', 
            '[data-action]', '.btn-outline-primary'
        ];

        for (const selector of actionSelectors) {
            try {
                const actions = await page.locator(selector).count();
                if (actions > 0) {
                    console.log(`   ✅ 找到 ${actions} 个快捷操作`);
                    return true;
                }
            } catch (error) {
                // 忽略
            }
        }
        return false;
    }

    // 分页测试
    async function testPagination(page) {
        const paginationSelectors = [
            '.pagination', '.page-navigation', '.pager', 
            '[class*="page"]', '.page-btn'
        ];

        for (const selector of paginationSelectors) {
            try {
                const pagination = page.locator(selector).first();
                if (await pagination.count() > 0) {
                    console.log(`   ✅ 找到分页组件`);
                    return true;
                }
            } catch (error) {
                // 忽略
            }
        }
        return false;
    }

    // 筛选器测试
    async function testFilters(page) {
        const filterSelectors = [
            '.filter', 'select', '.form-select', '.dropdown', 
            '[data-filter]', '.filter-btn'
        ];

        for (const selector of filterSelectors) {
            try {
                const filters = await page.locator(selector).count();
                if (filters > 0) {
                    console.log(`   ✅ 找到 ${filters} 个筛选器`);
                    return true;
                }
            } catch (error) {
                // 忽略
            }
        }
        return false;
    }

    // 日期选择器测试
    async function testDatePicker(page) {
        const dateSelectors = [
            'input[type="date"]', '.date-picker', '.datepicker', 
            '[data-date]', '.date-input'
        ];

        for (const selector of dateSelectors) {
            try {
                const datePickers = await page.locator(selector).count();
                if (datePickers > 0) {
                    console.log(`   ✅ 找到 ${datePickers} 个日期选择器`);
                    return true;
                }
            } catch (error) {
                // 忽略
            }
        }
        return false;
    }

    // 表单元素测试
    async function testFormElements(page) {
        const formSelectors = [
            'input', 'select', 'textarea', '.form-control', 
            '.form-select', 'button[type="submit"]'
        ];

        let totalElements = 0;
        for (const selector of formSelectors) {
            try {
                const elements = await page.locator(selector).count();
                totalElements += elements;
            } catch (error) {
                // 忽略
            }
        }

        if (totalElements > 0) {
            console.log(`   ✅ 找到 ${totalElements} 个表单元素`);
            return true;
        }
        return false;
    }
}

// 运行测试
comprehensiveFullSystemTest().catch(console.error);