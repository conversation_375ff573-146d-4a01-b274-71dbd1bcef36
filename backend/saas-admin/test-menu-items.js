const { chromium } = require('playwright');

async function testSpecificMenuItems() {
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    console.log('开始详细测试各个菜单项...');
    
    try {
        // 登录
        console.log('1. 登录系统...');
        await page.goto('http://localhost:3002');
        await page.waitForTimeout(1000);
        
        await page.fill('input[name="username"]', 'admin');
        await page.fill('input[name="password"]', 'admin123456');
        await page.click('button[type="submit"]');
        await page.waitForURL('**/dashboard');
        
        console.log('✅ 登录成功，开始测试各个菜单...');
        
        // 定义要测试的菜单项
        const menuItems = [
            { name: '平台仪表盘', selector: 'a[href="/dashboard"]', expected: '/dashboard' },
            { name: '租户列表', selector: 'a[href="/tenants"]', expected: '/tenants' },
            { name: '订阅管理', selector: 'a[href="/tenants/subscriptions"]', expected: '/tenants/subscriptions' },
            { name: '使用统计', selector: 'a[href="/tenants/usage"]', expected: '/tenants/usage' },
            { name: '今日鹅价', selector: 'a[href="/goose-prices"]', expected: '/goose-prices' },
            { name: '价格管理', selector: 'a[href="/goose-prices/manage"]', expected: '/goose-prices/manage' },
            { name: '地区配置', selector: 'a[href="/goose-prices/regions"]', expected: '/goose-prices/regions' },
            { name: '价格趋势', selector: 'a[href="/goose-prices/trends"]', expected: '/goose-prices/trends' },
            { name: '知识文章', selector: 'a[href="/knowledge"]', expected: '/knowledge' },
            { name: '分类管理', selector: 'a[href="/knowledge/categories"]', expected: '/knowledge/categories' },
            { name: '标签管理', selector: 'a[href="/knowledge/tags"]', expected: '/knowledge/tags' },
            { name: '公告列表', selector: 'a[href="/announcements"]', expected: '/announcements' },
            { name: '发布公告', selector: 'a[href="/announcements/create"]', expected: '/announcements/create' },
            { name: '商城管理', selector: 'a[href="/mall"]', expected: '/mall' },
            { name: '商品管理', selector: 'a[href="/mall/products"]', expected: '/mall/products' },
            { name: '订单管理', selector: 'a[href="/mall/orders"]', expected: '/mall/orders' },
            { name: '分类管理', selector: 'a[href="/mall/categories"]', expected: '/mall/categories' },
            { name: 'AI接口管理', selector: 'a[href="/ai-config"]', expected: '/ai-config' },
            { name: 'AI模型配置', selector: 'a[href="/ai-config/models"]', expected: '/ai-config/models' },
            { name: 'API密钥管理', selector: 'a[href="/ai-config/keys"]', expected: '/ai-config/keys' },
            { name: '数据统计确认', selector: 'a[href="/data-statistics"]', expected: '/data-statistics' },
            { name: '库存数据确认', selector: 'a[href="/inventory-data"]', expected: '/inventory-data' },
            { name: '平台用户', selector: 'a[href="/platform-users"]', expected: '/platform-users' },
            { name: '用户权限', selector: 'a[href="/platform-users/permissions"]', expected: '/platform-users/permissions' },
            { name: '统计报告', selector: 'a[href="/reports"]', expected: '/reports' },
            { name: '系统设置', selector: 'a[href="/system"]', expected: '/system' },
            { name: '系统配置', selector: 'a[href="/system/config"]', expected: '/system/config' },
            { name: '备份管理', selector: 'a[href="/system/backup"]', expected: '/system/backup' },
            { name: '日志管理', selector: 'a[href="/system/logs"]', expected: '/system/logs' }
        ];
        
        // 测试每个菜单项
        for (let i = 0; i < menuItems.length; i++) {
            const item = menuItems[i];
            console.log(`\\n${i + 2}. 测试 "${item.name}"...`);
            
            try {
                // 检查链接是否存在
                const linkExists = await page.locator(item.selector).count() > 0;
                if (!linkExists) {
                    console.log(`   ⚠️  菜单链接不存在: ${item.selector}`);
                    continue;
                }
                
                // 点击链接
                await page.click(item.selector);
                await page.waitForTimeout(2000);
                
                // 检查URL
                const currentURL = page.url();
                if (currentURL.includes(item.expected)) {
                    // 检查是否有错误信息
                    const hasError = await page.locator('.error, .alert-danger, [class*="error"]').count() > 0;
                    if (hasError) {
                        const errorText = await page.locator('.error, .alert-danger, [class*="error"]').first().textContent();
                        console.log(`   ❌ 页面有错误: ${errorText.slice(0, 100)}...`);
                    } else {
                        console.log(`   ✅ 正常访问: ${currentURL}`);
                    }
                } else {
                    console.log(`   ⚠️  URL不匹配: 期望包含 ${item.expected}, 实际 ${currentURL}`);
                }
                
            } catch (error) {
                console.log(`   ❌ 测试失败: ${error.message.slice(0, 100)}`);
            }
            
            // 每5个菜单项后返回首页，避免导航问题
            if ((i + 1) % 5 === 0) {
                await page.goto('http://localhost:3002/dashboard');
                await page.waitForTimeout(1000);
            }
        }
        
        console.log('\\n✅ 菜单测试完成');
        
    } catch (error) {
        console.error('测试过程中出现错误:', error);
    } finally {
        await browser.close();
    }
}

// 运行测试
testSpecificMenuItems().catch(console.error);