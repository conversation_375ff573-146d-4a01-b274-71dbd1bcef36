<%- contentFor('title') %>创建用户

<div class="container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-6">
                <h4 class="page-title">平台用户 - 创建用户</h4>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/dashboard">首页</a></li>
                    <li class="breadcrumb-item"><a href="/platform-users">平台用户</a></li>
                    <li class="breadcrumb-item active">创建用户</li>
                </ol>
            </div>
            <div class="col-sm-6">
                <div class="float-right">
                    <a href="/platform-users" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 offset-lg-2">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-plus text-primary"></i>
                        新建用户
                    </h5>
                </div>
                <div class="card-body">
                    <form action="/platform-users" method="POST" id="createUserForm">
                        <div class="row">
                            <!-- 基本信息 -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">用户名 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                    <div class="form-text">用于登录的唯一用户名</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">邮箱 <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">手机号码</label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">真实姓名 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" required>
                                </div>
                            </div>

                            <!-- 账户配置 -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">用户角色 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="role" name="role" required>
                                        <option value="">请选择角色</option>
                                        <option value="admin">系统管理员</option>
                                        <option value="tenant_admin">租户管理员</option>
                                        <option value="user">普通用户</option>
                                        <option value="operator">操作员</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">账户状态 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="active">激活</option>
                                        <option value="inactive">未激活</option>
                                        <option value="suspended">已暂停</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 密码设置 -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">初始密码 <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('password')">
                                            <i class="fas fa-eye" id="password-icon"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">密码长度至少8位，建议包含字母和数字</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">确认密码 <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                            </div>

                            <!-- 权限配置 -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">权限配置</label>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="can_manage_tenants" name="permissions[]" value="manage_tenants">
                                                <label class="form-check-label" for="can_manage_tenants">
                                                    租户管理权限
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="can_manage_users" name="permissions[]" value="manage_users">
                                                <label class="form-check-label" for="can_manage_users">
                                                    用户管理权限
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="can_manage_system" name="permissions[]" value="manage_system">
                                                <label class="form-check-label" for="can_manage_system">
                                                    系统管理权限
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="can_view_analytics" name="permissions[]" value="view_analytics">
                                                <label class="form-check-label" for="can_view_analytics">
                                                    数据分析权限
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="can_manage_content" name="permissions[]" value="manage_content">
                                                <label class="form-check-label" for="can_manage_content">
                                                    内容管理权限
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="require_2fa" name="security_options[]" value="require_2fa">
                                                <label class="form-check-label" for="require_2fa">
                                                    要求双因子认证
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 备注 -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">备注</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="可选的用户备注信息"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="/platform-users" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> 取消
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> 创建用户
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 密码显示/隐藏切换
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '-icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// 表单验证
document.getElementById('createUserForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    // 验证密码
    if (password.length < 8) {
        alert('密码长度至少需要8位');
        return false;
    }
    
    // 验证密码匹配
    if (password !== confirmPassword) {
        alert('两次输入的密码不匹配');
        return false;
    }
    
    // 验证必填字段
    const requiredFields = ['username', 'email', 'full_name', 'role', 'status'];
    for (let field of requiredFields) {
        if (!document.getElementById(field).value.trim()) {
            alert(`请填写${document.querySelector('label[for="' + field + '"]').textContent.replace(' *', '')}`);
            return false;
        }
    }
    
    // 如果验证通过，提交表单
    this.submit();
});

// 角色选择时的权限预设
document.getElementById('role').addEventListener('change', function() {
    const role = this.value;
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    
    // 清除所有选择
    checkboxes.forEach(cb => cb.checked = false);
    
    // 根据角色预设权限
    switch(role) {
        case 'admin':
            checkboxes.forEach(cb => cb.checked = true);
            break;
        case 'tenant_admin':
            document.getElementById('can_manage_users').checked = true;
            document.getElementById('can_view_analytics').checked = true;
            document.getElementById('can_manage_content').checked = true;
            break;
        case 'operator':
            document.getElementById('can_manage_content').checked = true;
            break;
    }
});
</script>