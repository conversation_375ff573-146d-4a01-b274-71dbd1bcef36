<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-edit me-2"></i>编辑平台用户</h2>
                <a href="/platform-users" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回列表
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user me-2"></i>用户信息</h5>
                </div>
                <div class="card-body">
                    <form id="editUserForm" method="POST" action="/platform-users/<%= user.id %>/update">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">用户名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<%= user.username %>" readonly>
                                <div class="form-text">用户名创建后不可修改</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">邮箱 <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<%= user.email %>" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<%= user.name %>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">角色 <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">请选择角色</option>
                                    <option value="super_admin" <%= user.role === 'super_admin' ? 'selected' : '' %>>超级管理员</option>
                                    <option value="admin" <%= user.role === 'admin' ? 'selected' : '' %>>管理员</option>
                                    <option value="manager" <%= user.role === 'manager' ? 'selected' : '' %>>经理</option>
                                    <option value="support" <%= user.role === 'support' ? 'selected' : '' %>>客服</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">状态 <span class="text-danger">*</span></label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="active" <%= user.status === 'active' ? 'selected' : '' %>>正常</option>
                                    <option value="inactive" <%= user.status === 'inactive' ? 'selected' : '' %>>停用</option>
                                    <option value="suspended" <%= user.status === 'suspended' ? 'selected' : '' %>>暂停</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">最后登录</label>
                                <div class="form-control-plaintext">
                                    <% if (user.last_login) { %>
                                        <%= Utils.formatDate(user.last_login) %>
                                    <% } else { %>
                                        从未登录
                                    <% } %>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="button" class="btn btn-warning me-2" onclick="resetPassword(<%= user.id %>)">
                                            <i class="fas fa-key me-1"></i>重置密码
                                        </button>
                                        <a href="/platform-users/<%= user.id %>/permissions" class="btn btn-info me-2">
                                            <i class="fas fa-user-cog me-1"></i>权限管理
                                        </a>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-outline-secondary me-2" onclick="window.location.href='/platform-users'">
                                            <i class="fas fa-times me-1"></i>取消
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>更新用户
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle me-2"></i>用户信息</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>创建时间：</strong><br>
                        <%= Utils.formatDate(user.created_at) %>
                    </div>
                    <div class="mb-3">
                        <strong>登录次数：</strong><br>
                        <%= user.login_count || 0 %> 次
                    </div>
                    <div class="mb-3">
                        <strong>账户状态：</strong><br>
                        <% if (user.status === 'active') { %>
                            <span class="badge bg-success">正常</span>
                        <% } else if (user.status === 'inactive') { %>
                            <span class="badge bg-danger">停用</span>
                        <% } else { %>
                            <span class="badge bg-warning">暂停</span>
                        <% } %>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-shield-alt me-2"></i>安全设置</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <small>
                            <strong>安全提醒：</strong><br>
                            • 定期更新密码<br>
                            • 审查用户权限<br>
                            • 监控异常登录<br>
                            • 超级管理员不可删除
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function resetPassword(userId) {
    if (confirm('确定要重置该用户的密码吗？')) {
        const newPassword = prompt('请输入新密码（留空则生成随机密码）：') || generateRandomPassword();
        
        if (newPassword) {
            fetch(`/api/platform-users/${userId}/reset-password`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ new_password: newPassword })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    alert(`密码重置成功！新密码：${newPassword}`);
                } else {
                    alert('重置失败：' + result.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('重置失败，请稍后重试');
            });
        }
    }
}

function generateRandomPassword() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let result = '';
    for (let i = 0; i < 12; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}
</script>