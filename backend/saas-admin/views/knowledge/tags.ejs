<%- contentFor('title') %>标签管理

<div class="container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-6">
                <h4 class="page-title">知识库 - 标签管理</h4>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/dashboard">首页</a></li>
                    <li class="breadcrumb-item"><a href="/knowledge">知识库</a></li>
                    <li class="breadcrumb-item active">标签管理</li>
                </ol>
            </div>
            <div class="col-sm-6">
                <div class="float-right">
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#createTagModal">
                        <i class="fas fa-plus"></i> 添加标签
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">标签列表</h4>
                    <div class="card-options">
                        <div class="input-group">
                            <input type="text" id="searchInput" class="form-control" placeholder="搜索标签...">
                            <div class="input-group-append">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>标签名称</th>
                                    <th>颜色</th>
                                    <th>使用次数</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="tagsTable">
                                <% if (tags && tags.length > 0) { %>
                                    <% tags.forEach(tag => { %>
                                        <tr data-tag-id="<%= tag.id %>">
                                            <td><%= tag.id %></td>
                                            <td>
                                                <span class="badge" style="background-color: <%= tag.color || '#007bff' %>; color: white;">
                                                    <%= tag.name %>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="color-preview" style="width: 20px; height: 20px; background-color: <%= tag.color || '#007bff' %>; border-radius: 3px; display: inline-block;"></div>
                                                <%= tag.color || '#007bff' %>
                                            </td>
                                            <td><%= tag.usage_count || 0 %></td>
                                            <td>
                                                <span class="badge <%= tag.status === 'active' ? 'badge-success' : 'badge-secondary' %>">
                                                    <%= tag.status === 'active' ? '启用' : '禁用' %>
                                                </span>
                                            </td>
                                            <td><%= Utils.formatDate(tag.created_at, 'YYYY-MM-DD HH:mm') %></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary edit-tag" data-tag-id="<%= tag.id %>">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger delete-tag" data-tag-id="<%= tag.id %>">
                                                    <i class="fas fa-trash"></i> 删除
                                                </button>
                                            </td>
                                        </tr>
                                    <% }) %>
                                <% } else { %>
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">暂无标签数据</p>
                                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTagModal">
                                                <i class="fas fa-plus"></i> 创建第一个标签
                                            </button>
                                        </td>
                                    </tr>
                                <% } %>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建标签模态框 -->
<div class="modal fade" id="createTagModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加标签</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createTagForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="tagName" class="form-label">标签名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="tagName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="tagColor" class="form-label">标签颜色</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="color" class="form-control form-control-color" id="tagColor" name="color" value="#007bff">
                            </div>
                            <div class="col-6">
                                <input type="text" class="form-control" id="tagColorText" placeholder="#007bff" value="#007bff">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="tagDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="tagDescription" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="tagStatus" class="form-label">状态</label>
                        <select class="form-select" id="tagStatus" name="status">
                            <option value="active">启用</option>
                            <option value="inactive">禁用</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑标签模态框 -->
<div class="modal fade" id="editTagModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑标签</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editTagForm">
                <input type="hidden" id="editTagId" name="id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editTagName" class="form-label">标签名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="editTagName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editTagColor" class="form-label">标签颜色</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="color" class="form-control form-control-color" id="editTagColor" name="color">
                            </div>
                            <div class="col-6">
                                <input type="text" class="form-control" id="editTagColorText" placeholder="#007bff">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editTagDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="editTagDescription" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="editTagStatus" class="form-label">状态</label>
                        <select class="form-select" id="editTagStatus" name="status">
                            <option value="active">启用</option>
                            <option value="inactive">禁用</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">更新</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // 颜色选择器同步
    $('#tagColor, #editTagColor').on('input', function() {
        const isEdit = $(this).attr('id').includes('edit');
        const textInput = isEdit ? $('#editTagColorText') : $('#tagColorText');
        textInput.val($(this).val());
    });

    $('#tagColorText, #editTagColorText').on('input', function() {
        const isEdit = $(this).attr('id').includes('edit');
        const colorInput = isEdit ? $('#editTagColor') : $('#tagColor');
        if (/^#[0-9A-F]{6}$/i.test($(this).val())) {
            colorInput.val($(this).val());
        }
    });

    // 创建标签
    $('#createTagForm').on('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        $.ajax({
            url: '/knowledge/tags',
            method: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    $('#createTagModal').modal('hide');
                    location.reload();
                    showAlert('标签创建成功', 'success');
                } else {
                    showAlert(response.message || '创建失败', 'error');
                }
            },
            error: function() {
                showAlert('网络错误，请重试', 'error');
            }
        });
    });

    // 编辑标签
    $('.edit-tag').on('click', function() {
        const tagId = $(this).data('tag-id');
        
        $.ajax({
            url: `/knowledge/tags/${tagId}`,
            method: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    const tag = response.data;
                    $('#editTagId').val(tag.id);
                    $('#editTagName').val(tag.name);
                    $('#editTagColor').val(tag.color || '#007bff');
                    $('#editTagColorText').val(tag.color || '#007bff');
                    $('#editTagDescription').val(tag.description || '');
                    $('#editTagStatus').val(tag.status);
                    $('#editTagModal').modal('show');
                } else {
                    showAlert(response.message || '获取标签信息失败', 'error');
                }
            },
            error: function() {
                showAlert('网络错误，请重试', 'error');
            }
        });
    });

    // 更新标签
    $('#editTagForm').on('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);
        const tagId = data.id;

        $.ajax({
            url: `/knowledge/tags/${tagId}`,
            method: 'PUT',
            data: data,
            success: function(response) {
                if (response.success) {
                    $('#editTagModal').modal('hide');
                    location.reload();
                    showAlert('标签更新成功', 'success');
                } else {
                    showAlert(response.message || '更新失败', 'error');
                }
            },
            error: function() {
                showAlert('网络错误，请重试', 'error');
            }
        });
    });

    // 删除标签
    $('.delete-tag').on('click', function() {
        const tagId = $(this).data('tag-id');
        
        if (confirm('确定要删除这个标签吗？删除后不可恢复。')) {
            $.ajax({
                url: `/knowledge/tags/${tagId}`,
                method: 'DELETE',
                success: function(response) {
                    if (response.success) {
                        location.reload();
                        showAlert('标签删除成功', 'success');
                    } else {
                        showAlert(response.message || '删除失败', 'error');
                    }
                },
                error: function() {
                    showAlert('网络错误，请重试', 'error');
                }
            });
        }
    });

    // 搜索功能
    $('#searchInput').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        $('#tagsTable tr').each(function() {
            const tagName = $(this).find('td:nth-child(2)').text().toLowerCase();
            if (tagName.includes(searchTerm) || searchTerm === '') {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // 模态框重置
    $('#createTagModal').on('hidden.bs.modal', function() {
        $('#createTagForm')[0].reset();
        $('#tagColor').val('#007bff');
        $('#tagColorText').val('#007bff');
    });

    function showAlert(message, type) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alert = `<div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;
        $('.container-fluid').prepend(alert);
        
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }
});
</script>