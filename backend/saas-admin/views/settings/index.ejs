<%- contentFor('body') %>

  <div class="content-wrapper">
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>系统设置</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="/dashboard">首页</a></li>
              <li class="breadcrumb-item active">系统设置</li>
            </ol>
          </div>
        </div>
      </div>
    </div>

    <section class="content">
      <div class="container-fluid">
        <!-- 系统状态卡片 -->
        <div class="row">
          <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
              <div class="inner">
                <h3>
                  <%= stats.totalUsers %>
                </h3>
                <p>总用户数</p>
              </div>
              <div class="icon">
                <i class="fas fa-users"></i>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
              <div class="inner">
                <h3>
                  <%= stats.activeUsers %>
                </h3>
                <p>活跃用户</p>
              </div>
              <div class="icon">
                <i class="fas fa-user-check"></i>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
              <div class="inner">
                <h3>
                  <%= stats.diskUsage %>
                </h3>
                <p>磁盘使用率</p>
              </div>
              <div class="icon">
                <i class="fas fa-hdd"></i>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
              <div class="inner">
                <h3>
                  <%= stats.memoryUsage %>
                </h3>
                <p>内存使用率</p>
              </div>
              <div class="icon">
                <i class="fas fa-memory"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- 设置标签页 -->
        <div class="card">
          <div class="card-header p-2">
            <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
              <li class="nav-item">
                <a class="nav-link active" id="general-tab" data-toggle="tab" href="#general" role="tab">基本设置</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" id="system-tab" data-toggle="tab" href="#system" role="tab">系统信息</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" id="maintenance-tab" data-toggle="tab" href="#maintenance" role="tab">维护工具</a>
              </li>
            </ul>
          </div>
          <div class="card-body">
            <div class="tab-content">
              <!-- 基本设置 -->
              <div class="tab-pane active" id="general" role="tabpanel">
                <form class="form-horizontal">
                  <div class="form-group row">
                    <label class="col-sm-2 col-form-label">站点名称</label>
                    <div class="col-sm-10">
                      <input type="text" class="form-control" value="<%= systemConfig.siteName %>">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="col-sm-2 col-form-label">站点URL</label>
                    <div class="col-sm-10">
                      <input type="url" class="form-control" value="<%= systemConfig.siteUrl %>">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="col-sm-2 col-form-label">管理员邮箱</label>
                    <div class="col-sm-10">
                      <input type="email" class="form-control" value="<%= systemConfig.adminEmail %>">
                    </div>
                  </div>
                  <div class="form-group row">
                    <div class="col-sm-offset-2 col-sm-10">
                      <button type="button" class="btn btn-primary" onclick="saveSettings()">保存设置</button>
                      <button type="button" class="btn btn-success" onclick="applySettings()">应用设置</button>
                      <button type="button" class="btn btn-warning" onclick="resetSettings()">重置设置</button>
                    </div>
                  </div>
                </form>
              </div>

              <!-- 系统信息 -->
              <div class="tab-pane" id="system" role="tabpanel">
                <table class="table table-bordered">
                  <thead>
                    <tr>
                      <th>项目</th>
                      <th>值</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% systemInfo.forEach(info=> { %>
                      <tr>
                        <td>
                          <%= info.key %>
                        </td>
                        <td>
                          <%= info.value %>
                        </td>
                      </tr>
                      <% }); %>
                  </tbody>
                </table>
              </div>

              <!-- 维护工具 -->
              <div class="tab-pane" id="maintenance" role="tabpanel">
                <div class="row">
                  <div class="col-md-6">
                    <div class="card">
                      <div class="card-header">
                        <h3 class="card-title">数据管理</h3>
                      </div>
                      <div class="card-body">
                        <button type="button" class="btn btn-info btn-block" onclick="backupData()">备份数据</button>
                        <button type="button" class="btn btn-warning btn-block" onclick="clearCache()">清除缓存</button>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="card">
                      <div class="card-header">
                        <h3 class="card-title">系统维护</h3>
                      </div>
                      <div class="card-body">
                        <button type="button" class="btn btn-danger btn-block" onclick="restartSystem()">重启系统</button>
                        <button type="button" class="btn btn-secondary btn-block" onclick="viewLogs()">查看日志</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <script>
    function saveSettings() {
      alert('设置已保存');
    }

    function applySettings() {
      alert('设置已应用');
    }

    function resetSettings() {
      if (confirm('确定要重置所有设置吗？')) {
        alert('设置已重置');
      }
    }

    function backupData() {
      alert('数据备份已开始');
    }

    function clearCache() {
      if (confirm('确定要清除缓存吗？')) {
        alert('缓存已清除');
      }
    }

    function restartSystem() {
      if (confirm('确定要重启系统吗？这将中断所有用户连接。')) {
        alert('系统重启中...');
      }
    }

    function viewLogs() {
      window.open('/logs', '_blank');
    }
  </script>