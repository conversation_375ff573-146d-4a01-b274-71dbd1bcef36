<!-- 地区价格管理页面 -->
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>地区价格管理</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/dashboard">首页</a></li>
                        <li class="breadcrumb-item"><a href="/goose-prices">鹅价管理</a></li>
                        <li class="breadcrumb-item active">地区配置</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <section class="content">
        <div class="container-fluid">
            <!-- 演示数据提示 -->
            <% if (typeof isDemo !== 'undefined' && isDemo) { %>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>演示模式:</strong> <%= demoMessage || '当前显示的是演示数据' %>
            </div>
            <% } %>

            <!-- 地区价格概览卡片 -->
            <div class="row">
                <% regions.forEach(function(region, index) { %>
                <div class="col-lg-3 col-md-6">
                    <div class="small-box bg-<%= index % 4 === 0 ? 'info' : index % 4 === 1 ? 'success' : index % 4 === 2 ? 'warning' : 'danger' %>">
                        <div class="inner">
                            <h3>¥<%= parseFloat(region.avg_price || 0).toFixed(2) %></h3>
                            <p><%= region.region %></p>
                            <small>记录: <%= region.record_count %> 条</small>
                        </div>
                        <div class="icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <a href="/goose-prices?region=<%= encodeURIComponent(region.region) %>" class="small-box-footer">
                            查看详情 <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
                <% }); %>
            </div>

            <!-- 地区价格对比图表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">地区价格对比趋势</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div style="position: relative; height: 400px;">
                        <canvas id="regionComparisonChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 地区详细数据表格 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">地区价格详细信息</h3>
                    <div class="card-tools">
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-primary" onclick="exportRegionData('excel')">
                                <i class="fas fa-file-excel"></i> 导出Excel
                            </button>
                            <button type="button" class="btn btn-sm btn-secondary" onclick="exportRegionData('pdf')">
                                <i class="fas fa-file-pdf"></i> 导出PDF
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th>地区名称</th>
                                <th>记录数量</th>
                                <th>平均价格 (¥/斤)</th>
                                <th>最高价格 (¥/斤)</th>
                                <th>最低价格 (¥/斤)</th>
                                <th>价格波动</th>
                                <th>最近更新</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% regions.forEach(function(region) { %>
                            <tr>
                                <td>
                                    <i class="fas fa-map-marker-alt text-primary"></i>
                                    <strong><%= region.region %></strong>
                                </td>
                                <td>
                                    <span class="badge badge-info"><%= region.record_count %></span>
                                </td>
                                <td>
                                    <span class="text-primary font-weight-bold">¥<%= parseFloat(region.avg_price || 0).toFixed(2) %></span>
                                </td>
                                <td>
                                    <span class="text-success">¥<%= parseFloat(region.max_price || 0).toFixed(2) %></span>
                                </td>
                                <td>
                                    <span class="text-danger">¥<%= parseFloat(region.min_price || 0).toFixed(2) %></span>
                                </td>
                                <td>
                                    <% var volatility = region.avg_price > 0 ? ((region.max_price - region.min_price) / region.avg_price * 100).toFixed(1) : 0; %>
                                    <span class="badge badge-<%= volatility > 15 ? 'danger' : volatility > 10 ? 'warning' : 'success' %>">
                                        <%= volatility %>%
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <i class="far fa-calendar-alt"></i> 
                                        <%= new Date(region.latest_date).toLocaleDateString('zh-CN') %>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-info" onclick="viewRegionDetails('<%= region.region %>')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="editRegionPrice('<%= region.region %>')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
$(document).ready(function() {
    // 初始化图表
    initRegionComparisonChart();
});

// 地区对比图表
let regionChart;
function initRegionComparisonChart() {
    const ctx = document.getElementById('regionComparisonChart').getContext('2d');
    
    const chartData = <%- JSON.stringify(chartData) %>;
    
    regionChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: chartData.series.map((series, index) => ({
                label: series.name,
                data: series.data,
                borderColor: `hsl(${index * 60}, 70%, 50%)`,
                backgroundColor: `hsla(${index * 60}, 70%, 50%, 0.1)`,
                borderWidth: 2,
                fill: false,
                tension: 0.1
            }))
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '各地区鹅价对比趋势图'
                },
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    title: {
                        display: true,
                        text: '价格 (¥/斤)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '日期'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

// 查看地区详情
function viewRegionDetails(region) {
    window.location.href = `/goose-prices?region=${encodeURIComponent(region)}`;
}

// 编辑地区价格
function editRegionPrice(region) {
    window.location.href = `/goose-prices/create?region=${encodeURIComponent(region)}`;
}

// 导出地区数据
function exportRegionData(format) {
    const params = new URLSearchParams({
        format: format,
        type: 'regions'
    });
    window.open(`/api/goose-prices/export?${params}`);
}
</script>