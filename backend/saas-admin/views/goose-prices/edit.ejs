<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-edit me-2"></i>编辑价格记录</h2>
                <a href="/goose-prices" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回列表
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-edit me-2"></i>价格信息</h5>
                </div>
                <div class="card-body">
                    <form id="editPriceForm" method="PUT">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="record_date" class="form-label">记录日期 <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="record_date" name="record_date" 
                                       value="<%= price.date %>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="region" class="form-label">地区 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="region" name="region" 
                                       value="<%= price.region %>" placeholder="如：广东广州" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="market_name" class="form-label">市场名称</label>
                                <input type="text" class="form-control" id="market_name" name="market_name" 
                                       value="<%= price.market_name || '' %>" placeholder="如：江南农副产品批发市场">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="goose_type" class="form-label">鹅种类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="goose_type" name="goose_type" required>
                                    <option value="">请选择鹅种类型</option>
                                    <option value="白鹅" <%= price.breed === '白鹅' ? 'selected' : '' %>>白鹅</option>
                                    <option value="灰鹅" <%= price.breed === '灰鹅' ? 'selected' : '' %>>灰鹅</option>
                                    <option value="黑鹅" <%= price.breed === '黑鹅' ? 'selected' : '' %>>黑鹅</option>
                                    <option value="狮头鹅" <%= price.breed === '狮头鹅' ? 'selected' : '' %>>狮头鹅</option>
                                    <option value="太湖鹅" <%= price.breed === '太湖鹅' ? 'selected' : '' %>>太湖鹅</option>
                                    <option value="四川白鹅" <%= price.breed === '四川白鹅' ? 'selected' : '' %>>四川白鹅</option>
                                    <option value="莱茵鹅" <%= price.breed === '莱茵鹅' ? 'selected' : '' %>>莱茵鹅</option>
                                    <option value="其他" <%= price.breed === '其他' ? 'selected' : '' %>>其他</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="price_per_kg" class="form-label">价格 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" class="form-control" id="price_per_kg" name="price_per_kg" 
                                           value="<%= price.price_per_kg %>" step="0.01" min="0" placeholder="0.00" required>
                                    <span class="input-group-text">/<%= price.unit || '斤' %></span>
                                </div>
                                <div class="form-text">请输入每斤的价格</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="unit" class="form-label">单位</label>
                                <select class="form-select" id="unit" name="unit">
                                    <option value="斤" <%= price.unit === '斤' ? 'selected' : '' %>>斤</option>
                                    <option value="公斤" <%= price.unit === '公斤' ? 'selected' : '' %>>公斤</option>
                                    <option value="千克" <%= price.unit === '千克' ? 'selected' : '' %>>千克</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="notes" class="form-label">备注</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" 
                                          placeholder="请输入价格变动原因、市场情况等备注信息..."><%= price.notes || '' %></textarea>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_published" 
                                           name="is_published" <%= price.is_published ? 'checked' : '' %>>
                                    <label class="form-check-label" for="is_published">
                                        立即发布到公共价格信息
                                    </label>
                                    <div class="form-text">勾选后，价格信息将对所有用户可见</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-outline-secondary" onclick="window.location.href='/goose-prices'">
                                        <i class="fas fa-times me-1"></i>取消
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>更新价格记录
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle me-2"></i>操作指南</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-1"></i>录入建议：</h6>
                        <ul class="mb-0 ps-3">
                            <li>请确保价格数据准确可靠</li>
                            <li>建议附上具体市场名称以提高可信度</li>
                            <li>适当添加备注说明价格变动原因</li>
                            <li>发布前请检查所有信息的准确性</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-1"></i>注意事项：</h6>
                        <ul class="mb-0 ps-3">
                            <li>价格信息一旦发布将影响市场预期</li>
                            <li>请确认价格单位是否正确</li>
                            <li>建议在价格有重大变化时添加说明</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('editPriceForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    try {
        const response = await fetch(`/goose-prices/edit/<%= price.id %>`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('价格记录更新成功！');
            window.location.href = '/goose-prices';
        } else {
            alert('更新失败：' + result.message);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('更新失败，请稍后重试');
    }
});
</script>