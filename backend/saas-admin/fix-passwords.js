const bcrypt = require('bcrypt');
const db = require('./config/database');

async function fixPasswords() {
    try {
        console.log('🔧 开始修复密码...');
        
        // 生成正确的密码哈希
        const passwordHash = await bcrypt.hash('admin123', 10);
        console.log('✅ 密码哈希生成成功');
        
        // 更新super_admin密码
        await db.execute('UPDATE platform_admins SET password = ? WHERE username = ?', [passwordHash, 'super_admin']);
        console.log('✅ super_admin密码已更新');
        
        // 验证密码
        const users = await db.execute('SELECT username, password FROM platform_admins WHERE username = ?', ['super_admin']);
        if (users.length > 0) {
            const isValid = await bcrypt.compare('admin123', users[0].password);
            console.log('🔐 密码验证结果:', isValid ? '成功' : '失败');
        }
        
        console.log('🎉 密码修复完成！');
        console.log('登录信息: super_admin / admin123');
        
    } catch (error) {
        console.error('❌ 修复失败:', error);
    }
}

fixPasswords();
