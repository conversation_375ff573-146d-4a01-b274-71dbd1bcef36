/**
 * 智慧养鹅SaaS平台 - SAAS管理后台
 * 专用于平台级管理功能
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');

// 创建Express应用
const app = express();
const PORT = process.env.SAAS_PORT || 3003;

// 中间件配置
app.use(helmet());
app.use(compression());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true
}));

// 请求解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 最多100个请求
  message: { error: '请求过于频繁，请稍后再试' }
});
app.use('/api', limiter);

// 基础路由
app.get('/', (req, res) => {
  res.json({
    message: '智慧养鹅SaaS平台 - SAAS管理后台',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString()
  });
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'saas-admin',
    port: PORT,
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// SAAS管理API路由
app.get('/api/v1/tenants', (req, res) => {
  res.json({
    success: true,
    message: '租户列表接口 (待实现)',
    data: []
  });
});

app.get('/api/v1/system/stats', (req, res) => {
  res.json({
    success: true,
    message: '系统统计接口',
    data: {
      totalTenants: 0,
      activeUsers: 0,
      systemUptime: process.uptime()
    }
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('SAAS管理后台错误:', error);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: '接口未找到',
    path: req.path
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 SAAS管理后台启动成功`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🌍 运行环境: ${process.env.NODE_ENV || 'development'}`);
  console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('🛑 收到SIGTERM信号，正在关闭SAAS管理后台...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 收到SIGINT信号，正在关闭SAAS管理后台...');
  process.exit(0);
});

module.exports = app;