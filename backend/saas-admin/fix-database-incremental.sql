-- 修复现有数据库表结构

-- 1. 修复 knowledge_tags 表，添加缺失字段
ALTER TABLE `knowledge_tags` 
ADD COLUMN IF NOT EXISTS `color` varchar(10) DEFAULT '#007bff' COMMENT '标签颜色' AFTER `name`,
ADD COLUMN IF NOT EXISTS `description` text COMMENT '标签描述' AFTER `color`,
ADD COLUMN IF NOT EXISTS `status` enum('active','inactive') DEFAULT 'active' COMMENT '状态' AFTER `usage_count`,
ADD COLUMN IF NOT EXISTS `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`;

-- 2. 检查并创建缺失的表

-- 商品评价表
CREATE TABLE IF NOT EXISTS `mall_reviews` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `user_id` int NOT NULL,
  `tenant_id` int NOT NULL,
  `rating` tinyint NOT NULL DEFAULT 5 COMMENT '评分1-5',
  `content` text COMMENT '评价内容',
  `images` json DEFAULT NULL COMMENT '评价图片',
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品评价表';

-- 优惠券表
CREATE TABLE IF NOT EXISTS `mall_coupons` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '优惠券名称',
  `code` varchar(50) NOT NULL COMMENT '优惠券代码',
  `type` enum('percent','fixed','free_shipping') DEFAULT 'percent' COMMENT '优惠类型',
  `value` decimal(10,2) NOT NULL COMMENT '优惠值',
  `min_amount` decimal(10,2) DEFAULT 0.00 COMMENT '最小金额',
  `max_discount` decimal(10,2) DEFAULT NULL COMMENT '最大折扣',
  `usage_limit` int DEFAULT NULL COMMENT '使用次数限制',
  `used_count` int DEFAULT 0 COMMENT '已使用次数',
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `status` enum('active','inactive','expired') DEFAULT 'active',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`),
  KEY `idx_status` (`status`),
  KEY `idx_dates` (`start_date`, `end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券表';

-- 促销活动表
CREATE TABLE IF NOT EXISTS `mall_promotions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL COMMENT '促销名称',
  `description` text COMMENT '促销描述',
  `type` enum('discount','buy_one_get_one','bundle','flash_sale') DEFAULT 'discount',
  `discount_type` enum('percent','fixed') DEFAULT 'percent',
  `discount_value` decimal(10,2) DEFAULT 0.00,
  `applicable_products` json DEFAULT NULL COMMENT '适用商品',
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `status` enum('active','inactive','expired') DEFAULT 'active',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_dates` (`start_date`, `end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='促销活动表';

-- 库存记录表
CREATE TABLE IF NOT EXISTS `mall_inventory` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `type` enum('in','out','adjust') DEFAULT 'adjust' COMMENT '库存变动类型',
  `quantity` int NOT NULL COMMENT '变动数量',
  `reason` varchar(500) DEFAULT NULL COMMENT '变动原因',
  `reference_id` int DEFAULT NULL COMMENT '关联ID(如订单ID)',
  `reference_type` varchar(50) DEFAULT NULL COMMENT '关联类型',
  `operator_id` int DEFAULT NULL COMMENT '操作员ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_type` (`type`),
  KEY `idx_reference` (`reference_id`, `reference_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存记录表';

-- 3. 插入一些初始标签数据（忽略重复）
INSERT IGNORE INTO `knowledge_tags` (`name`, `color`, `description`) VALUES
('新手必读', '#28a745', '适合新手阅读的文章'),
('高级技巧', '#dc3545', '高级养殖技巧'),
('季节性', '#ffc107', '季节性相关内容'),
('疾病预防', '#6f42c1', '疾病预防相关'),
('营养管理', '#fd7e14', '营养和饲料管理'),
('市场分析', '#20c997', '市场行情分析'),
('政策解读', '#6610f2', '政策法规解读');

-- 4. 插入一些商品示例数据（如果表存在且为空）
INSERT IGNORE INTO `mall_products` (`name`, `description`, `category_id`, `price`, `stock`) VALUES
('优质玉米饲料', '营养丰富的玉米饲料，适合各阶段鹅群', 1, 45.00, 1000),
('维生素营养剂', '专用维生素补充剂，增强免疫力', 1, 28.50, 500),
('自动饮水器', '不锈钢材质，自动供水系统', 2, 156.00, 200),
('鹅舍保温灯', '节能LED保温灯，适合育雏期使用', 2, 89.00, 150),
('禽流感疫苗', '预防禽流感专用疫苗', 3, 12.00, 300);

-- 5. 插入一些优惠券示例数据
INSERT IGNORE INTO `mall_coupons` (`name`, `code`, `type`, `value`, `min_amount`, `start_date`, `end_date`) VALUES
('新用户注册优惠', 'NEW2024', 'percent', 10.00, 50.00, '2024-01-01 00:00:00', '2024-12-31 23:59:59'),
('满100减20', 'SAVE20', 'fixed', 20.00, 100.00, '2024-01-01 00:00:00', '2024-12-31 23:59:59'),
('免费配送', 'FREESHIP', 'free_shipping', 0.00, 0.00, '2024-01-01 00:00:00', '2024-12-31 23:59:59');

-- 6. 插入一些促销活动示例数据
INSERT IGNORE INTO `mall_promotions` (`name`, `description`, `type`, `discount_type`, `discount_value`, `start_date`, `end_date`) VALUES
('春季养殖促销', '春季养殖用品全场9折', 'discount', 'percent', 10.00, '2024-03-01 00:00:00', '2024-05-31 23:59:59'),
('饲料买二送一', '指定饲料产品买二送一', 'buy_one_get_one', 'percent', 0.00, '2024-01-01 00:00:00', '2024-12-31 23:59:59');

COMMIT;