-- 智慧养鹅SAAS平台 v3.3.4 数据库清理与重构脚本（安全版本）
-- 执行此脚本前请备份数据库

USE smart_goose_saas;

-- 1. 清理无用表
SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS admin_users;
DROP TABLE IF EXISTS temp_admins;
DROP TABLE IF EXISTS old_platform_admins;
DROP TABLE IF EXISTS old_tenants;
DROP TABLE IF EXISTS temp_tenants;
DROP TABLE IF EXISTS backup_tenants;
DROP TABLE IF EXISTS old_api_configs;
DROP TABLE IF EXISTS temp_api_configs;
DROP TABLE IF EXISTS deprecated_api_settings;

SET FOREIGN_KEY_CHECKS = 1;

-- 2. 插入默认管理员账号（如不存在）
INSERT IGNORE INTO platform_admins (
    username, email, password, name, role, status, createdAt
) VALUES (
    'admin', 
    '<EMAIL>',
    'admin123456', -- 临时明文密码，登录后系统会自动处理
    '系统管理员',
    'super_admin',
    'active',
    NOW()
);

-- 3. 清理操作日志表（保留最近3个月的日志）
DELETE FROM operation_logs WHERE createdAt < DATE_SUB(NOW(), INTERVAL 3 MONTH);

-- 4. 清理统计表中的过期数据
DELETE FROM platform_operation_stats WHERE date < DATE_SUB(CURDATE(), INTERVAL 1 YEAR);
DELETE FROM platform_usage_stats WHERE date < DATE_SUB(CURDATE(), INTERVAL 1 YEAR);
DELETE FROM tenant_overview_stats WHERE date < DATE_SUB(CURDATE(), INTERVAL 6 MONTH);

-- 5. 优化所有表
OPTIMIZE TABLE platform_admins;
OPTIMIZE TABLE tenants;
OPTIMIZE TABLE system_configs;
OPTIMIZE TABLE operation_logs;
OPTIMIZE TABLE platform_operation_stats;
OPTIMIZE TABLE platform_usage_stats;
OPTIMIZE TABLE tenant_overview_stats;

-- 6. 显示清理后的表结构
SHOW TABLES;
SELECT COUNT(*) as admin_count FROM platform_admins;
SELECT COUNT(*) as tenant_count FROM tenants;
SELECT username, name, role, status, lastLoginAt FROM platform_admins;