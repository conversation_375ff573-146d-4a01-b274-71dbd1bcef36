-- 智慧养鹅SAAS平台 v3.3.4 数据库清理与重构脚本
-- 执行此脚本前请备份数据库

USE smart_goose_saas;

-- 1. 清理和重构核心表
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS admin_users;
DROP TABLE IF EXISTS temp_admins;
DROP TABLE IF EXISTS old_platform_admins;

-- 2. 确保platform_admins表结构正确
-- 先删除可能存在的索引，避免重复创建错误
ALTER TABLE platform_admins DROP INDEX IF EXISTS idx_username;
ALTER TABLE platform_admins DROP INDEX IF EXISTS idx_email;
ALTER TABLE platform_admins DROP INDEX IF EXISTS idx_role;
ALTER TABLE platform_admins DROP INDEX IF EXISTS idx_status;
ALTER TABLE platform_admins DROP INDEX IF EXISTS idx_lastLoginAt;

-- 重新创建索引
ALTER TABLE platform_admins 
ADD INDEX idx_username (username),
ADD INDEX idx_email (email),
ADD INDEX idx_role (role),
ADD INDEX idx_status (status),
ADD INDEX idx_lastLoginAt (lastLoginAt);

-- 3. 插入默认管理员账号（如不存在）
INSERT IGNORE INTO platform_admins (
    username, email, password, name, role, status
) VALUES (
    'admin', 
    '<EMAIL>',
    '$2b$10$rQJ8YnWx.vQJhQy5fQJhQeFQJhQy5fQJhQeFQJhQy5fQJhQeFQJhQ2', -- admin123456 的 bcrypt 哈希
    '系统管理员',
    'super_admin',
    'active'
);

-- 4. 清理重复和无用的租户相关表
DROP TABLE IF EXISTS old_tenants;
DROP TABLE IF EXISTS temp_tenants;
DROP TABLE IF EXISTS backup_tenants;

-- 5. 确保tenants表结构正确
ALTER TABLE tenants 
ADD INDEX idx_status (status),
ADD INDEX idx_subscription_status (subscription_status),
ADD INDEX idx_created_at (created_at);

-- 6. 清理无用的API配置表
DROP TABLE IF EXISTS old_api_configs;
DROP TABLE IF EXISTS temp_api_configs;
DROP TABLE IF EXISTS deprecated_api_settings;

-- 7. 优化系统配置表
ALTER TABLE system_configs
ADD INDEX idx_config_key (config_key),
ADD INDEX idx_is_active (is_active);

-- 8. 清理操作日志表（保留最近3个月的日志）
DELETE FROM operation_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 MONTH);

-- 9. 重建日志表索引
ALTER TABLE operation_logs
ADD INDEX idx_user_id (user_id),
ADD INDEX idx_action (action),
ADD INDEX idx_created_at (created_at);

-- 10. 清理统计表中的过期数据
DELETE FROM platform_operation_stats WHERE date < DATE_SUB(CURDATE(), INTERVAL 1 YEAR);
DELETE FROM platform_usage_stats WHERE date < DATE_SUB(CURDATE(), INTERVAL 1 YEAR);
DELETE FROM tenant_overview_stats WHERE date < DATE_SUB(CURDATE(), INTERVAL 6 MONTH);

-- 11. 优化所有表
OPTIMIZE TABLE platform_admins;
OPTIMIZE TABLE tenants;
OPTIMIZE TABLE system_configs;
OPTIMIZE TABLE operation_logs;
OPTIMIZE TABLE platform_operation_stats;
OPTIMIZE TABLE platform_usage_stats;
OPTIMIZE TABLE tenant_overview_stats;

-- 12. 显示清理后的表结构
SHOW TABLES;
SELECT COUNT(*) as admin_count FROM platform_admins;
SELECT COUNT(*) as tenant_count FROM tenants;