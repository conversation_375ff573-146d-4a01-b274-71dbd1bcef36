const { chromium } = require('playwright');

async function testAllPages() {
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    console.log('开始完整页面测试...');
    
    try {
        // 1. 访问登录页面
        console.log('1. 测试登录页面...');
        await page.goto('http://localhost:3002');
        await page.waitForTimeout(2000);
        
        // 输入登录信息
        await page.fill('input[name="username"]', 'admin');
        await page.fill('input[name="password"]', 'admin123456');
        await page.click('button[type="submit"]');
        
        // 等待跳转到仪表盘
        await page.waitForURL('**/dashboard');
        console.log('✅ 登录成功');
        
        // 2. 测试仪表盘页面
        console.log('2. 测试仪表盘页面...');
        await page.waitForTimeout(3000);
        
        // 检查是否有错误
        const errorElements = await page.locator('.error, .alert-danger, [class*="error"]').count();
        if (errorElements > 0) {
            const errorText = await page.locator('.error, .alert-danger, [class*="error"]').first().textContent();
            console.log('❌ 仪表盘错误:', errorText);
        } else {
            console.log('✅ 仪表盘加载正常');
        }
        
        // 3. 测试租户管理页面
        console.log('3. 测试租户管理页面...');
        await page.click('a[href="/tenants"], a:has-text("租户管理")');
        await page.waitForTimeout(2000);
        
        const tenantPageErrors = await page.locator('.error, .alert-danger, [class*="error"]').count();
        if (tenantPageErrors > 0) {
            const errorText = await page.locator('.error, .alert-danger, [class*="error"]').first().textContent();
            console.log('❌ 租户管理页面错误:', errorText);
        } else {
            console.log('✅ 租户管理页面正常');
        }
        
        // 4. 测试订阅管理页面
        console.log('4. 测试订阅管理页面...');
        try {
            await page.click('a:has-text("订阅管理")');
            await page.waitForTimeout(2000);
        } catch (e) {
            console.log('⚠️  订阅管理菜单不存在或无法点击');
        }
        
        // 5. 测试使用统计页面
        console.log('5. 测试使用统计页面...');
        try {
            await page.click('a:has-text("使用统计")');
            await page.waitForTimeout(2000);
        } catch (e) {
            console.log('⚠️  使用统计菜单不存在或无法点击');
        }
        
        // 6. 测试今日鹅价页面
        console.log('6. 测试今日鹅价页面...');
        try {
            await page.click('a:has-text("今日鹅价"), a[href*="goose-prices"]');
            await page.waitForTimeout(2000);
            
            const pricePageErrors = await page.locator('.error, .alert-danger, [class*="error"]').count();
            if (pricePageErrors > 0) {
                const errorText = await page.locator('.error, .alert-danger, [class*="error"]').first().textContent();
                console.log('❌ 今日鹅价页面错误:', errorText);
            } else {
                console.log('✅ 今日鹅价页面正常');
            }
        } catch (e) {
            console.log('⚠️  今日鹅价菜单不存在或无法点击');
        }
        
        // 7. 测试知识库管理页面
        console.log('7. 测试知识库管理页面...');
        try {
            await page.click('a:has-text("知识库管理"), a[href*="knowledge"]');
            await page.waitForTimeout(2000);
            
            const knowledgePageErrors = await page.locator('.error, .alert-danger, [class*="error"]').count();
            if (knowledgePageErrors > 0) {
                const errorText = await page.locator('.error, .alert-danger, [class*="error"]').first().textContent();
                console.log('❌ 知识库管理页面错误:', errorText);
            } else {
                console.log('✅ 知识库管理页面正常');
            }
        } catch (e) {
            console.log('⚠️  知识库管理菜单不存在或无法点击');
        }
        
        // 8. 测试公告管理页面
        console.log('8. 测试公告管理页面...');
        try {
            await page.click('a:has-text("公告管理"), a[href*="announcements"]');
            await page.waitForTimeout(2000);
            
            const announcementPageErrors = await page.locator('.error, .alert-danger, [class*="error"]').count();
            if (announcementPageErrors > 0) {
                const errorText = await page.locator('.error, .alert-danger, [class*="error"]').first().textContent();
                console.log('❌ 公告管理页面错误:', errorText);
            } else {
                console.log('✅ 公告管理页面正常');
            }
        } catch (e) {
            console.log('⚠️  公告管理菜单不存在或无法点击');
        }
        
        // 9. 测试商城管理页面
        console.log('9. 测试商城管理页面...');
        try {
            await page.click('a:has-text("商城管理"), a[href*="mall"]');
            await page.waitForTimeout(2000);
            
            const mallPageErrors = await page.locator('.error, .alert-danger, [class*="error"]').count();
            if (mallPageErrors > 0) {
                const errorText = await page.locator('.error, .alert-danger, [class*="error"]').first().textContent();
                console.log('❌ 商城管理页面错误:', errorText);
            } else {
                console.log('✅ 商城管理页面正常');
            }
        } catch (e) {
            console.log('⚠️  商城管理菜单不存在或无法点击');
        }
        
        // 10. 测试AI接口管理页面
        console.log('10. 测试AI接口管理页面...');
        try {
            await page.click('a:has-text("AI接口管理"), a[href*="ai-config"]');
            await page.waitForTimeout(2000);
            
            const aiPageErrors = await page.locator('.error, .alert-danger, [class*="error"]').count();
            if (aiPageErrors > 0) {
                const errorText = await page.locator('.error, .alert-danger, [class*="error"]').first().textContent();
                console.log('❌ AI接口管理页面错误:', errorText);
            } else {
                console.log('✅ AI接口管理页面正常');
            }
        } catch (e) {
            console.log('⚠️  AI接口管理菜单不存在或无法点击');
        }
        
        // 11. 测试数据统计确认页面
        console.log('11. 测试数据统计确认页面...');
        try {
            await page.click('a:has-text("数据统计确认"), a[href*="statistics"]');
            await page.waitForTimeout(2000);
        } catch (e) {
            console.log('⚠️  数据统计确认菜单不存在或无法点击');
        }
        
        // 12. 测试库存数据确认页面
        console.log('12. 测试库存数据确认页面...');
        try {
            await page.click('a:has-text("库存数据确认"), a[href*="inventory"]');
            await page.waitForTimeout(2000);
        } catch (e) {
            console.log('⚠️  库存数据确认菜单不存在或无法点击');
        }
        
        // 13. 测试平台用户页面
        console.log('13. 测试平台用户页面...');
        try {
            await page.click('a:has-text("平台用户"), a[href*="platform-users"]');
            await page.waitForTimeout(2000);
            
            const platformUserPageErrors = await page.locator('.error, .alert-danger, [class*="error"]').count();
            if (platformUserPageErrors > 0) {
                const errorText = await page.locator('.error, .alert-danger, [class*="error"]').first().textContent();
                console.log('❌ 平台用户页面错误:', errorText);
            } else {
                console.log('✅ 平台用户页面正常');
            }
        } catch (e) {
            console.log('⚠️  平台用户菜单不存在或无法点击');
        }
        
        // 14. 测试统计报告页面
        console.log('14. 测试统计报告页面...');
        try {
            await page.click('a:has-text("统计报告"), a[href*="reports"]');
            await page.waitForTimeout(2000);
        } catch (e) {
            console.log('⚠️  统计报告菜单不存在或无法点击');
        }
        
        // 15. 测试系统设置页面
        console.log('15. 测试系统设置页面...');
        try {
            await page.click('a:has-text("系统设置"), a[href*="system"], a[href*="settings"]');
            await page.waitForTimeout(2000);
            
            const systemPageErrors = await page.locator('.error, .alert-danger, [class*="error"]').count();
            if (systemPageErrors > 0) {
                const errorText = await page.locator('.error, .alert-danger, [class*="error"]').first().textContent();
                console.log('❌ 系统设置页面错误:', errorText);
            } else {
                console.log('✅ 系统设置页面正常');
            }
        } catch (e) {
            console.log('⚠️  系统设置菜单不存在或无法点击');
        }
        
        console.log('✅ 页面测试完成');
        
        // 截取最终页面的屏幕截图
        await page.screenshot({ path: 'final-page-test.png', fullPage: true });
        
    } catch (error) {
        console.error('测试过程中出现错误:', error);
    } finally {
        await browser.close();
    }
}

// 运行测试
testAllPages().catch(console.error);