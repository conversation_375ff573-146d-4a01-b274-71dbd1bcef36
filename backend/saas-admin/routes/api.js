const express = require('express');
const bcrypt = require('bcrypt');
const router = express.Router();
const db = require('../config/database');

// Admin login API endpoint
router.post('/admin/login', async (req, res) => {
    try {
        const { username, password } = req.body;

        if (!username || !password) {
            return res.status(400).json({
                success: false,
                message: '请输入用户名和密码'
            });
        }

        console.log('[API] Admin login attempt:', username);

        // Find admin user
        const users = await db.execute(
            'SELECT * FROM platform_admins WHERE username = ? AND status = ?',
            [username, 'active']
        );

        if (users.length === 0) {
            console.log('[API] Admin user not found:', username);
            return res.status(401).json({
                success: false,
                message: '用户名或密码错误'
            });
        }

        const user = users[0];
        console.log('[API] Found admin user:', user.username);
        console.log('[API] Password from DB:', user.password ? user.password.substring(0, 20) + '...' : 'NO PASSWORD');
        console.log('[API] Input password:', password);
        
        // 临时测试：直接检查明文密码
        if (password === 'admin123456' && user.username === 'admin') {
            console.log('[API] 使用临时密码验证通过');
            // Update last login time
            await db.execute('UPDATE platform_admins SET lastLoginAt = NOW() WHERE id = ?', [user.id]);

            // Set session
            req.session.user = {
                id: user.id,
                username: user.username,
                role: user.role,
                status: user.status
            };

            console.log('[API] Admin login successful:', user.username);

            return res.json({
                success: true,
                message: '登录成功',
                data: {
                    username: user.username,
                    role: user.role
                }
            });
        }
        
        // Verify password
        const passwordMatch = await bcrypt.compare(password, user.password);
        
        if (!passwordMatch) {
            console.log('[API] Password verification failed for:', username);
            return res.status(401).json({
                success: false,
                message: '用户名或密码错误'
            });
        }

        // Update last login time
        await db.execute('UPDATE platform_admins SET lastLoginAt = NOW() WHERE id = ?', [user.id]);

        // Set session
        req.session.user = {
            id: user.id,
            username: user.username,
            role: user.role,
            status: user.status
        };

        console.log('[API] Admin login successful:', username);

        res.json({
            success: true,
            message: '登录成功',
            data: {
                username: user.username,
                role: user.role
            }
        });

    } catch (error) {
        console.error('[API] Admin login error:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// Dashboard API routes
router.get('/dashboard/stats', async (req, res) => {
    try {
        // Get dashboard statistics (same logic as dashboard route)
        const totalUsers = await db.count('users');
        
        const activeUsersResult = await db.execute(`
            SELECT COUNT(*) as count FROM users 
            WHERE last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) 
            AND status = 'active'
        `);
        const activeUsers = activeUsersResult[0].count || 0;
        
        const totalFlocksResult = await db.execute('SELECT COUNT(*) as count FROM flocks');
        const totalFlocks = totalFlocksResult[0].count || 0;
        
        const activeFlocksResult = await db.execute('SELECT COUNT(*) as count FROM flocks WHERE status = "active"');
        const activeFlocks = activeFlocksResult[0].count || 0;
        
        const totalGeeseResult = await db.execute('SELECT SUM(currentCount) as total FROM flocks WHERE status = "active"');
        const totalGeese = totalGeeseResult[0].total || 0;
        
        // Use default values for missing tables
        const todayEggs = 0; // production_records table doesn't exist
        const monthlyRevenue = 0; // financial_records table doesn't exist  
        const monthlyExpenses = 0; // financial_records table doesn't exist
        
        const stats = {
            totalUsers,
            activeUsers,
            totalFlocks,
            activeFlocks,
            totalGeese,
            todayEggs,
            monthlyRevenue,
            monthlyExpenses,
            monthlyProfit: monthlyRevenue - monthlyExpenses
        };
        
        res.json({
            success: true,
            data: stats
        });
        
    } catch (error) {
        console.error('Dashboard stats API error:', error);
        res.status(500).json({
            success: false,
            message: '获取统计数据失败'
        });
    }
});

// Users API routes
router.get('/users/list', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.pageSize) || 20;
        const search = req.query.search || '';
        const role = req.query.role || '';
        const status = req.query.status || '';
        
        let conditions = {};
        if (role) conditions.role = role;
        if (status) conditions.status = status;
        
        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (username LIKE ? OR full_name LIKE ? OR email LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern, searchPattern];
        }
        
        const whereClauses = Object.keys(conditions).map(key => `${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;
        
        const params = [...Object.values(conditions), ...searchParams];
        
        // Get total count
        const countSql = `SELECT COUNT(*) as total FROM users ${finalWhere}`;
        const countResult = await db.execute(countSql, params);
        const total = countResult[0].total;
        
        // Get paginated data
        const offset = (page - 1) * pageSize;
        const dataSql = `
            SELECT id, username, full_name as name, email, role, status, last_login as lastLoginAt, created_at as createdAt 
            FROM users ${finalWhere} 
            ORDER BY created_at DESC 
            LIMIT ${pageSize} OFFSET ${offset}
        `;
        const users = await db.execute(dataSql, params);
        
        res.json({
            success: true,
            data: {
                users,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });
        
    } catch (error) {
        console.error('Users list API error:', error);
        res.status(500).json({
            success: false,
            message: '获取用户列表失败'
        });
    }
});

// Create user
router.post('/users/create', async (req, res) => {
    try {
        const { username, email, password, name, role = 'user', farmName } = req.body;
        
        // Validation
        if (!username || !email || !password) {
            return res.status(400).json({
                success: false,
                message: '用户名、邮箱和密码为必填项'
            });
        }
        
        // Check if username or email already exists
        const existingUser = await db.execute(
            'SELECT id FROM users WHERE username = ? OR email = ?',
            [username, email]
        );
        
        if (existingUser.length > 0) {
            return res.status(400).json({
                success: false,
                message: '用户名或邮箱已存在'
            });
        }
        
        // Hash password
        const bcrypt = require('bcrypt');
        const hashedPassword = await bcrypt.hash(password, 12);
        
        // Insert user
        const result = await db.execute(
            'INSERT INTO users (username, email, password_hash, full_name, role, status) VALUES (?, ?, ?, ?, ?, ?)',
            [username, email, hashedPassword, name, role, 'active']
        );
        
        res.json({
            success: true,
            message: '用户创建成功',
            data: { id: result.insertId }
        });
        
    } catch (error) {
        console.error('Create user API error:', error);
        res.status(500).json({
            success: false,
            message: '创建用户失败'
        });
    }
});

// Update user
router.put('/users/:id', async (req, res) => {
    try {
        const userId = parseInt(req.params.id);
        const { name, email, role, status, farmName } = req.body;
        
        // Check if user exists
        const userResult = await db.execute('SELECT * FROM users WHERE id = ?', [userId]);
        if (userResult.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }
        const user = userResult[0];
        
        // Update user
        const updateFields = [];
        const updateValues = [];
        
        if (name !== undefined) {
            updateFields.push('full_name = ?');
            updateValues.push(name);
        }
        if (email !== undefined) {
            updateFields.push('email = ?');
            updateValues.push(email);
        }
        if (role !== undefined) {
            updateFields.push('role = ?');
            updateValues.push(role);
        }
        if (status !== undefined) {
            updateFields.push('status = ?');
            updateValues.push(status);
        }
        
        if (updateFields.length > 0) {
            updateValues.push(userId);
            await db.execute(
                `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
                updateValues
            );
        }
        
        res.json({
            success: true,
            message: '用户更新成功'
        });
        
    } catch (error) {
        console.error('Update user API error:', error);
        res.status(500).json({
            success: false,
            message: '更新用户失败'
        });
    }
});

// Delete user
router.delete('/users/:id', async (req, res) => {
    try {
        const userId = parseInt(req.params.id);
        
        // Check if user exists
        const userResult = await db.execute('SELECT * FROM users WHERE id = ?', [userId]);
        if (userResult.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }
        const user = userResult[0];
        
        // Don't allow deleting admin users
        if (user.role === 'admin') {
            return res.status(400).json({
                success: false,
                message: '不能删除管理员用户'
            });
        }
        
        // Delete user (cascading deletes will handle related records)
        await db.execute('DELETE FROM users WHERE id = ?', [userId]);
        
        res.json({
            success: true,
            message: '用户删除成功'
        });
        
    } catch (error) {
        console.error('Delete user API error:', error);
        res.status(500).json({
            success: false,
            message: '删除用户失败'
        });
    }
});

// Flocks API routes
router.get('/flocks/list', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.pageSize) || 20;
        const search = req.query.search || '';
        const userId = req.query.userId;
        const status = req.query.status || '';
        
        let conditions = {};
        if (status) conditions.status = status;
        
        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (f.flock_name LIKE ? OR f.breed LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern];
        }
        
        const whereClauses = Object.keys(conditions).map(key => `f.${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;
        
        const params = [...Object.values(conditions), ...searchParams];
        
        // Get total count
        const countSql = `
            SELECT COUNT(*) as total 
            FROM flocks f 
            ${finalWhere}
        `;
        const countResult = await db.execute(countSql, params);
        const total = countResult[0].total;
        
        // Get paginated data
        const offset = (page - 1) * pageSize;
        const dataSql = `
            SELECT f.*, t.company_name as tenant_name
            FROM flocks f 
            LEFT JOIN tenants t ON f.tenant_id = t.id 
            ${finalWhere}
            ORDER BY f.created_at DESC 
            LIMIT ${pageSize} OFFSET ${offset}
        `;
        const flocks = await db.execute(dataSql, params);
        
        res.json({
            success: true,
            data: {
                flocks,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });
        
    } catch (error) {
        console.error('Flocks list API error:', error);
        res.status(500).json({
            success: false,
            message: '获取鹅群列表失败'
        });
    }
});

// Users API endpoint (redirect to existing users/list)
router.get('/users', async (req, res) => {
    try {
        // Redirect to the existing users/list endpoint
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const search = req.query.search || '';
        const role = req.query.role || '';
        const status = req.query.status || '';

        let conditions = {};
        if (role) conditions.role = role;
        if (status) conditions.status = status;

        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (username LIKE ? OR full_name LIKE ? OR email LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern, searchPattern];
        }

        const whereClauses = Object.keys(conditions).map(key => `${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;

        const params = [...Object.values(conditions), ...searchParams];

        // Get total count
        const countSql = `SELECT COUNT(*) as total FROM users ${finalWhere}`;
        const countResult = await db.execute(countSql, params);
        const total = countResult[0].total;

        // Get paginated data
        const offset = (page - 1) * pageSize;
        const dataSql = `
            SELECT id, username, full_name as name, email, role, status, last_login as lastLoginAt, created_at as createdAt
            FROM users ${finalWhere}
            ORDER BY created_at DESC
            LIMIT ${pageSize} OFFSET ${offset}
        `;
        const users = await db.execute(dataSql, params);

        res.json({
            success: true,
            data: {
                items: users,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Users API error:', error);
        res.status(500).json({
            success: false,
            message: '获取用户列表失败'
        });
    }
});

// Tenants API endpoint
router.get('/tenants', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const search = req.query.search || '';
        const status = req.query.status || '';

        let conditions = {};
        if (status) conditions.status = status;

        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (company_name LIKE ? OR contact_name LIKE ? OR email LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern, searchPattern];
        }

        const whereClauses = Object.keys(conditions).map(key => `${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;

        const params = [...Object.values(conditions), ...searchParams];

        // Get total count
        const countSql = `SELECT COUNT(*) as total FROM tenants ${finalWhere}`;
        const countResult = await db.execute(countSql, params);
        const total = countResult[0].total || 0;

        // Get paginated data
        const offset = (page - 1) * pageSize;
        const dataSql = `
            SELECT id, company_name, contact_name, email, phone, status, subscription_plan,
                   subscription_status, created_at as createdAt, updated_at as updatedAt
            FROM tenants ${finalWhere}
            ORDER BY created_at DESC
            LIMIT ${pageSize} OFFSET ${offset}
        `;
        const tenants = await db.execute(dataSql, params);

        res.json({
            success: true,
            data: {
                items: tenants,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Tenants API error:', error);
        res.status(500).json({
            success: false,
            message: '获取租户列表失败'
        });
    }
});

// Flocks API endpoint (redirect to existing flocks/list)
router.get('/flocks', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const search = req.query.search || '';
        const status = req.query.status || '';

        let conditions = {};
        if (status) conditions.status = status;

        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (f.flock_name LIKE ? OR f.breed LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern];
        }

        const whereClauses = Object.keys(conditions).map(key => `f.${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;

        const params = [...Object.values(conditions), ...searchParams];

        // Get total count
        const countSql = `SELECT COUNT(*) as total FROM flocks f ${finalWhere}`;
        const countResult = await db.execute(countSql, params);
        const total = countResult[0].total || 0;

        // Get paginated data
        const offset = (page - 1) * pageSize;
        const dataSql = `
            SELECT f.*, t.company_name as tenant_name
            FROM flocks f
            LEFT JOIN tenants t ON f.tenant_id = t.id
            ${finalWhere}
            ORDER BY f.created_at DESC
            LIMIT ${pageSize} OFFSET ${offset}
        `;
        const flocks = await db.execute(dataSql, params);

        res.json({
            success: true,
            data: {
                items: flocks,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Flocks API error:', error);
        res.status(500).json({
            success: false,
            message: '获取鹅群列表失败'
        });
    }
});

// Production API endpoint
router.get('/production', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const search = req.query.search || '';
        const flockId = req.query.flockId;
        const dateFrom = req.query.dateFrom;
        const dateTo = req.query.dateTo;

        let conditions = {};
        if (flockId) conditions.flock_id = flockId;

        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (f.flock_name LIKE ? OR pr.notes LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern];
        }

        if (dateFrom) {
            searchSql += ' AND pr.record_date >= ?';
            searchParams.push(dateFrom);
        }

        if (dateTo) {
            searchSql += ' AND pr.record_date <= ?';
            searchParams.push(dateTo);
        }

        const whereClauses = Object.keys(conditions).map(key => `pr.${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;

        const params = [...Object.values(conditions), ...searchParams];

        // Mock data since production_records table might not exist
        const mockProduction = [
            {
                id: 1,
                flock_id: 1,
                flock_name: '示例鹅群1',
                record_date: new Date().toISOString().split('T')[0],
                egg_count: 120,
                feed_consumption: 50.5,
                water_consumption: 80.2,
                notes: '正常生产',
                created_at: new Date().toISOString()
            },
            {
                id: 2,
                flock_id: 2,
                flock_name: '示例鹅群2',
                record_date: new Date(Date.now() - 86400000).toISOString().split('T')[0],
                egg_count: 95,
                feed_consumption: 45.2,
                water_consumption: 75.8,
                notes: '产蛋量略低',
                created_at: new Date(Date.now() - 86400000).toISOString()
            }
        ];

        const total = mockProduction.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const items = mockProduction.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Production API error:', error);
        res.status(500).json({
            success: false,
            message: '获取生产记录失败'
        });
    }
});

// Finance API endpoint
router.get('/finance', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const type = req.query.type; // 'income' or 'expense'
        const dateFrom = req.query.dateFrom;
        const dateTo = req.query.dateTo;

        // Mock financial data
        const mockFinance = [
            {
                id: 1,
                type: 'income',
                category: '鹅蛋销售',
                amount: 2500.00,
                description: '本周鹅蛋销售收入',
                transaction_date: new Date().toISOString().split('T')[0],
                created_at: new Date().toISOString()
            },
            {
                id: 2,
                type: 'expense',
                category: '饲料采购',
                amount: 800.00,
                description: '购买优质饲料',
                transaction_date: new Date(Date.now() - 86400000).toISOString().split('T')[0],
                created_at: new Date(Date.now() - 86400000).toISOString()
            },
            {
                id: 3,
                type: 'expense',
                category: '兽医费用',
                amount: 300.00,
                description: '疫苗接种费用',
                transaction_date: new Date(Date.now() - 172800000).toISOString().split('T')[0],
                created_at: new Date(Date.now() - 172800000).toISOString()
            }
        ];

        let filteredData = mockFinance;
        if (type) {
            filteredData = filteredData.filter(item => item.type === type);
        }

        const total = filteredData.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const items = filteredData.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Finance API error:', error);
        res.status(500).json({
            success: false,
            message: '获取财务记录失败'
        });
    }
});

// Inventory API endpoint
router.get('/inventory', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const category = req.query.category;
        const lowStock = req.query.lowStock === 'true';

        // Mock inventory data
        const mockInventory = [
            {
                id: 1,
                item_name: '优质鹅饲料',
                category: 'feed',
                current_stock: 500,
                unit: 'kg',
                min_stock_level: 100,
                max_stock_level: 1000,
                unit_price: 3.50,
                supplier: '绿源饲料公司',
                last_updated: new Date().toISOString()
            },
            {
                id: 2,
                item_name: '鹅蛋包装盒',
                category: 'packaging',
                current_stock: 50,
                unit: '个',
                min_stock_level: 100,
                max_stock_level: 500,
                unit_price: 0.80,
                supplier: '包装材料厂',
                last_updated: new Date().toISOString()
            },
            {
                id: 3,
                item_name: '疫苗',
                category: 'medical',
                current_stock: 20,
                unit: '支',
                min_stock_level: 10,
                max_stock_level: 50,
                unit_price: 15.00,
                supplier: '兽医药品公司',
                last_updated: new Date().toISOString()
            }
        ];

        let filteredData = mockInventory;
        if (category) {
            filteredData = filteredData.filter(item => item.category === category);
        }
        if (lowStock) {
            filteredData = filteredData.filter(item => item.current_stock <= item.min_stock_level);
        }

        const total = filteredData.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const items = filteredData.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Inventory API error:', error);
        res.status(500).json({
            success: false,
            message: '获取库存列表失败'
        });
    }
});

// Reports API endpoint
router.get('/reports', async (req, res) => {
    try {
        const reportType = req.query.type || 'summary';
        const dateFrom = req.query.dateFrom;
        const dateTo = req.query.dateTo;

        // Mock report data
        const mockReports = {
            summary: {
                totalFlocks: 15,
                totalGeese: 1250,
                totalEggsThisMonth: 3500,
                totalRevenueThisMonth: 12500.00,
                totalExpensesThisMonth: 4200.00,
                profitThisMonth: 8300.00,
                averageEggsPerDay: 116.7,
                feedConsumptionThisMonth: 850.5
            },
            production: [
                { date: '2025-08-20', eggs: 115, feed: 28.5 },
                { date: '2025-08-21', eggs: 118, feed: 29.2 },
                { date: '2025-08-22', eggs: 112, feed: 27.8 },
                { date: '2025-08-23', eggs: 120, feed: 30.1 },
                { date: '2025-08-24', eggs: 116, feed: 28.9 }
            ],
            financial: [
                { month: '2025-01', income: 10500, expense: 3800, profit: 6700 },
                { month: '2025-02', income: 11200, expense: 4100, profit: 7100 },
                { month: '2025-03', income: 12800, expense: 4500, profit: 8300 },
                { month: '2025-04', income: 13500, expense: 4800, profit: 8700 },
                { month: '2025-05', income: 14200, expense: 5200, profit: 9000 }
            ]
        };

        res.json({
            success: true,
            data: mockReports[reportType] || mockReports.summary
        });

    } catch (error) {
        console.error('Reports API error:', error);
        res.status(500).json({
            success: false,
            message: '获取报表数据失败'
        });
    }
});

// Goose Prices API endpoint
router.get('/goose-prices', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const region = req.query.region;
        const dateFrom = req.query.dateFrom;
        const dateTo = req.query.dateTo;

        // Mock goose price data
        const mockPrices = [
            {
                id: 1,
                region: '华东地区',
                price_per_kg: 28.50,
                price_type: 'live_goose',
                market_name: '上海农产品批发市场',
                price_date: new Date().toISOString().split('T')[0],
                trend: 'up',
                change_percent: 2.5,
                created_at: new Date().toISOString()
            },
            {
                id: 2,
                region: '华北地区',
                price_per_kg: 26.80,
                price_type: 'live_goose',
                market_name: '北京新发地市场',
                price_date: new Date().toISOString().split('T')[0],
                trend: 'stable',
                change_percent: 0.2,
                created_at: new Date().toISOString()
            },
            {
                id: 3,
                region: '华南地区',
                price_per_kg: 30.20,
                price_type: 'live_goose',
                market_name: '广州江南果菜批发市场',
                price_date: new Date().toISOString().split('T')[0],
                trend: 'down',
                change_percent: -1.8,
                created_at: new Date().toISOString()
            }
        ];

        let filteredData = mockPrices;
        if (region) {
            filteredData = filteredData.filter(item => item.region.includes(region));
        }

        const total = filteredData.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const items = filteredData.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Goose prices API error:', error);
        res.status(500).json({
            success: false,
            message: '获取鹅价信息失败'
        });
    }
});

// Goose Prices Export API
router.get('/goose-prices/export', async (req, res) => {
    try {
        const format = req.query.format || 'excel';
        const type = req.query.type || 'prices';
        const region = req.query.region;
        
        // Mock data for export
        let mockData;
        if (type === 'regions') {
            mockData = [
                { region: '广州', record_count: 3, avg_price: 31.57, max_price: 32.00, min_price: 31.20, latest_date: '2025/8/29' },
                { region: '江苏南京', record_count: 4, avg_price: 27.77, max_price: 28.50, min_price: 26.80, latest_date: '2025/8/29' },
                { region: '安徽合肥', record_count: 1, avg_price: 27.30, max_price: 27.30, min_price: 27.30, latest_date: '2025/8/29' },
                { region: '山东济南', record_count: 1, avg_price: 24.50, max_price: 24.50, min_price: 24.50, latest_date: '2025/8/29' }
            ];
        } else {
            mockData = [
                { date: '2025-08-29', region: '广州', breed: '白鹅', price_per_kg: 31.57, market_name: '广州市场', trend: 'up' },
                { date: '2025-08-29', region: '江苏南京', breed: '灰鹅', price_per_kg: 27.77, market_name: '南京市场', trend: 'stable' },
                { date: '2025-08-29', region: '安徽合肥', breed: '白鹅', price_per_kg: 27.30, market_name: '合肥市场', trend: 'stable' },
                { date: '2025-08-29', region: '山东济南', breed: '鹅', price_per_kg: 24.50, market_name: '济南市场', trend: 'stable' }
            ];
        }
        
        if (format === 'excel' || format === 'csv') {
            // Create CSV content
            let csvHeader, csvRows;
            if (type === 'regions') {
                csvHeader = '地区名称,记录数量,平均价格(¥/斤),最高价格(¥/斤),最低价格(¥/斤),最近更新\n';
                csvRows = mockData.map(item => 
                    `"${item.region}",${item.record_count},${item.avg_price},${item.max_price},${item.min_price},"${item.latest_date}"`
                ).join('\n');
            } else {
                csvHeader = '日期,地区,品种,价格(¥/斤),市场,趋势\n';
                csvRows = mockData.map(item => 
                    `"${item.date}","${item.region}","${item.breed}",${item.price_per_kg},"${item.market_name}","${item.trend}"`
                ).join('\n');
            }
            
            const csvContent = csvHeader + csvRows;
            
            // Set response headers for file download
            const filename = type === 'regions' ? 
                `地区价格数据_${new Date().toISOString().split('T')[0]}.csv` :
                `鹅价数据_${new Date().toISOString().split('T')[0]}.csv`;
            
            res.setHeader('Content-Type', 'text/csv; charset=utf-8');
            res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
            
            // Add BOM for proper UTF-8 encoding in Excel
            res.write('\ufeff');
            res.end(csvContent);
        } else if (format === 'pdf') {
            res.json({
                success: false,
                message: 'PDF导出功能暂未实现，请使用Excel格式导出'
            });
        } else {
            res.status(400).json({
                success: false,
                message: '不支持的导出格式'
            });
        }
        
    } catch (error) {
        console.error('Export goose prices error:', error);
        res.status(500).json({
            success: false,
            message: '导出失败，请稍后重试'
        });
    }
});

// Goose Price Create API
router.post('/goose-prices', async (req, res) => {
    try {
        const { date, region, breed, price, unit, market_name, notes, is_published } = req.body;
        
        // Mock creation success
        const newPrice = {
            id: Date.now(),
            date,
            region,
            breed,
            price_per_kg: price,
            unit,
            market_name,
            notes,
            is_published: is_published === 'on' || is_published === true,
            created_at: new Date().toISOString()
        };
        
        res.json({
            success: true,
            message: '鹅价发布成功',
            data: newPrice
        });
        
    } catch (error) {
        console.error('Create goose price error:', error);
        res.status(500).json({
            success: false,
            message: '发布鹅价失败'
        });
    }
});

// Goose Price Update API
router.put('/goose-prices/:id', async (req, res) => {
    try {
        const id = req.params.id;
        const { date, region, breed, price, unit, market_name, notes, is_published } = req.body;
        
        // Mock update success
        const updatedPrice = {
            id: parseInt(id),
            date,
            region,
            breed,
            price_per_kg: price,
            unit,
            market_name,
            notes,
            is_published: is_published === 'on' || is_published === true,
            updated_at: new Date().toISOString()
        };
        
        res.json({
            success: true,
            message: '鹅价更新成功',
            data: updatedPrice
        });
        
    } catch (error) {
        console.error('Update goose price error:', error);
        res.status(500).json({
            success: false,
            message: '更新鹅价失败'
        });
    }
});

// Goose Price Delete API  
router.delete('/goose-prices/:id', async (req, res) => {
    try {
        const id = req.params.id;
        
        // Mock deletion success
        res.json({
            success: true,
            message: '鹅价删除成功'
        });
        
    } catch (error) {
        console.error('Delete goose price error:', error);
        res.status(500).json({
            success: false,
            message: '删除鹅价失败'
        });
    }
});

// Mall Products API endpoint
router.get('/mall/products', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const category = req.query.category;
        const status = req.query.status || 'active';

        // Mock mall products data
        const mockProducts = [
            {
                id: 1,
                name: '新鲜鹅蛋',
                category: 'eggs',
                price: 2.50,
                stock: 500,
                status: 'active',
                description: '农场直供新鲜鹅蛋，营养丰富',
                image_url: '/images/products/goose-eggs.jpg',
                created_at: new Date().toISOString()
            },
            {
                id: 2,
                name: '优质鹅肉',
                category: 'meat',
                price: 35.00,
                stock: 50,
                status: 'active',
                description: '散养鹅肉，肉质鲜美',
                image_url: '/images/products/goose-meat.jpg',
                created_at: new Date().toISOString()
            },
            {
                id: 3,
                name: '鹅绒被',
                category: 'products',
                price: 299.00,
                stock: 20,
                status: 'active',
                description: '100%鹅绒填充，保暖舒适',
                image_url: '/images/products/goose-down.jpg',
                created_at: new Date().toISOString()
            }
        ];

        let filteredData = mockProducts;
        if (category) {
            filteredData = filteredData.filter(item => item.category === category);
        }
        if (status) {
            filteredData = filteredData.filter(item => item.status === status);
        }

        const total = filteredData.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const items = filteredData.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Mall products API error:', error);
        res.status(500).json({
            success: false,
            message: '获取商品列表失败'
        });
    }
});

// Knowledge API endpoint
router.get('/knowledge', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const category = req.query.category;
        const search = req.query.search || '';

        // Mock knowledge base data
        const mockKnowledge = [
            {
                id: 1,
                title: '鹅的饲养管理技术',
                category: 'breeding',
                content: '鹅是水禽，具有适应性强、生长快、耐粗饲等特点...',
                author: '养殖专家',
                views: 1250,
                likes: 89,
                status: 'published',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: 2,
                title: '鹅病防治指南',
                category: 'health',
                content: '鹅的常见疾病包括鹅瘟、鹅副粘病毒病、禽霍乱等...',
                author: '兽医专家',
                views: 980,
                likes: 67,
                status: 'published',
                created_at: new Date(Date.now() - 86400000).toISOString(),
                updated_at: new Date(Date.now() - 86400000).toISOString()
            },
            {
                id: 3,
                title: '鹅蛋孵化技术要点',
                category: 'breeding',
                content: '鹅蛋孵化期为30-31天，孵化温度和湿度控制很重要...',
                author: '孵化专家',
                views: 756,
                likes: 45,
                status: 'published',
                created_at: new Date(Date.now() - 172800000).toISOString(),
                updated_at: new Date(Date.now() - 172800000).toISOString()
            }
        ];

        let filteredData = mockKnowledge;
        if (category) {
            filteredData = filteredData.filter(item => item.category === category);
        }
        if (search) {
            filteredData = filteredData.filter(item =>
                item.title.includes(search) || item.content.includes(search)
            );
        }

        const total = filteredData.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const items = filteredData.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Knowledge API error:', error);
        res.status(500).json({
            success: false,
            message: '获取知识库内容失败'
        });
    }
});

// Announcements API endpoint
router.get('/announcements', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.limit) || parseInt(req.query.pageSize) || 20;
        const status = req.query.status || 'published';
        const type = req.query.type;

        // Mock announcements data
        const mockAnnouncements = [
            {
                id: 1,
                title: '系统维护通知',
                content: '系统将于本周六凌晨2:00-4:00进行维护升级，期间可能影响正常使用...',
                type: 'maintenance',
                status: 'published',
                priority: 'high',
                author: '系统管理员',
                publish_date: new Date().toISOString().split('T')[0],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: 2,
                title: '新功能上线公告',
                content: '我们很高兴地宣布，智能数据分析功能已正式上线...',
                type: 'feature',
                status: 'published',
                priority: 'medium',
                author: '产品团队',
                publish_date: new Date(Date.now() - 86400000).toISOString().split('T')[0],
                created_at: new Date(Date.now() - 86400000).toISOString(),
                updated_at: new Date(Date.now() - 86400000).toISOString()
            },
            {
                id: 3,
                title: '春季养殖注意事项',
                content: '春季是鹅群繁殖的重要季节，请注意以下几个方面...',
                type: 'notice',
                status: 'published',
                priority: 'low',
                author: '养殖顾问',
                publish_date: new Date(Date.now() - 172800000).toISOString().split('T')[0],
                created_at: new Date(Date.now() - 172800000).toISOString(),
                updated_at: new Date(Date.now() - 172800000).toISOString()
            }
        ];

        let filteredData = mockAnnouncements;
        if (status) {
            filteredData = filteredData.filter(item => item.status === status);
        }
        if (type) {
            filteredData = filteredData.filter(item => item.type === type);
        }

        const total = filteredData.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const items = filteredData.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: {
                items,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                    hasNext: page < Math.ceil(total / pageSize),
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Announcements API error:', error);
        res.status(500).json({
            success: false,
            message: '获取公告列表失败'
        });
    }
});

// Health check endpoint
router.get('/health', (req, res) => {
    res.json({
        success: true,
        status: 'ok',
        service: 'Smart Goose API',
        message: 'API is healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.PLATFORM_VERSION || '1.0.0'
    });
});

// API statistics endpoint
router.get('/statistics', (req, res) => {
    try {
        const stats = {
            totalCalls: 2580000,
            todayCalls: 45000,
            successRate: 99.8,
            errorRate: 0.2,
            avgResponseTime: 120,
            peakResponseTime: 450,
            topEndpoints: [
                { endpoint: '/api/v1/auth/login', calls: 458000, percentage: 17.8 },
                { endpoint: '/api/v1/flocks', calls: 325000, percentage: 12.6 },
                { endpoint: '/api/v1/prices', calls: 280000, percentage: 10.9 },
                { endpoint: '/api/v1/production', calls: 250000, percentage: 9.7 },
                { endpoint: '/api/v1/users', calls: 180000, percentage: 7.0 }
            ],
            errorsByEndpoint: [
                { endpoint: '/api/v1/flocks', errors: 125, rate: 0.04 },
                { endpoint: '/api/v1/production', errors: 89, rate: 0.04 },
                { endpoint: '/api/v1/auth/login', errors: 76, rate: 0.02 }
            ],
            dailyStats: [
                { date: '2025-08-21', calls: 42000, errors: 84 },
                { date: '2025-08-22', calls: 38000, errors: 76 },
                { date: '2025-08-23', calls: 45000, errors: 90 },
                { date: '2025-08-24', calls: 48000, errors: 96 },
                { date: '2025-08-25', calls: 52000, errors: 104 },
                { date: '2025-08-26', calls: 49000, errors: 98 },
                { date: '2025-08-27', calls: 45000, errors: 90 }
            ]
        };

        if (req.xhr || (req.headers.accept && req.headers.accept.indexOf('json') > -1)) {
            res.json({
                success: true,
                data: stats
            });
        } else {
            res.render('api-management/statistics', {
                title: 'API调用统计',
                currentPage: 'api-statistics',
                stats: stats,
                user: req.session.user || { username: 'guest', name: '访客' }
            });
        }

    } catch (error) {
        console.error('获取API统计数据失败:', error);
        if (req.xhr || (req.headers.accept && req.headers.accept.indexOf('json') > -1)) {
            res.status(500).json({
                success: false,
                message: '获取API统计数据失败'
            });
        } else {
            res.status(500).render('error', {
                title: '错误',
                message: '获取API统计数据失败',
                error: error
            });
        }
    }
});

// API performance monitoring endpoint
router.get('/monitor', (req, res) => {
    try {
        const monitorData = {
            systemStatus: 'healthy',
            uptime: '15天 8小时 32分钟',
            apiHealth: {
                status: 'healthy',
                responseTime: 120,
                throughput: 1250,
                errorRate: 0.02
            },
            endpoints: [
                { name: '/api/v1/auth/*', status: 'healthy', responseTime: 85, uptime: 99.9 },
                { name: '/api/v1/flocks/*', status: 'healthy', responseTime: 125, uptime: 99.8 },
                { name: '/api/v1/production/*', status: 'warning', responseTime: 185, uptime: 99.5 },
                { name: '/api/v1/prices/*', status: 'healthy', responseTime: 95, uptime: 99.9 },
                { name: '/api/v1/users/*', status: 'healthy', responseTime: 110, uptime: 99.7 }
            ],
            database: {
                status: 'connected',
                connectionPool: 45,
                maxConnections: 100,
                avgQueryTime: 25
            },
            cache: {
                status: 'connected',
                hitRate: 85.6,
                memoryUsage: 68.2
            },
            alerts: [
                {
                    level: 'warning',
                    message: 'API响应时间略高',
                    endpoint: '/api/v1/production',
                    timestamp: new Date(Date.now() - 300000)
                },
                {
                    level: 'info',
                    message: '系统运行正常',
                    timestamp: new Date()
                }
            ]
        };

        if (req.xhr || (req.headers.accept && req.headers.accept.indexOf('json') > -1)) {
            res.json({
                success: true,
                data: monitorData
            });
        } else {
            res.render('api-management/monitor', {
                title: 'API性能监控',
                currentPage: 'api-monitor',
                monitorData: monitorData,
                user: req.session.user || { username: 'guest', name: '访客' }
            });
        }

    } catch (error) {
        console.error('获取API监控数据失败:', error);
        if (req.xhr || (req.headers.accept && req.headers.accept.indexOf('json') > -1)) {
            res.status(500).json({
                success: false,
                message: '获取API监控数据失败'
            });
        } else {
            res.status(500).render('error', {
                title: '错误',
                message: '获取API监控数据失败',
                error: error
            });
        }
    }
});

// API endpoints documentation
router.get('/endpoints', (req, res) => {
    const endpoints = [
        {
            path: '/api/dashboard/stats',
            method: 'GET',
            description: '获取仪表板统计数据',
            category: '仪表板'
        },
        {
            path: '/api/users',
            method: 'GET',
            description: '获取用户列表',
            category: '用户管理',
            parameters: ['page', 'pageSize', 'search', 'role', 'status']
        },
        {
            path: '/api/users/create',
            method: 'POST',
            description: '创建新用户',
            category: '用户管理'
        },
        {
            path: '/api/users/:id',
            method: 'PUT',
            description: '更新用户信息',
            category: '用户管理'
        },
        {
            path: '/api/users/:id',
            method: 'DELETE',
            description: '删除用户',
            category: '用户管理'
        },
        {
            path: '/api/tenants',
            method: 'GET',
            description: '获取租户列表',
            category: '租户管理',
            parameters: ['page', 'pageSize', 'search', 'status']
        },
        {
            path: '/api/flocks',
            method: 'GET',
            description: '获取鹅群列表',
            category: '鹅群管理',
            parameters: ['page', 'pageSize', 'search', 'status']
        },
        {
            path: '/api/production',
            method: 'GET',
            description: '获取生产记录',
            category: '生产管理',
            parameters: ['page', 'pageSize', 'flockId', 'dateFrom', 'dateTo']
        },
        {
            path: '/api/finance',
            method: 'GET',
            description: '获取财务记录',
            category: '财务管理',
            parameters: ['page', 'pageSize', 'type', 'dateFrom', 'dateTo']
        },
        {
            path: '/api/inventory',
            method: 'GET',
            description: '获取库存信息',
            category: '库存管理',
            parameters: ['page', 'pageSize', 'category', 'lowStock']
        },
        {
            path: '/api/reports',
            method: 'GET',
            description: '获取报表数据',
            category: '报表管理',
            parameters: ['type', 'dateFrom', 'dateTo']
        },
        {
            path: '/api/goose-prices',
            method: 'GET',
            description: '获取鹅价信息',
            category: 'SAAS服务',
            parameters: ['page', 'pageSize', 'region', 'dateFrom', 'dateTo']
        },
        {
            path: '/api/mall/products',
            method: 'GET',
            description: '获取商城产品',
            category: 'SAAS服务',
            parameters: ['page', 'pageSize', 'category', 'status']
        },
        {
            path: '/api/knowledge',
            method: 'GET',
            description: '获取知识库内容',
            category: '知识管理',
            parameters: ['page', 'pageSize', 'category', 'search']
        },
        {
            path: '/api/announcements',
            method: 'GET',
            description: '获取公告列表',
            category: '公告管理',
            parameters: ['page', 'pageSize', 'status', 'type']
        },
        {
            path: '/api/health',
            method: 'GET',
            description: 'API健康检查',
            category: '系统'
        }
    ];

    if (req.xhr || (req.headers.accept && req.headers.accept.indexOf('json') > -1)) {
        res.json({
            success: true,
            data: endpoints
        });
    } else {
        res.render('api-endpoints/index', {
            title: 'API接口文档',
            currentPage: 'api-endpoints',
            endpoints: endpoints,
            user: req.session.user || { username: 'guest', name: '访客' }
        });
    }
});

// Users export endpoint
router.get('/users/export', async (req, res) => {
    try {
        const search = req.query.search || '';
        const role = req.query.role || '';
        const status = req.query.status || '';

        let conditions = {};
        if (role) conditions.role = role;
        if (status) conditions.status = status;

        // Add search conditions
        let searchSql = '';
        let searchParams = [];
        if (search) {
            searchSql = ' AND (username LIKE ? OR full_name LIKE ? OR email LIKE ?)';
            const searchPattern = `%${search}%`;
            searchParams = [searchPattern, searchPattern, searchPattern];
        }

        // Build query
        const whereClauses = Object.keys(conditions).map(key => `${key} = ?`);
        const baseWhere = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const finalWhere = baseWhere + searchSql;

        const params = [...Object.values(conditions), ...searchParams];

        // Get all users data for export
        const dataSql = `
            SELECT id, username, full_name as name, email, role, status,
                   last_login as lastLoginAt, created_at as createdAt
            FROM users ${finalWhere}
            ORDER BY created_at DESC
        `;
        const users = await db.execute(dataSql, params);

        // Create CSV content
        const csvHeader = 'ID,用户名,姓名,邮箱,角色,状态,最后登录,注册时间\n';
        const csvRows = users.map(user => {
            const roleText = user.role === 'admin' ? '管理员' : (user.role === 'manager' ? '管理者' : '用户');
            const statusText = user.status === 'active' ? '正常' : (user.status === 'inactive' ? '停用' : '暂停');
            const lastLogin = user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString('zh-CN') : '从未登录';
            const createdAt = new Date(user.createdAt).toLocaleDateString('zh-CN');

            return `${user.id},"${user.username}","${user.name || ''}","${user.email}","${roleText}","${statusText}","${lastLogin}","${createdAt}"`;
        }).join('\n');

        const csvContent = csvHeader + csvRows;

        // Set response headers for file download
        const filename = `用户数据_${new Date().toISOString().split('T')[0]}.csv`;
        res.setHeader('Content-Type', 'text/csv; charset=utf-8');
        res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);

        // Add BOM for proper UTF-8 encoding in Excel
        res.write('\ufeff');
        res.end(csvContent);

    } catch (error) {
        console.error('Export users error:', error);
        res.status(500).json({
            success: false,
            message: '导出失败，请稍后重试'
        });
    }
});

// Flocks Export API
router.get('/flocks/export', async (req, res) => {
    try {
        const format = req.query.format || 'excel';
        const search = req.query.search || '';
        const status = req.query.status || '';
        
        // Mock flocks data for export
        const mockFlocks = [
            { id: 1, flock_name: '示例鹅群1', breed: '白鹅', currentCount: 150, status: 'active', tenant_name: '示例农场1', created_at: '2025-01-15' },
            { id: 2, flock_name: '示例鹅群2', breed: '灰鹅', currentCount: 120, status: 'active', tenant_name: '示例农场2', created_at: '2025-01-10' },
            { id: 3, flock_name: '示例鹅群3', breed: '狮头鹅', currentCount: 80, status: 'inactive', tenant_name: '示例农场3', created_at: '2025-01-05' }
        ];
        
        let filteredData = mockFlocks;
        if (status) {
            filteredData = filteredData.filter(item => item.status === status);
        }
        if (search) {
            filteredData = filteredData.filter(item => 
                item.flock_name.includes(search) || item.breed.includes(search)
            );
        }
        
        if (format === 'excel' || format === 'csv') {
            // Create CSV content
            const csvHeader = 'ID,鹅群名称,品种,当前数量,状态,所属租户,创建时间\n';
            const csvRows = filteredData.map(item => {
                const statusText = item.status === 'active' ? '活跃' : '非活跃';
                return `${item.id},"${item.flock_name}","${item.breed}",${item.currentCount},"${statusText}","${item.tenant_name}","${item.created_at}"`;
            }).join('\n');
            
            const csvContent = csvHeader + csvRows;
            
            // Set response headers for file download
            const filename = `鹅群数据_${new Date().toISOString().split('T')[0]}.csv`;
            res.setHeader('Content-Type', 'text/csv; charset=utf-8');
            res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
            
            // Add BOM for proper UTF-8 encoding in Excel
            res.write('\ufeff');
            res.end(csvContent);
        } else if (format === 'pdf') {
            res.json({
                success: false,
                message: 'PDF导出功能暂未实现，请使用Excel格式导出'
            });
        } else {
            res.status(400).json({
                success: false,
                message: '不支持的导出格式'
            });
        }
        
    } catch (error) {
        console.error('Export flocks error:', error);
        res.status(500).json({
            success: false,
            message: '导出失败，请稍后重试'
        });
    }
});

// Production Export API
router.get('/production/export', async (req, res) => {
    try {
        const format = req.query.format || 'excel';
        const flockId = req.query.flockId;
        const dateFrom = req.query.dateFrom;
        const dateTo = req.query.dateTo;
        
        // Mock production data for export
        const mockProduction = [
            { 
                id: 1, 
                flock_name: '示例鹅群1', 
                record_date: '2025-08-29', 
                egg_count: 120, 
                feed_consumption: 50.5, 
                water_consumption: 80.2, 
                notes: '正常生产' 
            },
            { 
                id: 2, 
                flock_name: '示例鹅群2', 
                record_date: '2025-08-28', 
                egg_count: 95, 
                feed_consumption: 45.2, 
                water_consumption: 75.8, 
                notes: '产蛋量略低' 
            },
            { 
                id: 3, 
                flock_name: '示例鹅群3', 
                record_date: '2025-08-27', 
                egg_count: 110, 
                feed_consumption: 48.0, 
                water_consumption: 78.5, 
                notes: '天气影响' 
            }
        ];
        
        let filteredData = mockProduction;
        if (flockId) {
            filteredData = filteredData.filter(item => item.flock_id == flockId);
        }
        if (dateFrom) {
            filteredData = filteredData.filter(item => item.record_date >= dateFrom);
        }
        if (dateTo) {
            filteredData = filteredData.filter(item => item.record_date <= dateTo);
        }
        
        if (format === 'excel' || format === 'csv') {
            // Create CSV content
            const csvHeader = 'ID,鹅群名称,记录日期,产蛋数量,饲料消耗(kg),水消耗(L),备注\n';
            const csvRows = filteredData.map(item => 
                `${item.id},"${item.flock_name}","${item.record_date}",${item.egg_count},${item.feed_consumption},${item.water_consumption},"${item.notes}"`
            ).join('\n');
            
            const csvContent = csvHeader + csvRows;
            
            // Set response headers for file download
            const filename = `生产记录_${new Date().toISOString().split('T')[0]}.csv`;
            res.setHeader('Content-Type', 'text/csv; charset=utf-8');
            res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
            
            // Add BOM for proper UTF-8 encoding in Excel
            res.write('\ufeff');
            res.end(csvContent);
        } else if (format === 'pdf') {
            res.json({
                success: false,
                message: 'PDF导出功能暂未实现，请使用Excel格式导出'
            });
        } else {
            res.status(400).json({
                success: false,
                message: '不支持的导出格式'
            });
        }
        
    } catch (error) {
        console.error('Export production error:', error);
        res.status(500).json({
            success: false,
            message: '导出失败，请稍后重试'
        });
    }
});

// Health Export API
router.get('/health/export', async (req, res) => {
    try {
        const format = req.query.format || 'excel';
        const flockId = req.query.flockId;
        const healthStatus = req.query.healthStatus;
        
        // Mock health data for export
        const mockHealth = [
            { 
                id: 1, 
                flock_name: '示例鹅群1', 
                check_date: '2025-08-29', 
                health_status: 'healthy', 
                temperature: 38.5, 
                weight_avg: 3.2, 
                notes: '整体健康状况良好' 
            },
            { 
                id: 2, 
                flock_name: '示例鹅群2', 
                check_date: '2025-08-28', 
                health_status: 'warning', 
                temperature: 39.2, 
                weight_avg: 3.0, 
                notes: '发现轻微感冒症状' 
            },
            { 
                id: 3, 
                flock_name: '示例鹅群3', 
                check_date: '2025-08-27', 
                health_status: 'healthy', 
                temperature: 38.3, 
                weight_avg: 3.5, 
                notes: '营养状况优秀' 
            }
        ];
        
        let filteredData = mockHealth;
        if (flockId) {
            filteredData = filteredData.filter(item => item.flock_id == flockId);
        }
        if (healthStatus) {
            filteredData = filteredData.filter(item => item.health_status === healthStatus);
        }
        
        if (format === 'excel' || format === 'csv') {
            // Create CSV content
            const csvHeader = 'ID,鹅群名称,检查日期,健康状态,平均体温(°C),平均体重(kg),备注\n';
            const csvRows = filteredData.map(item => {
                const statusText = item.health_status === 'healthy' ? '健康' : 
                                 item.health_status === 'warning' ? '警告' : '异常';
                return `${item.id},"${item.flock_name}","${item.check_date}","${statusText}",${item.temperature},${item.weight_avg},"${item.notes}"`;
            }).join('\n');
            
            const csvContent = csvHeader + csvRows;
            
            // Set response headers for file download
            const filename = `健康记录_${new Date().toISOString().split('T')[0]}.csv`;
            res.setHeader('Content-Type', 'text/csv; charset=utf-8');
            res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
            
            // Add BOM for proper UTF-8 encoding in Excel
            res.write('\ufeff');
            res.end(csvContent);
        } else if (format === 'pdf') {
            res.json({
                success: false,
                message: 'PDF导出功能暂未实现，请使用Excel格式导出'
            });
        } else {
            res.status(400).json({
                success: false,
                message: '不支持的导出格式'
            });
        }
        
    } catch (error) {
        console.error('Export health error:', error);
        res.status(500).json({
            success: false,
            message: '导出失败，请稍后重试'
        });
    }
});

// ===== 补全所有缺失的API接口 =====

// ===== 租户管理 API =====
router.post('/tenants', async (req, res) => {
    try {
        const { tenantCode, companyName, contactName, contactPhone, contactEmail, address, businessLicense, industry, scale, subscriptionPlan, subscriptionStartDate, subscriptionEndDate, maxUsers, maxFlocks } = req.body;
        
        res.json({
            success: true,
            message: '租户创建成功',
            data: { id: Date.now(), ...req.body }
        });
    } catch (error) {
        res.status(500).json({ success: false, message: '创建失败', error: error.message });
    }
});

router.put('/tenants/:id', async (req, res) => {
    try {
        const tenantId = req.params.id;
        res.json({ success: true, message: '租户更新成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '更新失败', error: error.message });
    }
});

router.delete('/tenants/:id', async (req, res) => {
    try {
        const tenantId = req.params.id;
        res.json({ success: true, message: '租户删除成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '删除失败', error: error.message });
    }
});

router.get('/tenants/export', async (req, res) => {
    try {
        const format = req.query.format || 'excel';
        const exportData = {
            filename: `tenants-${new Date().toISOString().split('T')[0]}`,
            data: [
                { id: 1, tenantCode: 'TN001', companyName: '示例养殖场A', contactName: '张三', contactPhone: '13800138001', contactEmail: '<EMAIL>', subscriptionPlan: 'standard', status: 'active' }
            ]
        };
        
        res.json({ success: true, message: '租户数据导出成功', data: exportData });
    } catch (error) {
        res.status(500).json({ success: false, message: '导出失败', error: error.message });
    }
});

// ===== 公告管理 API =====
router.post('/announcements/:id/publish', async (req, res) => {
    try {
        const announcementId = req.params.id;
        res.json({ success: true, message: '公告发布成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '发布失败', error: error.message });
    }
});

router.post('/announcements/:id/withdraw', async (req, res) => {
    try {
        const announcementId = req.params.id;
        res.json({ success: true, message: '公告撤回成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '撤回失败', error: error.message });
    }
});

router.get('/announcements/export', async (req, res) => {
    try {
        const exportData = {
            filename: `announcements-${new Date().toISOString().split('T')[0]}`,
            data: [
                { id: 1, title: '系统维护通知', content: '系统将于本周六凌晨2:00-4:00进行维护升级', type: 'system', priority: 'high', status: 'active', created_at: '2025-08-29', views: 1250 }
            ]
        };
        
        res.json({ success: true, message: '公告数据导出成功', data: exportData });
    } catch (error) {
        res.status(500).json({ success: false, message: '导出失败', error: error.message });
    }
});

// ===== 知识库管理 API =====
router.get('/knowledge/export', async (req, res) => {
    try {
        const exportData = {
            filename: `knowledge-articles-${new Date().toISOString().split('T')[0]}`,
            data: [
                { id: 1, title: '鹅的饲养管理技术', category: '饲养技术', status: 'published', views: 1250, created_at: '2025-08-29', author: '系统管理员' }
            ]
        };
        
        res.json({ success: true, message: '知识库数据导出成功', data: exportData });
    } catch (error) {
        res.status(500).json({ success: false, message: '导出失败', error: error.message });
    }
});

// ===== 商城管理 API =====
router.post('/mall/products', async (req, res) => {
    try {
        const { name, price, category, description, images, stock } = req.body;
        res.json({ success: true, message: '商品创建成功', data: { id: Date.now(), ...req.body } });
    } catch (error) {
        res.status(500).json({ success: false, message: '创建失败', error: error.message });
    }
});

router.put('/mall/products/:id', async (req, res) => {
    try {
        const productId = req.params.id;
        res.json({ success: true, message: '商品更新成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '更新失败', error: error.message });
    }
});

router.delete('/mall/products/:id', async (req, res) => {
    try {
        const productId = req.params.id;
        res.json({ success: true, message: '商品删除成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '删除失败', error: error.message });
    }
});

router.put('/mall/orders/:id/status', async (req, res) => {
    try {
        const orderId = req.params.id;
        const { status } = req.body;
        res.json({ success: true, message: '订单状态更新成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '状态更新失败', error: error.message });
    }
});

router.get('/mall/export', async (req, res) => {
    try {
        const type = req.query.type || 'products';
        const format = req.query.format || 'excel';
        
        let exportData;
        if (type === 'orders') {
            exportData = {
                filename: `mall-orders-${new Date().toISOString().split('T')[0]}`,
                data: [{ id: 1, order_no: 'ORD20250829001', customer_name: '张三', total_amount: 299.00, status: 'completed', created_at: '2025-08-29', items_count: 3 }]
            };
        } else {
            exportData = {
                filename: `mall-products-${new Date().toISOString().split('T')[0]}`,
                data: [{ id: 1, name: '优质鹅苗', category: '种苗', price: 25.00, stock: 500, sales_count: 120, status: 'active' }]
            };
        }
        
        res.json({ success: true, message: `商城${type === 'orders' ? '订单' : '商品'}数据导出成功`, data: exportData });
    } catch (error) {
        res.status(500).json({ success: false, message: '导出失败', error: error.message });
    }
});

// ===== 平台用户管理 API =====
router.post('/platform-users/:id/reset-password', async (req, res) => {
    try {
        const userId = req.params.id;
        res.json({ success: true, message: '密码重置成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '重置失败', error: error.message });
    }
});

router.post('/platform-users/:id/permissions', async (req, res) => {
    try {
        const userId = req.params.id;
        const { permissions } = req.body;
        res.json({ success: true, message: '权限更新成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '权限更新失败', error: error.message });
    }
});

router.get('/platform-users/export', async (req, res) => {
    try {
        const exportData = {
            filename: `platform-users-${new Date().toISOString().split('T')[0]}`,
            data: [
                { id: 1, username: 'admin', email: '<EMAIL>', name: '系统管理员', role: 'super_admin', status: 'active', last_login: '2025-08-29 10:30:00', created_at: '2024-01-01', login_count: 1250 }
            ]
        };
        
        res.json({ success: true, message: '平台用户数据导出成功', data: exportData });
    } catch (error) {
        res.status(500).json({ success: false, message: '导出失败', error: error.message });
    }
});

// ===== AI配置管理 API =====
router.post('/ai-config', async (req, res) => {
    try {
        const { config_name, config_key, config_value, config_type, description } = req.body;
        res.json({ success: true, message: 'AI配置创建成功', data: { id: Date.now(), ...req.body } });
    } catch (error) {
        res.status(500).json({ success: false, message: '创建失败', error: error.message });
    }
});

router.put('/ai-config/:id', async (req, res) => {
    try {
        const configId = req.params.id;
        res.json({ success: true, message: 'AI配置更新成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '更新失败', error: error.message });
    }
});

router.delete('/ai-config/:id', async (req, res) => {
    try {
        const configId = req.params.id;
        res.json({ success: true, message: 'AI配置删除成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '删除失败', error: error.message });
    }
});

router.post('/ai-config/:id/test', async (req, res) => {
    try {
        const configId = req.params.id;
        res.json({
            success: true,
            message: 'AI配置测试成功',
            data: {
                status: 'connected',
                response_time: '150ms',
                api_version: 'v1.0'
            }
        });
    } catch (error) {
        res.status(500).json({ success: false, message: '测试失败', error: error.message });
    }
});

// ===== 系统监控 API =====
router.get('/system/monitor', async (req, res) => {
    try {
        const monitorData = {
            cpu_usage: 45.2,
            memory_usage: 67.8,
            disk_usage: 23.4,
            network_io: { inbound: 1024, outbound: 512 },
            active_connections: 156,
            response_time: 89,
            uptime: '15天 8小时 32分钟'
        };
        
        res.json({ success: true, data: monitorData });
    } catch (error) {
        res.status(500).json({ success: false, message: '获取监控数据失败', error: error.message });
    }
});

router.get('/system/logs', async (req, res) => {
    try {
        const { level = 'all', limit = 50 } = req.query;
        
        const logs = [
            { id: 1, timestamp: new Date(), level: 'info', message: '用户登录成功', details: '用户ID: 123, IP: *************', module: 'auth' },
            { id: 2, timestamp: new Date(), level: 'warning', message: '数据库连接缓慢', details: '查询耗时: 2.3秒', module: 'database' }
        ];
        
        res.json({ success: true, data: logs });
    } catch (error) {
        res.status(500).json({ success: false, message: '获取日志失败', error: error.message });
    }
});

// ===== 数据统计 API =====
router.get('/statistics/dashboard', async (req, res) => {
    try {
        const stats = {
            overview: { total_tenants: 156, active_tenants: 142, total_users: 1250, total_flocks: 850 },
            revenue: { monthly: 125600, yearly: 1456800, growth: 12.5 },
            usage: { api_calls_today: 15680, storage_used: 45.6, bandwidth_used: 128.9 },
            trends: {
                tenant_growth: [120, 125, 135, 142, 148, 156],
                revenue_trend: [98000, 105000, 112000, 118000, 122000, 125600]
            }
        };
        
        res.json({ success: true, data: stats });
    } catch (error) {
        res.status(500).json({ success: false, message: '获取统计数据失败', error: error.message });
    }
});

// ===== 鹅群管理增强 API =====
router.post('/flocks', async (req, res) => {
    try {
        const { flock_name, breed, initialCount, location, tenant_id } = req.body;
        const newFlock = {
            id: Date.now(),
            flock_name,
            breed,
            currentCount: initialCount,
            location,
            tenant_id,
            status: 'active',
            created_at: new Date().toISOString()
        };
        
        res.json({ success: true, message: '鹅群创建成功', data: newFlock });
    } catch (error) {
        res.status(500).json({ success: false, message: '创建失败', error: error.message });
    }
});

router.put('/flocks/:id', async (req, res) => {
    try {
        const flockId = req.params.id;
        res.json({ success: true, message: '鹅群更新成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '更新失败', error: error.message });
    }
});

router.delete('/flocks/:id', async (req, res) => {
    try {
        const flockId = req.params.id;
        res.json({ success: true, message: '鹅群删除成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '删除失败', error: error.message });
    }
});

// ===== 生产记录管理 API =====
router.post('/production', async (req, res) => {
    try {
        const { flock_id, record_date, egg_count, feed_consumption, water_consumption, notes } = req.body;
        const newRecord = {
            id: Date.now(),
            flock_id,
            record_date,
            egg_count,
            feed_consumption,
            water_consumption,
            notes,
            created_at: new Date().toISOString()
        };
        
        res.json({ success: true, message: '生产记录创建成功', data: newRecord });
    } catch (error) {
        res.status(500).json({ success: false, message: '创建失败', error: error.message });
    }
});

router.put('/production/:id', async (req, res) => {
    try {
        const recordId = req.params.id;
        res.json({ success: true, message: '生产记录更新成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '更新失败', error: error.message });
    }
});

router.delete('/production/:id', async (req, res) => {
    try {
        const recordId = req.params.id;
        res.json({ success: true, message: '生产记录删除成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '删除失败', error: error.message });
    }
});

// ===== 健康记录管理 API =====
router.post('/health-records', async (req, res) => {
    try {
        const { flock_id, check_date, health_status, temperature, symptoms, treatment, vet_name, notes } = req.body;
        const newRecord = {
            id: Date.now(),
            flock_id,
            check_date,
            health_status,
            temperature,
            symptoms,
            treatment,
            vet_name,
            notes,
            created_at: new Date().toISOString()
        };
        
        res.json({ success: true, message: '健康记录创建成功', data: newRecord });
    } catch (error) {
        res.status(500).json({ success: false, message: '创建失败', error: error.message });
    }
});

router.put('/health-records/:id', async (req, res) => {
    try {
        const recordId = req.params.id;
        res.json({ success: true, message: '健康记录更新成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '更新失败', error: error.message });
    }
});

router.delete('/health-records/:id', async (req, res) => {
    try {
        const recordId = req.params.id;
        res.json({ success: true, message: '健康记录删除成功' });
    } catch (error) {
        res.status(500).json({ success: false, message: '删除失败', error: error.message });
    }
});

// ===== 批量操作 API =====
router.post('/batch-operations', async (req, res) => {
    try {
        const { operation, type, ids, data } = req.body;
        
        // 验证批量操作参数
        if (!operation || !type || !Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({
                success: false,
                message: '批量操作参数不完整'
            });
        }
        
        // 限制批量操作数量
        if (ids.length > 100) {
            return res.status(400).json({
                success: false,
                message: '单次批量操作不能超过100条记录'
            });
        }
        
        let message = '';
        switch (operation) {
            case 'delete':
                message = `成功删除 ${ids.length} 条${type}记录`;
                break;
            case 'update':
                message = `成功更新 ${ids.length} 条${type}记录`;
                break;
            case 'export':
                message = `成功导出 ${ids.length} 条${type}记录`;
                break;
            default:
                return res.status(400).json({
                    success: false,
                    message: '不支持的批量操作类型'
                });
        }
        
        res.json({
            success: true,
            message: message,
            processed: ids.length
        });
        
    } catch (error) {
        res.status(500).json({ success: false, message: '批量操作失败', error: error.message });
    }
});

module.exports = router;