/**
 * 智慧养鹅SAAS管理后台 - 统一路由架构
 * 
 * 业务逻辑架构：
 * 1. 平台级管理功能 - 影响所有租户，需要超级管理员权限
 *    - 今日鹅价管理
 *    - 平台公告管理  
 *    - 知识库管理
 *    - 商城模块管理
 *    - 租户管理
 *    - AI大模型配置
 *    - 系统设置
 * 
 * 2. 租户级管理功能 - 数据完全隔离，每个租户只能看到自己的数据
 *    - 鹅群管理
 *    - 生产物料管理
 *    - 健康记录管理
 *    - 财务管理
 */

const express = require('express');
const bcrypt = require('bcrypt');
const TenantManagementService = require('../../services/tenant-management');
const db = require('../config/database');

const router = express.Router();
const tenantService = new TenantManagementService();

// ==================== 认证中间件 ====================

// 验证登录状态
const requireAuth = (req, res, next) => {
  console.log('登录状态检查:', req.session?.user ? '已登录' : '未登录');
  if (!req.session || !req.session.user) {
    if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
      return res.status(401).json({ success: false, message: '请先登录' });
    }
    return res.redirect('/login');
  }
  next();
};

// 验证平台管理员权限（超级管理员）
const requirePlatformAdmin = (req, res, next) => {
  if (!req.session.user || !['super_admin', 'platform_admin'].includes(req.session.user.role)) {
    if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
      return res.status(403).json({ success: false, message: '需要平台管理员权限' });
    }
    return res.status(403).render('error', { 
      title: '权限不足', 
      message: '需要平台管理员权限才能访问此页面',
      layout: 'layouts/main'
    });
  }
  next();
};

// 验证租户管理员权限
const requireTenantAdmin = (req, res, next) => {
  if (!req.session.user || !['super_admin', 'platform_admin', 'tenant_admin'].includes(req.session.user.role)) {
    if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
      return res.status(403).json({ success: false, message: '需要租户管理员权限' });
    }
    return res.status(403).render('error', { 
      title: '权限不足', 
      message: '需要租户管理员权限才能访问此页面',
      layout: 'layouts/main' 
    });
  }
  next();
};

// ==================== 认证路由 ====================

// 登录页面
router.get('/login', (req, res) => {
  if (req.session && req.session.user) {
    return res.redirect('/dashboard');
  }
  
  res.render('auth/login', {
    title: '登录 - 智慧养鹅SAAS管理平台',
    error: req.query.error,
    layout: false // 登录页面不使用主布局
  });
});

// 登录处理
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    console.log('登录尝试:', { username, password: '***' });
    
    if (!username || !password) {
      return res.redirect('/login?error=' + encodeURIComponent('用户名和密码不能为空'));
    }

    // 查询用户（支持用户名或邮箱登录）
    const db = req.app.locals.db;
    console.log('[DB Query] SELECT * FROM platform_admins WHERE (username = ? OR email = ?) AND status = ?', [username, username, 'active']);
    
    const users = await db.execute(
      'SELECT * FROM platform_admins WHERE (username = ? OR email = ?) AND status = ?',
      [username, username, 'active']
    );
    
    console.log('查询结果:', users.length, '个用户');
    
    if (users.length === 0) {
      return res.redirect('/login?error=' + encodeURIComponent('用户名或密码错误'));
    }

    const user = users[0];
    
    // bcrypt密码验证
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.redirect('/login?error=' + encodeURIComponent('用户名或密码错误'));
    }

    // 更新最后登录时间
    console.log('[DB Query] UPDATE platform_admins SET last_login = NOW() WHERE id = ?', [user.id]);
    await db.execute('UPDATE platform_admins SET last_login = NOW() WHERE id = ?', [user.id]);

    // 设置session
    req.session.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      role: user.role,
      tenant_id: user.tenant_id
    };

    console.log('用户登录成功:', req.session.user);
    res.redirect('/dashboard');
    
  } catch (error) {
    console.error('登录错误:', error);
    res.redirect('/login?error=' + encodeURIComponent('登录失败，请重试'));
  }
});

// 兼容性路由 - 支持 /auth/login 路径 (AJAX调用)
router.post('/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    console.log('登录尝试 (auth路径):', { username, password: '***' });
    
    if (!username || !password) {
      return res.status(400).json({ success: false, message: '用户名和密码不能为空' });
    }

    // 查询用户（支持用户名或邮箱登录）
    const db = req.app.locals.db;
    console.log('[DB Query] SELECT * FROM platform_admins WHERE (username = ? OR email = ?) AND status = ?', [username, username, 'active']);
    
    const users = await db.execute(
      'SELECT * FROM platform_admins WHERE (username = ? OR email = ?) AND status = ?',
      [username, username, 'active']
    );
    
    console.log('查询结果:', users.length, '个用户');
    
    if (users.length === 0) {
      return res.status(401).json({ success: false, message: '用户名或密码错误' });
    }

    const user = users[0];
    
    // bcrypt密码验证
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ success: false, message: '用户名或密码错误' });
    }

    // 更新最后登录时间
    console.log('[DB Query] UPDATE platform_admins SET last_login = NOW() WHERE id = ?', [user.id]);
    await db.execute('UPDATE platform_admins SET last_login = NOW() WHERE id = ?', [user.id]);

    // 设置session
    req.session.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      role: user.role,
      tenant_id: user.tenant_id
    };

    console.log('用户登录成功 (auth路径):', req.session.user);
    return res.json({ success: true, message: '登录成功', redirect: '/dashboard' });
    
  } catch (error) {
    console.error('登录错误 (auth路径):', error);
    return res.status(500).json({ success: false, message: '登录失败，请稍后重试' });
  }
});

// 登出
router.post('/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      console.error('登出错误:', err);
    }
    res.redirect('/login');
  });
});

// ==================== 首页和仪表板 ====================

// 首页重定向
router.get('/', (req, res) => {
  if (req.session?.user) {
    res.redirect('/dashboard');
  } else {
    res.redirect('/login');
  }
});

// 仪表板 - 根据用户角色显示不同内容
router.get('/dashboard', requireAuth, async (req, res) => {
  try {
    console.log('加载仪表板...');
    const user = req.session.user;
    const db = req.app.locals.db;
    
    // 根据用户角色加载不同的仪表板数据
    let dashboardData = {
      user,
      isPlatformAdmin: ['super_admin', 'platform_admin'].includes(user.role),
      isTenantAdmin: ['super_admin', 'platform_admin', 'tenant_admin'].includes(user.role)
    };
    
    if (dashboardData.isPlatformAdmin) {
      // 平台管理员看到平台级数据
      try {
        const tenantResult = await tenantService.getTenants({ page: 1, limit: 10 });
        dashboardData.tenantStats = {
          total: tenantResult.pagination?.total || 0,
          active: tenantResult.data?.filter(t => t.status === 'active').length || 0,
          recent: tenantResult.data?.slice(0, 5) || []
        };
      } catch (error) {
        console.error('租户列表加载错误:', error);
        dashboardData.tenantStats = { total: 0, active: 0, recent: [] };
      }
      
      try {
        const [platformUserResult] = await db.execute('SELECT COUNT(*) as count FROM platform_admins WHERE status = ?', ['active']);
        dashboardData.platformUserCount = platformUserResult[0]?.count || 0;
      } catch (error) {
        console.error('平台用户列表加载错误:', error);
        dashboardData.platformUserCount = 0;
      }
      
    } else if (user.tenant_id) {
      // 租户管理员看到租户内数据
      dashboardData.tenantData = {
        tenant_id: user.tenant_id,
        // 这里可以加载租户特定的统计数据
      };
    }
    
    // 创建stats对象，用于模板渲染
    const stats = {
      // 基本统计
      totalTenants: dashboardData.tenantStats?.total || 0,
      activeTenants: dashboardData.tenantStats?.active || 0,
      totalUsers: dashboardData.platformUserCount || 0,
      monthlyRevenue: 0, // 需要从订单系统获取
      
      // 订阅计划统计
      basicPlan: 0,
      standardPlan: 0,
      premiumPlan: 0,
      enterprisePlan: 0,
      
      // 业务数据统计
      totalFlocks: 0,
      totalGeese: 0,
      todayRecords: 0,
      todayEggs: 0,
      activeProducts: 0,
      publishedPrices: 0,
      
      // 用户活跃度统计
      weeklyActiveUsers: 0,
      monthlyActiveUsers: 0,
      activeUsers: 0,
      publishedArticles: 0,
      helpArticles: 0,
      activeAnnouncements: 0
    };
    
    // 根据租户数据计算订阅计划统计
    if (dashboardData.tenantStats?.recent) {
      dashboardData.tenantStats.recent.forEach(tenant => {
        switch(tenant.subscription_plan) {
          case 'basic': stats.basicPlan++; break;
          case 'standard': stats.standardPlan++; break;
          case 'premium': stats.premiumPlan++; break;
          case 'enterprise': stats.enterprisePlan++; break;
        }
      });
    }
    
    // 系统健康状态
    const systemHealth = {
      database: { status: 'healthy', responseTime: 50 },
      expiringSubs: 0,
      pendingOrders: 0,
      apiEndpoints: { active: 25, total: 30 },
      systemLoad: { cpu: 25, memory: 45, disk: 60 },
      uptime: process.uptime(),
      nodeVersion: process.version
    };

    console.log('渲染仪表板页面...');
    res.render('dashboard/index', {
      title: '控制台 - 智慧养鹅SAAS管理平台',
      currentPage: 'dashboard',
      stats,
      systemHealth,
      recentTenants: dashboardData.tenantStats?.recent || [],
      recentOrders: [],
      ...dashboardData
    });
    
  } catch (error) {
    console.error('仪表板加载错误:', error);
    res.status(500).render('error', {
      title: '系统错误',
      message: '仪表板加载失败，请重试',
      layout: 'layouts/main'
    });
  }
});

// ==================== 平台级管理功能路由 ====================

// 1. 租户管理
router.get('/tenants', requireAuth, requirePlatformAdmin, async (req, res) => {
    try {
        console.log('加载租户管理页面...');
        
        // 获取租户统计数据
        const [totalResult] = await db.execute('SELECT COUNT(*) as total FROM platform_admins');
        const total = totalResult[0]?.total || 0;
        
        const stats = {
            total: total,
            active: Math.floor(total * 0.8),
            inactive: Math.floor(total * 0.2),
            thisMonth: Math.floor(total * 0.1)
        };
        
        // 获取租户列表
        const tenantsResult = await db.execute(`
            SELECT id, username, name, email, role, status, created_at, last_login
            FROM platform_admins
            ORDER BY created_at DESC
            LIMIT 20
        `);
        const tenants = tenantsResult[0] || [];

        // 获取订阅计划
        const plansResult = await db.execute('SELECT plan_code, display_name FROM subscription_plans WHERE is_active = true ORDER BY sort_order');
        const plans = plansResult[0] || [];
        
        res.render('tenants/index', {
            title: '租户管理',
            stats,
            tenants,
            plans,
            user: req.session.user
        });
    } catch (error) {
        console.error('平台用户列表加载错误:', error);
        res.status(500).render('error', {
            title: '加载失败',
            message: '页面加载失败',
            error: error,
            layout: 'layouts/main'
        });
    }
});

// 租户详情 - 进入租户级管理界面
router.get('/tenants/:id', requireAuth, requirePlatformAdmin, async (req, res) => {
  try {
    const tenantId = parseInt(req.params.id);
    const tenant = await tenantService.getTenantById(tenantId);
    
    if (!tenant) {
      return res.status(404).render('error', {
        title: '租户不存在',
        message: '未找到指定的租户',
        layout: 'layouts/main'
      });
    }
    
    // 切换到租户上下文，设置临时租户ID
    req.session.viewingTenantId = tenantId;
    
    res.render('tenants/details', {
      title: `${tenant.name} - 租户详情`,
      currentPage: 'tenant-detail',
      user: req.session.user,
      tenant,
      viewingTenantId: tenantId
    });
  } catch (error) {
    console.error('租户详情加载错误:', error);
    res.status(500).render('error', {
      title: '加载失败',
      message: '租户详情加载失败，请重试',
      layout: 'layouts/main'
    });
  }
});

// 2. 今日鹅价管理
router.get('/goose-prices', requireAuth, requirePlatformAdmin, async (req, res) => {
  try {
    console.log('加载今日鹅价管理页面...');
    
    // 获取鹅价统计数据
    const stats = {
      currentPrice: 12.5,
      priceChange: '+0.5',
      changePercent: '+4.2%',
      totalRecords: 150,
      todayRecords: 5,
      avgPrice: 12.2,
      maxPrice: 15.0,
      minPrice: 10.5
    };
    
    // 获取价格历史数据（模拟数据）
    const priceHistory = [
      { id: 1, date: '2024-01-01', price: 12.0, change: 0, region: '华东', market: '上海', grade: 'A级' },
      { id: 2, date: '2024-01-02', price: 12.2, change: 0.2, region: '华东', market: '杭州', grade: 'A级' },
      { id: 3, date: '2024-01-03', price: 12.5, change: 0.3, region: '华东', market: '南京', grade: 'A级' },
      { id: 4, date: '2024-01-04', price: 12.3, change: -0.2, region: '华南', market: '广州', grade: 'A级' },
      { id: 5, date: '2024-01-05', price: 12.5, change: 0.2, region: '华南', market: '深圳', grade: 'A级' }
    ];
    
    // 获取地区列表
    const regions = ['华东', '华南', '华北', '华中', '西南', '西北', '东北'];
    
    // 获取市场列表
    const markets = ['上海', '杭州', '南京', '广州', '深圳', '北京', '天津', '武汉', '成都'];
    
    // 获取等级列表
    const grades = ['A级', 'B级', 'C级'];
    
    res.render('goose-prices/index', {
      title: '今日鹅价管理 - 智慧养鹅SAAS管理平台',
      currentPage: 'goose-prices',
      stats,
      priceHistory,
      regions,
      markets,
      grades,
      user: req.session.user
    });
  } catch (error) {
    console.error('今日鹅价页面加载错误:', error);
    res.status(500).render('error', {
      title: '加载失败',
      message: '今日鹅价管理页面加载失败，请重试',
      layout: 'layouts/main'
    });
  }
});

// 3. 平台公告管理
router.get('/announcements', requireAuth, requirePlatformAdmin, async (req, res) => {
  try {
    console.log('加载平台公告管理页面...');
    
    // 获取公告统计数据
    const stats = {
      total: 25,
      published: 20,
      draft: 3,
      archived: 2,
      todayPublished: 2,
      totalViews: 5420,
      todayViews: 125
    };
    
    // 获取公告列表（模拟数据）
    const announcements = [
      {
        id: 1,
        title: '系统维护通知',
        content: '系统将于本周末进行维护升级，预计停机2小时...',
        status: 'published',
        priority: 'high',
        views: 1250,
        author: '系统管理员',
        created_at: new Date('2024-01-01'),
        updated_at: new Date('2024-01-01')
      },
      {
        id: 2,
        title: '新功能发布公告',
        content: '我们很高兴地宣布新的AI功能已经上线，包括智能价格预测...',
        status: 'published',
        priority: 'medium',
        views: 890,
        author: '产品团队',
        created_at: new Date('2024-01-02'),
        updated_at: new Date('2024-01-02')
      },
      {
        id: 3,
        title: '春节放假通知',
        content: '春节期间客服时间调整通知...',
        status: 'draft',
        priority: 'low',
        views: 0,
        author: '人事部',
        created_at: new Date('2024-01-03'),
        updated_at: new Date('2024-01-03')
      }
    ];
    
    // 获取公告分类
    const categories = [
      { id: 1, name: '系统通知', count: 8 },
      { id: 2, name: '功能更新', count: 6 },
      { id: 3, name: '活动公告', count: 5 },
      { id: 4, name: '维护通知', count: 4 },
      { id: 5, name: '其他', count: 2 }
    ];
    
    res.render('announcements/index', {
      title: '平台公告管理 - 智慧养鹅SAAS管理平台',
      currentPage: 'announcements',
      stats,
      announcements,
      categories,
      user: req.session.user
    });
  } catch (error) {
    console.error('平台公告页面加载错误:', error);
    res.status(500).render('error', {
      title: '加载失败',
      message: '平台公告管理页面加载失败，请重试',
      layout: 'layouts/main'
    });
  }
});

// 4. 知识库管理
router.get('/knowledge', requireAuth, requirePlatformAdmin, async (req, res) => {
  try {
    console.log('加载知识库管理页面...');
    
    // 获取知识库统计数据
    const stats = {
      total: 45,
      published: 40,
      draft: 3,
      archived: 2,
      categories: 8,
      totalViews: 12500,
      todayViews: 125,
      avgRating: 4.6
    };
    
    // 获取知识文章列表（模拟数据）
    const articles = [
      {
        id: 1,
        title: '鹅的养殖技术指南',
        content: '详细介绍鹅的养殖技术要点...',
        category: '养殖技术',
        status: 'published',
        views: 1250,
        rating: 4.8,
        author: '专家团队',
        tags: ['养殖', '技术', '指南'],
        created_at: new Date('2024-01-01'),
        updated_at: new Date('2024-01-01')
      },
      {
        id: 2,
        title: '鹅病防治手册',
        content: '常见鹅病的预防和治疗方法...',
        category: '疾病防治',
        status: 'published',
        views: 980,
        rating: 4.5,
        author: '兽医专家',
        tags: ['疾病', '防治', '健康'],
        created_at: new Date('2024-01-02'),
        updated_at: new Date('2024-01-02')
      },
      {
        id: 3,
        title: '鹅产品市场分析',
        content: '当前鹅产品市场趋势分析...',
        category: '市场行情',
        status: 'draft',
        views: 0,
        rating: 0,
        author: '市场分析师',
        tags: ['市场', '分析', '趋势'],
        created_at: new Date('2024-01-03'),
        updated_at: new Date('2024-01-03')
      }
    ];
    
    // 获取分类列表（模拟数据）
    const categories = [
      { id: 1, name: '养殖技术', count: 15, description: '鹅的养殖相关技术', color: 'primary' },
      { id: 2, name: '疾病防治', count: 12, description: '鹅病预防和治疗', color: 'danger' },
      { id: 3, name: '市场行情', count: 8, description: '鹅产品市场信息', color: 'success' },
      { id: 4, name: '营养饲料', count: 6, description: '鹅的营养和饲料', color: 'warning' },
      { id: 5, name: '繁殖技术', count: 4, description: '鹅的繁殖相关技术', color: 'info' }
    ];
    
    // 获取标签列表
    const tags = ['养殖', '技术', '疾病', '防治', '市场', '分析', '营养', '饲料', '繁殖'];
    
    res.render('knowledge/index', {
      title: '知识库管理 - 智慧养鹅SAAS管理平台',
      currentPage: 'knowledge',
      stats,
      articles,
      categories,
      tags,
      user: req.session.user
    });
  } catch (error) {
    console.error('知识库页面加载错误:', error);
    res.status(500).render('error', {
      title: '加载失败',
      message: '知识库管理页面加载失败，请重试',
      layout: 'layouts/main'
    });
  }
});

// 5. 商城模块管理
router.get('/mall', requireAuth, requirePlatformAdmin, async (req, res) => {
  try {
    console.log('加载商城模块管理页面...');
    
    // 获取商城统计数据
    const stats = {
      totalProducts: 120,
      activeProducts: 95,
      inactiveProducts: 25,
      totalOrders: 350,
      todayOrders: 8,
      totalRevenue: 25600.50,
      todayRevenue: 1200.00,
      avgOrderValue: 73.14,
      topSellingCategory: '鹅苗'
    };
    
    // 获取商品列表（模拟数据）
    const products = [
      {
        id: 1,
        name: '优质鹅苗',
        description: '健康优质的鹅苗，成活率高',
        price: 15.00,
        stock: 500,
        status: 'active',
        sales: 120,
        category: '鹅苗',
        images: ['/images/goose-chick-1.jpg'],
        created_at: new Date('2024-01-01')
      },
      {
        id: 2,
        name: '鹅蛋礼盒装',
        description: '新鲜鹅蛋，营养丰富',
        price: 68.00,
        stock: 200,
        status: 'active',
        sales: 85,
        category: '鹅蛋',
        images: ['/images/goose-eggs-1.jpg'],
        created_at: new Date('2024-01-02')
      },
      {
        id: 3,
        name: '鹅肉礼盒',
        description: '优质鹅肉，口感鲜美',
        price: 128.00,
        stock: 50,
        status: 'active',
        sales: 45,
        category: '鹅肉',
        images: ['/images/goose-meat-1.jpg'],
        created_at: new Date('2024-01-03')
      }
    ];
    
    // 获取订单列表（模拟数据）
    const orders = [
      {
        id: 1,
        orderNo: 'ORD20240101001',
        customer: '张三',
        customerPhone: '13800138001',
        amount: 150.00,
        status: 'completed',
        items: 2,
        paymentMethod: '微信支付',
        created_at: new Date('2024-01-01')
      },
      {
        id: 2,
        orderNo: 'ORD20240101002',
        customer: '李四',
        customerPhone: '13800138002',
        amount: 68.00,
        status: 'pending',
        items: 1,
        paymentMethod: '支付宝',
        created_at: new Date('2024-01-02')
      }
    ];
    
    // 获取商品分类
    const categories = [
      { id: 1, name: '鹅苗', count: 25, description: '各种品种的鹅苗' },
      { id: 2, name: '鹅蛋', count: 15, description: '新鲜鹅蛋产品' },
      { id: 3, name: '鹅肉', count: 12, description: '优质鹅肉产品' },
      { id: 4, name: '鹅毛', count: 8, description: '鹅毛制品' },
      { id: 5, name: '饲料', count: 10, description: '专用鹅饲料' }
    ];
    
    res.render('mall/index', {
      title: '商城模块管理 - 智慧养鹅SAAS管理平台',
      currentPage: 'mall',
      stats,
      products,
      orders,
      categories,
      user: req.session.user
    });
  } catch (error) {
    console.error('商城模块页面加载错误:', error);
    res.status(500).render('error', {
      title: '加载失败',
      message: '商城模块管理页面加载失败，请重试',
      layout: 'layouts/main'
    });
  }
});

// 6. AI大模型配置
router.get('/ai-config', requireAuth, requirePlatformAdmin, async (req, res) => {
  try {
    console.log('加载AI大模型配置页面...');

    // 获取AI配置统计数据
    const stats = {
      totalConfigs: 5,
      activeConfigs: 3,
      inactiveConfigs: 2,
      totalCalls: 1250,
      todayCalls: 45,
      successRate: 98.5,
      avgResponseTime: 1.2
    };

    // 获取AI配置列表（模拟数据）
    const configs = [
      {
        id: 1,
        name: 'GPT-4 配置',
        model: 'gpt-4',
        provider: 'OpenAI',
        status: 'active',
        apiKey: 'sk-***',
        calls: 500,
        successRate: 99.2,
        lastUsed: new Date('2024-01-01'),
        created_at: new Date('2024-01-01')
      },
      {
        id: 2,
        name: '通义千问配置',
        model: 'qwen-max',
        provider: '阿里云',
        status: 'active',
        apiKey: 'sk-***',
        calls: 300,
        successRate: 98.5,
        lastUsed: new Date('2024-01-02'),
        created_at: new Date('2024-01-02')
      },
      {
        id: 3,
        name: '文心一言配置',
        model: 'ernie-bot',
        provider: '百度',
        status: 'inactive',
        apiKey: 'sk-***',
        calls: 150,
        successRate: 97.8,
        lastUsed: new Date('2024-01-03'),
        created_at: new Date('2024-01-03')
      }
    ];

    // 获取AI服务提供商信息
    const providerInfo = {
      'OpenAI': { name: 'OpenAI', icon: 'fab fa-openai' },
      '阿里云': { name: '阿里云', icon: 'fas fa-cloud' },
      '百度': { name: '百度', icon: 'fas fa-search' },
      'zhipu': { name: '智谱AI', icon: 'fas fa-brain' },
      'siliconflow': { name: 'SiliconFlow', icon: 'fas fa-microchip' },
      'openai': { name: 'OpenAI', icon: 'fab fa-openai' }
    };

    res.render('ai-config/index', {
      title: 'AI大模型配置 - 智慧养鹅SAAS管理平台',
      currentPage: 'ai-config',
      stats,
      configs,
      providerInfo,
      user: req.session.user
    });
  } catch (error) {
    console.error('AI配置页面加载错误:', error);
    res.status(500).render('error', {
      title: '加载失败',
      message: 'AI大模型配置页面加载失败，请重试',
      layout: 'layouts/main'
    });
  }
});

// 7. 系统设置
router.get('/settings', requireAuth, requirePlatformAdmin, (req, res) => {
  res.render('settings/index', {
    title: '系统设置 - 智慧养鹅SAAS管理平台',
    currentPage: 'settings',
    user: req.session.user
  });
});

// 8. 平台用户管理
router.get('/platform-users', requireAuth, requirePlatformAdmin, async (req, res) => {
  try {
    const db = req.app.locals.db;
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const offset = (page - 1) * limit;

    const [countResult] = await db.execute('SELECT COUNT(*) as total FROM platform_admins');
    const total = countResult[0].total;

    const [users] = await db.execute(`
      SELECT id, username, email, name, role, status, last_login, created_at
      FROM platform_admins 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `, [limit, offset]);

    res.render('platform-users/index', {
      title: '平台用户管理 - 智慧养鹅SAAS管理平台',
      currentPage: 'platform-users',
      user: req.session.user,
      users: users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('平台用户列表加载错误:', error);
    res.status(500).render('error', {
      title: '加载失败',
      message: '平台用户列表加载失败，请重试',
      layout: 'layouts/main'
    });
  }
});

// ==================== 租户级管理功能路由 ====================

// 检查租户上下文的中间件
const ensureTenantContext = (req, res, next) => {
  const user = req.session.user;
  let tenantId = null;
  
  // 平台管理员可能在查看特定租户
  if (['super_admin', 'platform_admin'].includes(user.role)) {
    tenantId = req.session.viewingTenantId || null;
  } else if (user.tenant_id) {
    // 租户用户只能访问自己的租户数据
    tenantId = user.tenant_id;
  }
  
  if (!tenantId) {
    return res.status(403).render('error', {
      title: '权限不足',
      message: '请先选择要管理的租户',
      layout: 'layouts/main'
    });
  }
  
  req.tenantId = tenantId;
  next();
};

// 1. 鹅群管理（租户级）
router.get('/tenant/flocks', requireAuth, requireTenantAdmin, ensureTenantContext, (req, res) => {
  res.render('flocks/index', {
    title: '鹅群管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'tenant-flocks',
    user: req.session.user,
    tenantId: req.tenantId,
    isViewingTenant: !!req.session.viewingTenantId
  });
});

// 2. 生产物料管理（租户级）
router.get('/tenant/inventory', requireAuth, requireTenantAdmin, ensureTenantContext, (req, res) => {
  res.render('inventory/index', {
    title: '生产物料管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'tenant-inventory',
    user: req.session.user,
    tenantId: req.tenantId,
    isViewingTenant: !!req.session.viewingTenantId
  });
});

// 3. 健康记录管理（租户级）
router.get('/tenant/health', requireAuth, requireTenantAdmin, ensureTenantContext, (req, res) => {
  res.render('health/index', {
    title: '健康记录管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'tenant-health',
    user: req.session.user,
    tenantId: req.tenantId,
    isViewingTenant: !!req.session.viewingTenantId
  });
});

// 4. 财务管理（租户级）
router.get('/tenant/finance', requireAuth, requireTenantAdmin, ensureTenantContext, (req, res) => {
  res.render('finance/index', {
    title: '财务管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'tenant-finance',
    user: req.session.user,
    tenantId: req.tenantId,
    isViewingTenant: !!req.session.viewingTenantId
  });
});

// 5. 生产记录管理（租户级）
router.get('/tenant/production', requireAuth, requireTenantAdmin, ensureTenantContext, (req, res) => {
  res.render('production/index', {
    title: '生产记录管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'tenant-production',
    user: req.session.user,
    tenantId: req.tenantId,
    isViewingTenant: !!req.session.viewingTenantId
  });
});

// ==================== 其他功能路由 ====================

// 监控页面
router.get('/monitoring', requireAuth, requirePlatformAdmin, (req, res) => {
  res.render('monitoring/index', {
    title: '性能监控 - 智慧养鹅SAAS管理平台',
    currentPage: 'monitoring',
    user: req.session.user
  });
});

// 系统日志
router.get('/logs', requireAuth, requirePlatformAdmin, (req, res) => {
  res.render('system/logs', {
    title: '系统日志 - 智慧养鹅SAAS管理平台',
    currentPage: 'logs',
    user: req.session.user
  });
});

// 报表管理
router.get('/reports', requireAuth, requirePlatformAdmin, (req, res) => {
  res.render('reports/index', {
    title: '报表管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'reports',
    user: req.session.user
  });
});

// ==================== API端点路由 ====================

// API文档
router.get('/api-docs', requireAuth, requirePlatformAdmin, (req, res) => {
  res.render('api-endpoints/index', {
    title: 'API文档 - 智慧养鹅SAAS管理平台',
    currentPage: 'api-docs',
    user: req.session.user
  });
});

// ==================== 错误处理 ====================

// 404处理
router.use('*', (req, res) => {
  res.status(404).render('error', {
    title: '页面未找到',
    message: '您访问的页面不存在',
    layout: 'layouts/main'
  });
});

module.exports = router;