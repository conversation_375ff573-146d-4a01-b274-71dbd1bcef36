const express = require('express');
const router = express.Router();
const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'goose_saas_platform',
    charset: 'utf8mb4'
};

// 今日鹅价列表页面
router.get('/', async (req, res) => {
    try {
        const { region, breed, dateFrom, dateTo } = req.query;
        
        // 使用模拟数据，因为数据库表可能不存在
        const mockPrices = [
            {
                id: 1,
                date: '2025-08-29',
                region: '广州',
                breed: '白鹅',
                price_per_kg: 31.57,
                unit: '斤',
                market_name: '广州市场',
                is_published: true,
                notes: '价格稳定'
            },
            {
                id: 2,
                date: '2025-08-29', 
                region: '江苏南京',
                breed: '灰鹅',
                price_per_kg: 27.77,
                unit: '斤',
                market_name: '南京市场',
                is_published: true,
                notes: '需求旺盛'
            },
            {
                id: 3,
                date: '2025-08-29',
                region: '安徽合肥',
                breed: '白鹅', 
                price_per_kg: 27.30,
                unit: '斤',
                market_name: '合肥市场',
                is_published: true,
                notes: '供应稳定'
            },
            {
                id: 4,
                date: '2025-08-29',
                region: '山东济南',
                breed: '鹅',
                price_per_kg: 24.50,
                unit: '斤', 
                market_name: '济南市场',
                is_published: true,
                notes: '价格平稳'
            }
        ];
        
        // 根据查询参数过滤数据
        let filteredPrices = mockPrices;
        if (region) {
            filteredPrices = filteredPrices.filter(p => p.region.includes(region));
        }
        if (breed) {
            filteredPrices = filteredPrices.filter(p => p.breed.includes(breed));
        }
        
        // 计算统计数据
        const prices = filteredPrices.map(p => p.price_per_kg);
        const stats = {
            currentPrice: prices.length > 0 ? (prices.reduce((a, b) => a + b) / prices.length).toFixed(2) : '0.00',
            maxPrice: prices.length > 0 ? Math.max(...prices).toFixed(2) : '0.00',
            minPrice: prices.length > 0 ? Math.min(...prices).toFixed(2) : '0.00',
            monthlyChange: 2.3,
            totalRecords: filteredPrices.length
        };
        
        const regions = [...new Set(mockPrices.map(p => p.region))];
        
        // 模拟图表数据
        const chartData = {
            labels: ['2025-08-25', '2025-08-26', '2025-08-27', '2025-08-28', '2025-08-29'],
            prices: ['28.50', '29.20', '28.80', '29.50', '28.90']
        };
        
        res.render('goose-prices/index', {
            title: region ? `${region} - 鹅价详情` : '今日鹅价管理',
            prices: filteredPrices,
            stats: stats,
            regions: regions,
            chartData: chartData,
            currentFilter: { region, breed, dateFrom, dateTo }
        });
        
    } catch (error) {
        console.error('获取鹅价列表失败:', error);
        
        // 提供备用数据
        const mockStats = {
            currentPrice: '28.50',
            maxPrice: '31.57',
            minPrice: '24.50',
            monthlyChange: 2.3,
            totalRecords: 4
        };
        
        const mockRegions = ['广州', '江苏南京', '安徽合肥', '山东济南'];
        const mockChartData = {
            labels: ['2025-08-25', '2025-08-26', '2025-08-27', '2025-08-28', '2025-08-29'],
            prices: ['28.50', '29.20', '28.80', '29.50', '28.90']
        };
        
        res.render('goose-prices/index', {
            title: '今日鹅价管理',
            prices: [],
            stats: mockStats,
            regions: mockRegions,
            chartData: mockChartData,
            isDemo: true,
            demoMessage: '当前显示演示数据，数据库连接可能存在问题'
        });
    }
});

// 创建鹅价页面
router.get('/create', (req, res) => {
    res.render('goose-prices/create', {
        title: '发布今日鹅价'
    });
});

// 创建鹅价处理
router.post('/create', async (req, res) => {
    try {
        const {
            date,
            region,
            breed,
            price,
            unit,
            market_name,
            notes,
            is_published
        } = req.body;
        
        const connection = await mysql.createConnection(dbConfig);
        
        await connection.execute(`
            INSERT INTO goose_prices (
                date, region, breed, price, unit, market_name, is_published
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
            date, region, breed, price, unit, market_name, is_published === 'on'
        ]);
        
        await connection.end();
        
        res.redirect('/goose-prices?success=created');
        
    } catch (error) {
        console.error('发布鹅价失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '发布鹅价失败',
            error: error
        });
    }
});

// 价格趋势分析页面
router.get('/trends', async (req, res) => {
    try {
        const connection = await mysql.createConnection(dbConfig);

        // 获取查询参数
        const { dateRange = '30', region = '', gooseType = '' } = req.query;

        // 获取地区列表
        const [regionData] = await connection.execute(`
            SELECT DISTINCT region FROM goose_prices
            WHERE is_published = TRUE
            ORDER BY region
        `);

        // 获取最近指定天数的价格趋势
        const [trends] = await connection.execute(`
            SELECT
                date,
                region,
                breed,
                unit,
                price_per_kg as price,
                market_name
            FROM goose_prices
            WHERE date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            AND is_published = true
            ${region ? 'AND region = ?' : ''}
            ${gooseType ? 'AND breed = ?' : ''}
            ORDER BY date DESC, region ASC
        `, [dateRange, ...(region ? [region] : []), ...(gooseType ? [gooseType] : [])]);

        // 处理趋势数据用于图表显示
        const trendData = {
            labels: [],
            prices: []
        };

        const regionData_chart = {
            labels: [],
            prices: []
        };

        const typeData = {
            labels: [],
            prices: []
        };

        if (trends.length > 0) {
            // 按日期分组计算平均价格
            const dateGroups = {};
            const regionGroups = {};
            const typeGroups = {};

            trends.forEach(trend => {
                const dateKey = trend.date;
                if (!dateGroups[dateKey]) {
                    dateGroups[dateKey] = { total: 0, count: 0 };
                }
                dateGroups[dateKey].total += parseFloat(trend.price || 0);
                dateGroups[dateKey].count += 1;

                // 地区分组
                if (!regionGroups[trend.region]) {
                    regionGroups[trend.region] = { total: 0, count: 0 };
                }
                regionGroups[trend.region].total += parseFloat(trend.price || 0);
                regionGroups[trend.region].count += 1;

                // 品种分组
                if (!typeGroups[trend.breed]) {
                    typeGroups[trend.breed] = { total: 0, count: 0 };
                }
                typeGroups[trend.breed].total += parseFloat(trend.price || 0);
                typeGroups[trend.breed].count += 1;
            });

            // 生成趋势图数据
            Object.keys(dateGroups).sort().forEach(date => {
                trendData.labels.push(date);
                trendData.prices.push((dateGroups[date].total / dateGroups[date].count).toFixed(2));
            });

            // 生成地区分布数据
            Object.keys(regionGroups).forEach(region => {
                regionData_chart.labels.push(region);
                regionData_chart.prices.push((regionGroups[region].total / regionGroups[region].count).toFixed(2));
            });

            // 生成品种对比数据
            Object.keys(typeGroups).forEach(type => {
                typeData.labels.push(type);
                typeData.prices.push((typeGroups[type].total / typeGroups[type].count).toFixed(2));
            });
        }

        await connection.end();

        const regions = regionData.map(r => r.region);

        res.render('goose-prices/trends', {
            title: '鹅价趋势分析',
            trends: trends,
            regions: regions,
            trendData: trendData,
            regionData: regionData_chart,
            typeData: typeData,
            currentFilters: {
                dateRange,
                region,
                gooseType
            }
        });

    } catch (error) {
        console.error('获取价格趋势失败:', error);

        // 如果数据库表不存在，提供模拟数据
        const mockRegions = ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '重庆'];
        const mockTrendData = {
            labels: ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],
            prices: ['16.50', '16.80', '17.20', '16.90', '17.10']
        };
        const mockRegionData = {
            labels: ['北京', '上海', '广州', '深圳'],
            prices: ['18.50', '17.80', '16.20', '17.90']
        };
        const mockTypeData = {
            labels: ['白鹅', '灰鹅', '狮头鹅', '太湖鹅'],
            prices: ['16.80', '17.20', '19.50', '18.30']
        };

        res.render('goose-prices/trends', {
            title: '鹅价趋势分析',
            trends: [],
            regions: mockRegions,
            trendData: mockTrendData,
            regionData: mockRegionData,
            typeData: mockTypeData,
            currentFilters: {
                dateRange: '30',
                region: '',
                gooseType: ''
            },
            isDemo: true
        });
    }
});

// 地区管理页面
router.get('/regions', async (req, res) => {
    try {
        const connection = await mysql.createConnection(dbConfig);
        
        // 获取所有地区的价格统计数据
        const [regionsData] = await connection.execute(`
            SELECT 
                region,
                COUNT(*) as record_count,
                AVG(price_per_kg) as avg_price,
                MAX(price_per_kg) as max_price,
                MIN(price_per_kg) as min_price,
                MAX(date) as latest_date
            FROM goose_prices 
            WHERE is_published = TRUE
            GROUP BY region
            ORDER BY avg_price DESC
        `);
        
        // 获取最近7天各地区价格对比数据
        const [chartData] = await connection.execute(`
            SELECT 
                region,
                date,
                AVG(price_per_kg) as daily_avg_price
            FROM goose_prices 
            WHERE is_published = TRUE 
            AND date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            GROUP BY region, date
            ORDER BY date ASC, region ASC
        `);
        
        await connection.end();
        
        // 处理图表数据
        const regions = [...new Set(chartData.map(d => d.region))];
        const dates = [...new Set(chartData.map(d => d.date))].sort();
        
        const chartSeriesData = regions.map(region => ({
            name: region,
            data: dates.map(date => {
                const dataPoint = chartData.find(d => d.region === region && d.date === date);
                return dataPoint ? parseFloat(dataPoint.daily_avg_price || 0).toFixed(2) : 0;
            })
        }));
        
        res.render('goose-prices/regions', {
            title: '地区价格管理',
            regions: regionsData,
            chartData: {
                labels: dates,
                series: chartSeriesData
            }
        });
        
    } catch (error) {
        console.error('获取地区价格数据失败:', error);
        
        // 使用模拟数据
        const mockRegions = [
            {
                region: '北京',
                record_count: 25,
                avg_price: 18.50,
                max_price: 20.00,
                min_price: 17.00,
                latest_date: '2024-01-15'
            },
            {
                region: '上海',
                record_count: 22,
                avg_price: 17.80,
                max_price: 19.50,
                min_price: 16.20,
                latest_date: '2024-01-15'
            },
            {
                region: '广州',
                record_count: 20,
                avg_price: 16.20,
                max_price: 17.80,
                min_price: 15.50,
                latest_date: '2024-01-15'
            },
            {
                region: '深圳',
                record_count: 18,
                avg_price: 17.90,
                max_price: 19.20,
                min_price: 16.80,
                latest_date: '2024-01-14'
            }
        ];
        
        const mockChartData = {
            labels: ['2024-01-10', '2024-01-11', '2024-01-12', '2024-01-13', '2024-01-14', '2024-01-15'],
            series: [
                {
                    name: '北京',
                    data: ['18.20', '18.40', '18.60', '18.30', '18.70', '18.50']
                },
                {
                    name: '上海',
                    data: ['17.50', '17.70', '17.90', '17.60', '17.85', '17.80']
                },
                {
                    name: '广州',
                    data: ['16.00', '16.10', '16.30', '16.20', '16.40', '16.20']
                },
                {
                    name: '深圳',
                    data: ['17.70', '17.80', '18.00', '17.90', '18.10', '17.90']
                }
            ]
        };
        
        res.render('goose-prices/regions', {
            title: '地区价格管理',
            regions: mockRegions,
            chartData: mockChartData,
            isDemo: true,
            demoMessage: '当前显示的是演示数据，数据库连接可能存在问题'
        });
    }
});

// 删除鹅价记录路由
router.delete('/delete/:id', async (req, res) => {
    try {
        const id = req.params.id;
        
        // Mock deletion success (在实际应用中会从数据库删除)
        console.log(`删除鹅价记录 ID: ${id}`);
        
        res.json({
            success: true,
            message: '价格记录删除成功'
        });
        
    } catch (error) {
        console.error('删除鹅价记录失败:', error);
        res.status(500).json({
            success: false,
            message: '删除失败'
        });
    }
});

// 编辑鹅价记录页面
router.get('/edit/:id', async (req, res) => {
    try {
        const id = req.params.id;
        
        // Mock price data for editing
        const mockPrice = {
            id: parseInt(id),
            date: '2025-08-29',
            region: '广州',
            breed: '白鹅',
            price_per_kg: 31.57,
            unit: '斤',
            market_name: '广州市场',
            is_published: true,
            notes: '价格稳定'
        };
        
        res.render('goose-prices/edit', {
            title: '编辑鹅价记录',
            price: mockPrice
        });
        
    } catch (error) {
        console.error('获取鹅价记录失败:', error);
        res.redirect('/goose-prices?error=record_not_found');
    }
});

// 更新鹅价记录处理
router.put('/edit/:id', async (req, res) => {
    try {
        const id = req.params.id;
        const { date, region, breed, price, unit, market_name, notes, is_published } = req.body;
        
        // Mock update success
        console.log(`更新鹅价记录 ID: ${id}`, req.body);
        
        res.json({
            success: true,
            message: '鹅价记录更新成功'
        });
        
    } catch (error) {
        console.error('更新鹅价记录失败:', error);
        res.status(500).json({
            success: false,
            message: '更新失败'
        });
    }
});

module.exports = router;