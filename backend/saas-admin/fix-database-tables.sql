-- 补充缺失的数据库表和修复现有表结构

-- 1. 确保知识库标签表存在且结构正确
CREATE TABLE IF NOT EXISTS `knowledge_tags` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '标签名称',
  `color` varchar(10) DEFAULT '#007bff' COMMENT '标签颜色',
  `description` text COMMENT '标签描述', 
  `usage_count` int DEFAULT 0 COMMENT '使用次数',
  `status` enum('active','inactive') DEFAULT 'active' COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_tag_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_usage_count` (`usage_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库标签表';

-- 2. 确保知识库分类表存在
CREATE TABLE IF NOT EXISTS `knowledge_categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `parent_id` int DEFAULT 0 COMMENT '父分类ID',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库分类表';

-- 3. 确保文章标签关联表存在
CREATE TABLE IF NOT EXISTS `knowledge_article_tags` (
  `id` int NOT NULL AUTO_INCREMENT,
  `article_id` int NOT NULL,
  `tag_id` int NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_article_tag` (`article_id`, `tag_id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_tag_id` (`tag_id`),
  CONSTRAINT `fk_article_tags_article` FOREIGN KEY (`article_id`) REFERENCES `knowledge_articles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_article_tags_tag` FOREIGN KEY (`tag_id`) REFERENCES `knowledge_tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章标签关联表';

-- 4. 确保商城分类表结构正确
CREATE TABLE IF NOT EXISTS `mall_categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `parent_id` int DEFAULT 0 COMMENT '父分类ID',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `image_url` varchar(500) DEFAULT NULL COMMENT '分类图片',
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商城分类表';

-- 5. 确保商城产品表结构正确
CREATE TABLE IF NOT EXISTS `mall_products` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL COMMENT '产品名称',
  `description` text COMMENT '产品描述',
  `category_id` int DEFAULT NULL,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `stock` int DEFAULT 0 COMMENT '库存',
  `sold_count` int DEFAULT 0 COMMENT '销量',
  `images` json DEFAULT NULL COMMENT '产品图片',
  `specifications` json DEFAULT NULL COMMENT '产品规格',
  `status` enum('active','inactive','out_of_stock') DEFAULT 'active',
  `is_featured` tinyint(1) DEFAULT 0 COMMENT '是否推荐',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_price` (`price`),
  CONSTRAINT `fk_products_category` FOREIGN KEY (`category_id`) REFERENCES `mall_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商城产品表';

-- 6. 创建商品评价表
CREATE TABLE IF NOT EXISTS `mall_reviews` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `user_id` int NOT NULL,
  `tenant_id` int NOT NULL,
  `rating` tinyint NOT NULL DEFAULT 5 COMMENT '评分1-5',
  `content` text COMMENT '评价内容',
  `images` json DEFAULT NULL COMMENT '评价图片',
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_reviews_product` FOREIGN KEY (`product_id`) REFERENCES `mall_products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品评价表';

-- 7. 创建优惠券表
CREATE TABLE IF NOT EXISTS `mall_coupons` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '优惠券名称',
  `code` varchar(50) NOT NULL COMMENT '优惠券代码',
  `type` enum('percent','fixed','free_shipping') DEFAULT 'percent' COMMENT '优惠类型',
  `value` decimal(10,2) NOT NULL COMMENT '优惠值',
  `min_amount` decimal(10,2) DEFAULT 0.00 COMMENT '最小金额',
  `max_discount` decimal(10,2) DEFAULT NULL COMMENT '最大折扣',
  `usage_limit` int DEFAULT NULL COMMENT '使用次数限制',
  `used_count` int DEFAULT 0 COMMENT '已使用次数',
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `status` enum('active','inactive','expired') DEFAULT 'active',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`),
  KEY `idx_status` (`status`),
  KEY `idx_dates` (`start_date`, `end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券表';

-- 8. 创建促销活动表
CREATE TABLE IF NOT EXISTS `mall_promotions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL COMMENT '促销名称',
  `description` text COMMENT '促销描述',
  `type` enum('discount','buy_one_get_one','bundle','flash_sale') DEFAULT 'discount',
  `discount_type` enum('percent','fixed') DEFAULT 'percent',
  `discount_value` decimal(10,2) DEFAULT 0.00,
  `applicable_products` json DEFAULT NULL COMMENT '适用商品',
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `status` enum('active','inactive','expired') DEFAULT 'active',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_dates` (`start_date`, `end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='促销活动表';

-- 9. 创建库存记录表
CREATE TABLE IF NOT EXISTS `mall_inventory` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `type` enum('in','out','adjust') DEFAULT 'adjust' COMMENT '库存变动类型',
  `quantity` int NOT NULL COMMENT '变动数量',
  `reason` varchar(500) DEFAULT NULL COMMENT '变动原因',
  `reference_id` int DEFAULT NULL COMMENT '关联ID(如订单ID)',
  `reference_type` varchar(50) DEFAULT NULL COMMENT '关联类型',
  `operator_id` int DEFAULT NULL COMMENT '操作员ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_type` (`type`),
  KEY `idx_reference` (`reference_id`, `reference_type`),
  CONSTRAINT `fk_inventory_product` FOREIGN KEY (`product_id`) REFERENCES `mall_products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存记录表';

-- 10. 创建系统配置表
CREATE TABLE IF NOT EXISTS `system_configs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` enum('string','number','boolean','json') DEFAULT 'string',
  `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开配置',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 11. 插入一些初始数据

-- 知识库分类初始数据
INSERT IGNORE INTO `knowledge_categories` (`name`, `description`, `sort_order`) VALUES
('养殖技术', '专业的养殖技术指导', 1),
('疾病防治', '疾病预防和治疗方法', 2),
('营养饲料', '营养搭配和饲料选择', 3),
('市场行情', '市场价格和行情分析', 4),
('政策法规', '相关政策法规解读', 5);

-- 知识库标签初始数据
INSERT IGNORE INTO `knowledge_tags` (`name`, `color`, `description`) VALUES
('新手必读', '#28a745', '适合新手阅读的文章'),
('高级技巧', '#dc3545', '高级养殖技巧'),
('季节性', '#ffc107', '季节性相关内容'),
('疾病预防', '#6f42c1', '疾病预防相关'),
('营养管理', '#fd7e14', '营养和饲料管理'),
('市场分析', '#20c997', '市场行情分析'),
('政策解读', '#6610f2', '政策法规解读');

-- 商城分类初始数据
INSERT IGNORE INTO `mall_categories` (`name`, `description`, `sort_order`) VALUES
('饲料用品', '各类饲料和营养补充剂', 1),
('养殖设备', '养殖所需的各种设备', 2),
('防疫用品', '疫苗、消毒用品等', 3),
('鹅产品', '鹅蛋、鹅肉等产品', 4),
('其他用品', '其他相关用品', 5);

-- 系统配置初始数据
INSERT IGNORE INTO `system_configs` (`config_key`, `config_value`, `config_type`, `description`, `is_public`) VALUES
('site_name', '智慧养鹅SAAS平台', 'string', '网站名称', 1),
('site_description', '专业的智能化养鹅管理平台', 'string', '网站描述', 1),
('max_file_size', '10', 'number', '最大文件上传大小(MB)', 0),
('allowed_file_types', '["jpg","jpeg","png","gif","pdf","doc","docx"]', 'json', '允许的文件类型', 0),
('email_notifications', 'true', 'boolean', '是否启用邮件通知', 0),
('maintenance_mode', 'false', 'boolean', '是否维护模式', 0);

-- 插入一些商品示例数据
INSERT IGNORE INTO `mall_products` (`name`, `description`, `category_id`, `price`, `stock`) VALUES
('优质玉米饲料', '营养丰富的玉米饲料，适合各阶段鹅群', 1, 45.00, 1000),
('维生素营养剂', '专用维生素补充剂，增强免疫力', 1, 28.50, 500),
('自动饮水器', '不锈钢材质，自动供水系统', 2, 156.00, 200),
('鹅舍保温灯', '节能LED保温灯，适合育雏期使用', 2, 89.00, 150),
('禽流感疫苗', '预防禽流感专用疫苗', 3, 12.00, 300);

COMMIT;