const express = require('express');
const { body, query, validationResult } = require('express-validator');
const winston = require('winston');

const router = express.Router();

// Logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

/**
 * @route GET /api/platform/prices
 * @desc 获取鹅价列表
 * @access Private (Platform Admin)
 */
router.get('/',
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
    query('region').optional().isLength({ max: 100 }).withMessage('地区名称最多100个字符'),
    query('breed').optional().isLength({ max: 100 }).withMessage('品种名称最多100个字符'),
    query('dateFrom').optional().isISO8601().withMessage('开始日期格式无效'),
    query('dateTo').optional().isISO8601().withMessage('结束日期格式无效'),
    query('status').optional().isIn(['active', 'archived']).withMessage('无效的状态值')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        page = 1,
        pageSize = 20,
        region,
        breed,
        dateFrom,
        dateTo,
        status = 'active',
        sortBy = 'date',
        sortOrder = 'DESC'
      } = req.query;

      // 构建查询条件
      const whereClauses = [];
      const params = [];

      if (status) {
        whereClauses.push('status = ?');
        params.push(status);
      }

      if (region) {
        whereClauses.push('region LIKE ?');
        params.push(`%${region}%`);
      }

      if (breed) {
        whereClauses.push('breed LIKE ?');
        params.push(`%${breed}%`);
      }

      if (dateFrom) {
        whereClauses.push('date >= ?');
        params.push(dateFrom);
      }

      if (dateTo) {
        whereClauses.push('date <= ?');
        params.push(dateTo);
      }

      const whereClause = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

      // 验证排序字段
      const allowedSortFields = ['date', 'region', 'breed', 'price_per_kg', 'created_at'];
      const safeSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'date';
      const safeSortOrder = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

      // 构建查询
      const baseQuery = `
        SELECT 
          id, date, region, breed, price_per_kg, market_trend, source, notes,
          status, created_by, created_at, updated_at
        FROM goose_prices 
        ${whereClause}
        ORDER BY ${safeSortBy} ${safeSortOrder}
      `;

      const countQuery = `SELECT COUNT(*) as total FROM goose_prices ${whereClause}`;

      // 执行分页查询
      const result = await req.db.paginate(baseQuery, params, parseInt(page), parseInt(pageSize), countQuery);

      // 获取创建人信息
      const pricesWithCreator = await Promise.all(
        result.data.map(async (price) => {
          if (price.created_by) {
            const creator = await req.db.query(
              'SELECT username, name FROM platform_admins WHERE id = ?',
              [price.created_by]
            );
            price.creator = creator[0] || null;
          }
          return price;
        })
      );

      // 获取统计信息
      const stats = await req.db.query(`
        SELECT 
          region,
          breed,
          AVG(price_per_kg) as avg_price,
          MIN(price_per_kg) as min_price,
          MAX(price_per_kg) as max_price,
          COUNT(*) as count
        FROM goose_prices
        WHERE status = 'active' AND date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY region, breed
        ORDER BY avg_price DESC
        LIMIT 10
      `);

      res.json({
        success: true,
        message: '获取鹅价列表成功',
        data: pricesWithCreator,
        pagination: result.pagination,
        statistics: {
          recent_trends: stats
        }
      });

    } catch (error) {
      logger.error('Get goose prices error', {
        error: error.message,
        adminId: req.admin.id,
        query: req.query
      });

      res.status(500).json({
        success: false,
        message: '获取鹅价列表失败'
      });
    }
  }
);

/**
 * @route GET /api/platform/prices/latest
 * @desc 获取最新鹅价（用于前端显示）
 * @access Public
 */
router.get('/latest',
  [
    query('region').optional().isLength({ max: 100 }).withMessage('地区名称最多100个字符'),
    query('breed').optional().isLength({ max: 100 }).withMessage('品种名称最多100个字符'),
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('限制数量必须在1-50之间')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const { region, breed, limit = 20 } = req.query;

      // 构建查询条件
      const whereClauses = ['status = "active"'];
      const params = [];

      if (region) {
        whereClauses.push('region LIKE ?');
        params.push(`%${region}%`);
      }

      if (breed) {
        whereClauses.push('breed LIKE ?');
        params.push(`%${breed}%`);
      }

      // 获取最新价格（按地区和品种分组，取每组最新日期的价格）
      const query = `
        SELECT 
          gp1.id, gp1.date, gp1.region, gp1.breed, gp1.price_per_kg, 
          gp1.market_trend, gp1.source, gp1.notes
        FROM goose_prices gp1
        INNER JOIN (
          SELECT region, breed, MAX(date) as max_date
          FROM goose_prices
          WHERE ${whereClauses.join(' AND ')}
          GROUP BY region, breed
        ) gp2 ON gp1.region = gp2.region AND gp1.breed = gp2.breed AND gp1.date = gp2.max_date
        WHERE ${whereClauses.join(' AND ')}
        ORDER BY gp1.price_per_kg DESC
        LIMIT ?
      `;

      params.push(parseInt(limit));

      const latestPrices = await req.db.query(query, params);

      // 计算价格趋势
      const trendsQuery = `
        SELECT 
          region, breed,
          AVG(CASE WHEN date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN price_per_kg END) as avg_price_7d,
          AVG(CASE WHEN date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN price_per_kg END) as avg_price_30d
        FROM goose_prices
        WHERE status = 'active'
        GROUP BY region, breed
      `;

      const trends = await req.db.query(trendsQuery);

      // 为每个价格添加趋势信息
      const pricesWithTrends = latestPrices.map(price => {
        const trend = trends.find(t => t.region === price.region && t.breed === price.breed);
        if (trend) {
          price.price_trend_7d = trend.avg_price_7d;
          price.price_trend_30d = trend.avg_price_30d;
          
          // 计算涨跌幅
          if (trend.avg_price_30d && trend.avg_price_7d) {
            price.change_percentage = ((trend.avg_price_7d - trend.avg_price_30d) / trend.avg_price_30d * 100).toFixed(2);
          }
        }
        return price;
      });

      res.json({
        success: true,
        message: '获取最新鹅价成功',
        data: pricesWithTrends,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Get latest goose prices error', {
        error: error.message,
        query: req.query
      });

      res.status(500).json({
        success: false,
        message: '获取最新鹅价失败'
      });
    }
  }
);

/**
 * @route POST /api/platform/prices
 * @desc 添加新的鹅价记录
 * @access Private (Platform Admin)
 */
router.post('/',
  [
    body('date')
      .notEmpty().withMessage('日期不能为空')
      .isISO8601().withMessage('日期格式无效'),
    
    body('region')
      .notEmpty().withMessage('地区不能为空')
      .isLength({ min: 2, max: 100 }).withMessage('地区名称长度必须在2-100个字符之间'),
    
    body('breed')
      .notEmpty().withMessage('品种不能为空')
      .isLength({ min: 2, max: 100 }).withMessage('品种名称长度必须在2-100个字符之间'),
    
    body('pricePerKg')
      .notEmpty().withMessage('价格不能为空')
      .isFloat({ min: 0.01, max: 9999.99 }).withMessage('价格必须在0.01-9999.99之间'),
    
    body('marketTrend')
      .optional()
      .isIn(['up', 'down', 'stable']).withMessage('市场趋势只能是up、down或stable'),
    
    body('source').optional().isLength({ max: 200 }).withMessage('价格来源最多200个字符'),
    body('notes').optional().isLength({ max: 1000 }).withMessage('备注最多1000个字符')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        date,
        region,
        breed,
        pricePerKg,
        marketTrend,
        source,
        notes
      } = req.body;

      // 检查是否已存在相同日期、地区、品种的记录
      const existingRecord = await req.db.query(
        'SELECT id FROM goose_prices WHERE date = ? AND region = ? AND breed = ?',
        [date, region, breed]
      );

      if (existingRecord && existingRecord.length > 0) {
        return res.status(409).json({
          success: false,
          message: '该日期、地区和品种的价格记录已存在',
          code: 'PRICE_RECORD_EXISTS'
        });
      }

      // 插入新记录
      const insertResult = await req.db.query(`
        INSERT INTO goose_prices (
          date, region, breed, price_per_kg, market_trend, source, notes,
          status, created_by, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW())
      `, [
        date, region, breed, pricePerKg, marketTrend, source, notes, req.admin.id
      ]);

      // 记录操作日志
      logger.info('Goose price record created', {
        priceId: insertResult.insertId,
        date,
        region,
        breed,
        pricePerKg,
        createdBy: req.admin.id,
        createdByUsername: req.admin.username
      });

      // 获取创建的记录
      const newRecord = await req.db.query(
        'SELECT * FROM goose_prices WHERE id = ?',
        [insertResult.insertId]
      );

      res.status(201).json({
        success: true,
        message: '鹅价记录添加成功',
        data: newRecord[0]
      });

    } catch (error) {
      logger.error('Create goose price error', {
        error: error.message,
        adminId: req.admin.id,
        requestBody: req.body
      });

      res.status(500).json({
        success: false,
        message: '添加鹅价记录失败'
      });
    }
  }
);

/**
 * @route PUT /api/platform/prices/:id
 * @desc 更新鹅价记录
 * @access Private (Platform Admin)
 */
router.put('/:id',
  [
    body('date').optional().isISO8601().withMessage('日期格式无效'),
    body('region').optional().isLength({ min: 2, max: 100 }).withMessage('地区名称长度必须在2-100个字符之间'),
    body('breed').optional().isLength({ min: 2, max: 100 }).withMessage('品种名称长度必须在2-100个字符之间'),
    body('pricePerKg').optional().isFloat({ min: 0.01, max: 9999.99 }).withMessage('价格必须在0.01-9999.99之间'),
    body('marketTrend').optional().isIn(['up', 'down', 'stable']).withMessage('市场趋势只能是up、down或stable'),
    body('source').optional().isLength({ max: 200 }).withMessage('价格来源最多200个字符'),
    body('notes').optional().isLength({ max: 1000 }).withMessage('备注最多1000个字符'),
    body('status').optional().isIn(['active', 'archived']).withMessage('状态只能是active或archived')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const priceId = parseInt(req.params.id);
      if (isNaN(priceId)) {
        return res.status(400).json({
          success: false,
          message: '无效的价格记录ID'
        });
      }

      // 检查记录是否存在
      const existingRecord = await req.db.query(
        'SELECT * FROM goose_prices WHERE id = ?',
        [priceId]
      );

      if (!existingRecord || existingRecord.length === 0) {
        return res.status(404).json({
          success: false,
          message: '价格记录不存在'
        });
      }

      // 构建更新字段
      const updateFields = [];
      const updateValues = [];

      const allowedFields = {
        date: 'date',
        region: 'region',
        breed: 'breed',
        pricePerKg: 'price_per_kg',
        marketTrend: 'market_trend',
        source: 'source',
        notes: 'notes',
        status: 'status'
      };

      Object.keys(req.body).forEach(key => {
        const dbField = allowedFields[key];
        if (dbField && req.body[key] !== undefined) {
          updateFields.push(`${dbField} = ?`);
          updateValues.push(req.body[key]);
        }
      });

      if (updateFields.length === 0) {
        return res.status(400).json({
          success: false,
          message: '没有可更新的字段'
        });
      }

      // 添加更新时间
      updateFields.push('updated_at = NOW()');
      updateValues.push(priceId);

      // 执行更新
      await req.db.query(
        `UPDATE goose_prices SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );

      // 记录操作日志
      logger.info('Goose price record updated', {
        priceId,
        updatedFields: Object.keys(req.body),
        updatedBy: req.admin.id,
        updatedByUsername: req.admin.username
      });

      // 获取更新后的记录
      const updatedRecord = await req.db.query(
        'SELECT * FROM goose_prices WHERE id = ?',
        [priceId]
      );

      res.json({
        success: true,
        message: '鹅价记录更新成功',
        data: updatedRecord[0]
      });

    } catch (error) {
      logger.error('Update goose price error', {
        error: error.message,
        adminId: req.admin.id,
        priceId: req.params.id,
        requestBody: req.body
      });

      res.status(500).json({
        success: false,
        message: '更新鹅价记录失败'
      });
    }
  }
);

/**
 * @route DELETE /api/platform/prices/:id
 * @desc 删除鹅价记录（软删除）
 * @access Private (Platform Admin)
 */
router.delete('/:id', async (req, res) => {
  try {
    const priceId = parseInt(req.params.id);
    if (isNaN(priceId)) {
      return res.status(400).json({
        success: false,
        message: '无效的价格记录ID'
      });
    }

    // 检查记录是否存在
    const record = await req.db.query(
      'SELECT * FROM goose_prices WHERE id = ?',
      [priceId]
    );

    if (!record || record.length === 0) {
      return res.status(404).json({
        success: false,
        message: '价格记录不存在'
      });
    }

    // 软删除（更改状态为archived）
    await req.db.query(
      'UPDATE goose_prices SET status = "archived", updated_at = NOW() WHERE id = ?',
      [priceId]
    );

    // 记录操作日志
    logger.warn('Goose price record deleted (soft delete)', {
      priceId,
      date: record[0].date,
      region: record[0].region,
      breed: record[0].breed,
      deletedBy: req.admin.id,
      deletedByUsername: req.admin.username
    });

    res.json({
      success: true,
      message: '鹅价记录已删除'
    });

  } catch (error) {
    logger.error('Delete goose price error', {
      error: error.message,
      adminId: req.admin.id,
      priceId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '删除鹅价记录失败'
    });
  }
});

/**
 * @route GET /api/platform/prices/stats
 * @desc 获取鹅价统计信息
 * @access Private (Platform Admin)
 */
router.get('/stats', async (req, res) => {
  try {
    // 获取基本统计
    const basicStats = await req.db.query(`
      SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT region) as total_regions,
        COUNT(DISTINCT breed) as total_breeds,
        AVG(price_per_kg) as avg_price,
        MIN(price_per_kg) as min_price,
        MAX(price_per_kg) as max_price
      FROM goose_prices 
      WHERE status = 'active'
    `);

    // 获取最近30天的价格趋势
    const recentTrends = await req.db.query(`
      SELECT 
        DATE(date) as price_date,
        AVG(price_per_kg) as avg_price,
        COUNT(*) as record_count
      FROM goose_prices 
      WHERE status = 'active' AND date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
      GROUP BY DATE(date)
      ORDER BY price_date DESC
      LIMIT 30
    `);

    // 获取各地区平均价格
    const regionStats = await req.db.query(`
      SELECT 
        region,
        AVG(price_per_kg) as avg_price,
        MIN(price_per_kg) as min_price,
        MAX(price_per_kg) as max_price,
        COUNT(*) as record_count
      FROM goose_prices 
      WHERE status = 'active' AND date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
      GROUP BY region
      ORDER BY avg_price DESC
      LIMIT 10
    `);

    // 获取各品种平均价格
    const breedStats = await req.db.query(`
      SELECT 
        breed,
        AVG(price_per_kg) as avg_price,
        MIN(price_per_kg) as min_price,
        MAX(price_per_kg) as max_price,
        COUNT(*) as record_count
      FROM goose_prices 
      WHERE status = 'active' AND date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
      GROUP BY breed
      ORDER BY avg_price DESC
      LIMIT 10
    `);

    res.json({
      success: true,
      message: '获取鹅价统计成功',
      data: {
        overview: basicStats[0],
        recent_trends: recentTrends,
        by_region: regionStats,
        by_breed: breedStats,
        generated_at: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Get goose price stats error', {
      error: error.message,
      adminId: req.admin.id
    });

    res.status(500).json({
      success: false,
      message: '获取鹅价统计失败'
    });
  }
});

module.exports = router;