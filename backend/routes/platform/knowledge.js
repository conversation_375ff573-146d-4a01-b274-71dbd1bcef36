const express = require('express');
const { body, query, validationResult } = require('express-validator');
const winston = require('winston');

const router = express.Router();

// Logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

/**
 * @route GET /api/platform/knowledge
 * @desc 获取知识库文章列表
 * @access Private (Platform Admin)
 */
router.get('/',
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
    query('category').optional().isLength({ max: 100 }).withMessage('分类名称最多100个字符'),
    query('type').optional().isIn(['article', 'video', 'document', 'faq']).withMessage('无效的类型'),
    query('difficulty').optional().isIn(['beginner', 'intermediate', 'advanced']).withMessage('无效的难度级别'),
    query('status').optional().isIn(['draft', 'published', 'archived']).withMessage('无效的状态值'),
    query('search').optional().isLength({ max: 100 }).withMessage('搜索关键词最多100个字符')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        page = 1,
        pageSize = 20,
        category,
        type,
        difficulty,
        status,
        search,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = req.query;

      // 构建查询条件
      const whereClauses = [];
      const params = [];

      if (category) {
        whereClauses.push('category LIKE ?');
        params.push(`%${category}%`);
      }

      if (type) {
        whereClauses.push('type = ?');
        params.push(type);
      }

      if (difficulty) {
        whereClauses.push('difficulty = ?');
        params.push(difficulty);
      }

      if (status) {
        whereClauses.push('status = ?');
        params.push(status);
      }

      if (search) {
        whereClauses.push('(title LIKE ? OR content LIKE ? OR tags LIKE ?)');
        const searchTerm = `%${search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      const whereClause = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

      // 验证排序字段
      const allowedSortFields = ['created_at', 'title', 'view_count', 'like_count', 'category', 'difficulty'];
      const safeSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
      const safeSortOrder = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

      // 构建查询（不包含content字段以提高性能）
      const baseQuery = `
        SELECT 
          id, title, category, tags, type, difficulty, status, view_count, like_count,
          author_id, seo_keywords, created_at, updated_at
        FROM knowledge_base 
        ${whereClause}
        ORDER BY ${safeSortBy} ${safeSortOrder}
      `;

      const countQuery = `SELECT COUNT(*) as total FROM knowledge_base ${whereClause}`;

      // 执行分页查询
      const result = await req.db.paginate(baseQuery, params, parseInt(page), parseInt(pageSize), countQuery);

      // 获取作者信息
      const articlesWithAuthor = await Promise.all(
        result.data.map(async (article) => {
          if (article.author_id) {
            const author = await req.db.query(
              'SELECT username, name FROM platform_admins WHERE id = ?',
              [article.author_id]
            );
            article.author = author[0] || null;
          }
          
          // 解析tags JSON
          if (article.tags) {
            try {
              article.tags = JSON.parse(article.tags);
            } catch (e) {
              article.tags = [];
            }
          }
          
          return article;
        })
      );

      // 获取统计信息
      const stats = await req.db.query(`
        SELECT 
          category,
          type,
          status,
          COUNT(*) as count,
          AVG(view_count) as avg_views,
          SUM(view_count) as total_views
        FROM knowledge_base
        GROUP BY category, type, status
        ORDER BY count DESC
      `);

      res.json({
        success: true,
        message: '获取知识库文章列表成功',
        data: articlesWithAuthor,
        pagination: result.pagination,
        statistics: {
          breakdown: stats
        }
      });

    } catch (error) {
      logger.error('Get knowledge base articles error', {
        error: error.message,
        adminId: req.admin.id,
        query: req.query
      });

      res.status(500).json({
        success: false,
        message: '获取知识库文章列表失败'
      });
    }
  }
);

/**
 * @route GET /api/platform/knowledge/:id
 * @desc 获取知识库文章详情
 * @access Private (Platform Admin) or Public (if published)
 */
router.get('/:id', async (req, res) => {
  try {
    const articleId = parseInt(req.params.id);

    if (isNaN(articleId)) {
      return res.status(400).json({
        success: false,
        message: '无效的文章ID'
      });
    }

    // 获取文章详情
    const article = await req.db.query(`
      SELECT 
        id, title, content, category, tags, type, difficulty, status, view_count, like_count,
        author_id, attachments, seo_keywords, created_at, updated_at
      FROM knowledge_base 
      WHERE id = ?
    `, [articleId]);

    if (!article || article.length === 0) {
      return res.status(404).json({
        success: false,
        message: '文章不存在'
      });
    }

    const articleData = article[0];

    // 如果不是管理员且文章未发布，拒绝访问
    if (!req.admin && articleData.status !== 'published') {
      return res.status(403).json({
        success: false,
        message: '文章未发布',
        code: 'ARTICLE_NOT_PUBLISHED'
      });
    }

    // 获取作者信息
    if (articleData.author_id) {
      const author = await req.db.query(
        'SELECT username, name FROM platform_admins WHERE id = ?',
        [articleData.author_id]
      );
      articleData.author = author[0] || null;
    }

    // 解析JSON字段
    if (articleData.tags) {
      try {
        articleData.tags = JSON.parse(articleData.tags);
      } catch (e) {
        articleData.tags = [];
      }
    }

    if (articleData.attachments) {
      try {
        articleData.attachments = JSON.parse(articleData.attachments);
      } catch (e) {
        articleData.attachments = [];
      }
    }

    // 如果是公开访问（非管理员），增加浏览次数
    if (!req.admin) {
      await req.db.query(
        'UPDATE knowledge_base SET view_count = view_count + 1 WHERE id = ?',
        [articleId]
      );
      articleData.view_count += 1;
    }

    res.json({
      success: true,
      message: '获取文章详情成功',
      data: articleData
    });

  } catch (error) {
    logger.error('Get knowledge base article details error', {
      error: error.message,
      adminId: req.admin?.id,
      articleId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '获取文章详情失败'
    });
  }
});

/**
 * @route POST /api/platform/knowledge
 * @desc 创建知识库文章
 * @access Private (Platform Admin)
 */
router.post('/',
  [
    body('title')
      .notEmpty().withMessage('标题不能为空')
      .isLength({ min: 5, max: 200 }).withMessage('标题长度必须在5-200个字符之间'),
    
    body('content')
      .notEmpty().withMessage('内容不能为空')
      .isLength({ min: 50 }).withMessage('内容至少需要50个字符'),
    
    body('category')
      .notEmpty().withMessage('分类不能为空')
      .isLength({ min: 2, max: 100 }).withMessage('分类名称长度必须在2-100个字符之间'),
    
    body('type')
      .notEmpty().withMessage('类型不能为空')
      .isIn(['article', 'video', 'document', 'faq']).withMessage('无效的文章类型'),
    
    body('difficulty')
      .optional()
      .isIn(['beginner', 'intermediate', 'advanced']).withMessage('无效的难度级别'),
    
    body('status')
      .optional()
      .isIn(['draft', 'published', 'archived']).withMessage('无效的状态值'),
    
    body('tags').optional().isArray().withMessage('标签必须是数组'),
    body('tags.*').optional().isLength({ min: 1, max: 50 }).withMessage('每个标签长度必须在1-50个字符之间'),
    
    body('seoKeywords').optional().isLength({ max: 500 }).withMessage('SEO关键词最多500个字符')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        title,
        content,
        category,
        type,
        difficulty = 'beginner',
        status = 'draft',
        tags = [],
        seoKeywords,
        attachments = []
      } = req.body;

      // 检查标题是否重复
      const existingArticle = await req.db.query(
        'SELECT id FROM knowledge_base WHERE title = ?',
        [title]
      );

      if (existingArticle && existingArticle.length > 0) {
        return res.status(409).json({
          success: false,
          message: '文章标题已存在',
          code: 'TITLE_EXISTS'
        });
      }

      // 插入新文章
      const insertResult = await req.db.query(`
        INSERT INTO knowledge_base (
          title, content, category, tags, type, difficulty, status, view_count, like_count,
          author_id, attachments, seo_keywords, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, 0, ?, ?, ?, NOW(), NOW())
      `, [
        title, content, category, JSON.stringify(tags), type, difficulty, status,
        req.admin.id, JSON.stringify(attachments), seoKeywords
      ]);

      // 记录操作日志
      logger.info('Knowledge base article created', {
        articleId: insertResult.insertId,
        title,
        category,
        type,
        status,
        createdBy: req.admin.id,
        createdByUsername: req.admin.username
      });

      // 获取创建的文章
      const newArticle = await req.db.query(
        'SELECT * FROM knowledge_base WHERE id = ?',
        [insertResult.insertId]
      );

      const articleData = newArticle[0];
      
      // 解析JSON字段用于响应
      if (articleData.tags) {
        try {
          articleData.tags = JSON.parse(articleData.tags);
        } catch (e) {
          articleData.tags = [];
        }
      }

      if (articleData.attachments) {
        try {
          articleData.attachments = JSON.parse(articleData.attachments);
        } catch (e) {
          articleData.attachments = [];
        }
      }

      res.status(201).json({
        success: true,
        message: '知识库文章创建成功',
        data: articleData
      });

    } catch (error) {
      logger.error('Create knowledge base article error', {
        error: error.message,
        adminId: req.admin.id,
        requestBody: req.body
      });

      res.status(500).json({
        success: false,
        message: '创建知识库文章失败'
      });
    }
  }
);

/**
 * @route PUT /api/platform/knowledge/:id
 * @desc 更新知识库文章
 * @access Private (Platform Admin)
 */
router.put('/:id',
  [
    body('title').optional().isLength({ min: 5, max: 200 }).withMessage('标题长度必须在5-200个字符之间'),
    body('content').optional().isLength({ min: 50 }).withMessage('内容至少需要50个字符'),
    body('category').optional().isLength({ min: 2, max: 100 }).withMessage('分类名称长度必须在2-100个字符之间'),
    body('type').optional().isIn(['article', 'video', 'document', 'faq']).withMessage('无效的文章类型'),
    body('difficulty').optional().isIn(['beginner', 'intermediate', 'advanced']).withMessage('无效的难度级别'),
    body('status').optional().isIn(['draft', 'published', 'archived']).withMessage('无效的状态值'),
    body('tags').optional().isArray().withMessage('标签必须是数组'),
    body('tags.*').optional().isLength({ min: 1, max: 50 }).withMessage('每个标签长度必须在1-50个字符之间'),
    body('seoKeywords').optional().isLength({ max: 500 }).withMessage('SEO关键词最多500个字符')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const articleId = parseInt(req.params.id);
      if (isNaN(articleId)) {
        return res.status(400).json({
          success: false,
          message: '无效的文章ID'
        });
      }

      // 检查文章是否存在
      const existingArticle = await req.db.query(
        'SELECT * FROM knowledge_base WHERE id = ?',
        [articleId]
      );

      if (!existingArticle || existingArticle.length === 0) {
        return res.status(404).json({
          success: false,
          message: '文章不存在'
        });
      }

      // 如果更新标题，检查是否重复
      if (req.body.title && req.body.title !== existingArticle[0].title) {
        const duplicateTitle = await req.db.query(
          'SELECT id FROM knowledge_base WHERE title = ? AND id != ?',
          [req.body.title, articleId]
        );

        if (duplicateTitle && duplicateTitle.length > 0) {
          return res.status(409).json({
            success: false,
            message: '文章标题已存在',
            code: 'TITLE_EXISTS'
          });
        }
      }

      // 构建更新字段
      const updateFields = [];
      const updateValues = [];

      const allowedFields = {
        title: 'title',
        content: 'content',
        category: 'category',
        type: 'type',
        difficulty: 'difficulty',
        status: 'status',
        seoKeywords: 'seo_keywords'
      };

      Object.keys(req.body).forEach(key => {
        const dbField = allowedFields[key];
        if (dbField && req.body[key] !== undefined) {
          updateFields.push(`${dbField} = ?`);
          updateValues.push(req.body[key]);
        }
      });

      // 处理特殊字段
      if (req.body.tags !== undefined) {
        updateFields.push('tags = ?');
        updateValues.push(JSON.stringify(req.body.tags));
      }

      if (req.body.attachments !== undefined) {
        updateFields.push('attachments = ?');
        updateValues.push(JSON.stringify(req.body.attachments));
      }

      if (updateFields.length === 0) {
        return res.status(400).json({
          success: false,
          message: '没有可更新的字段'
        });
      }

      // 添加更新时间
      updateFields.push('updated_at = NOW()');
      updateValues.push(articleId);

      // 执行更新
      await req.db.query(
        `UPDATE knowledge_base SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );

      // 记录操作日志
      logger.info('Knowledge base article updated', {
        articleId,
        updatedFields: Object.keys(req.body),
        updatedBy: req.admin.id,
        updatedByUsername: req.admin.username
      });

      // 获取更新后的文章
      const updatedArticle = await req.db.query(
        'SELECT * FROM knowledge_base WHERE id = ?',
        [articleId]
      );

      const articleData = updatedArticle[0];
      
      // 解析JSON字段用于响应
      if (articleData.tags) {
        try {
          articleData.tags = JSON.parse(articleData.tags);
        } catch (e) {
          articleData.tags = [];
        }
      }

      if (articleData.attachments) {
        try {
          articleData.attachments = JSON.parse(articleData.attachments);
        } catch (e) {
          articleData.attachments = [];
        }
      }

      res.json({
        success: true,
        message: '知识库文章更新成功',
        data: articleData
      });

    } catch (error) {
      logger.error('Update knowledge base article error', {
        error: error.message,
        adminId: req.admin.id,
        articleId: req.params.id,
        requestBody: req.body
      });

      res.status(500).json({
        success: false,
        message: '更新知识库文章失败'
      });
    }
  }
);

/**
 * @route DELETE /api/platform/knowledge/:id
 * @desc 删除知识库文章（软删除）
 * @access Private (Platform Admin)
 */
router.delete('/:id', async (req, res) => {
  try {
    const articleId = parseInt(req.params.id);
    if (isNaN(articleId)) {
      return res.status(400).json({
        success: false,
        message: '无效的文章ID'
      });
    }

    // 检查文章是否存在
    const article = await req.db.query(
      'SELECT * FROM knowledge_base WHERE id = ?',
      [articleId]
    );

    if (!article || article.length === 0) {
      return res.status(404).json({
        success: false,
        message: '文章不存在'
      });
    }

    // 软删除（更改状态为archived）
    await req.db.query(
      'UPDATE knowledge_base SET status = "archived", updated_at = NOW() WHERE id = ?',
      [articleId]
    );

    // 记录操作日志
    logger.warn('Knowledge base article deleted (soft delete)', {
      articleId,
      title: article[0].title,
      category: article[0].category,
      deletedBy: req.admin.id,
      deletedByUsername: req.admin.username
    });

    res.json({
      success: true,
      message: '知识库文章已删除'
    });

  } catch (error) {
    logger.error('Delete knowledge base article error', {
      error: error.message,
      adminId: req.admin.id,
      articleId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '删除知识库文章失败'
    });
  }
});

/**
 * @route POST /api/platform/knowledge/:id/like
 * @desc 点赞文章
 * @access Public
 */
router.post('/:id/like', async (req, res) => {
  try {
    const articleId = parseInt(req.params.id);
    if (isNaN(articleId)) {
      return res.status(400).json({
        success: false,
        message: '无效的文章ID'
      });
    }

    // 检查文章是否存在且已发布
    const article = await req.db.query(
      'SELECT id FROM knowledge_base WHERE id = ? AND status = "published"',
      [articleId]
    );

    if (!article || article.length === 0) {
      return res.status(404).json({
        success: false,
        message: '文章不存在或未发布'
      });
    }

    // 增加点赞数
    await req.db.query(
      'UPDATE knowledge_base SET like_count = like_count + 1 WHERE id = ?',
      [articleId]
    );

    // 获取更新后的点赞数
    const updatedArticle = await req.db.query(
      'SELECT like_count FROM knowledge_base WHERE id = ?',
      [articleId]
    );

    res.json({
      success: true,
      message: '点赞成功',
      data: {
        articleId,
        likeCount: updatedArticle[0].like_count
      }
    });

  } catch (error) {
    logger.error('Like knowledge base article error', {
      error: error.message,
      articleId: req.params.id,
      userId: req.user?.id || req.admin?.id
    });

    res.status(500).json({
      success: false,
      message: '点赞失败'
    });
  }
});

/**
 * @route GET /api/platform/knowledge/categories
 * @desc 获取知识库分类列表
 * @access Public
 */
router.get('/categories', async (req, res) => {
  try {
    // 获取所有分类及其文章数量
    const categories = await req.db.query(`
      SELECT 
        category,
        COUNT(*) as article_count,
        COUNT(CASE WHEN status = 'published' THEN 1 END) as published_count
      FROM knowledge_base
      WHERE status != 'archived'
      GROUP BY category
      ORDER BY article_count DESC
    `);

    res.json({
      success: true,
      message: '获取知识库分类成功',
      data: categories
    });

  } catch (error) {
    logger.error('Get knowledge base categories error', {
      error: error.message
    });

    res.status(500).json({
      success: false,
      message: '获取知识库分类失败'
    });
  }
});

/**
 * @route GET /api/platform/knowledge/stats
 * @desc 获取知识库统计信息
 * @access Private (Platform Admin)
 */
router.get('/stats', async (req, res) => {
  try {
    // 获取基本统计
    const basicStats = await req.db.query(`
      SELECT 
        COUNT(*) as total_articles,
        COUNT(CASE WHEN status = 'published' THEN 1 END) as published_articles,
        COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_articles,
        COUNT(DISTINCT category) as total_categories,
        SUM(view_count) as total_views,
        SUM(like_count) as total_likes,
        AVG(view_count) as avg_views_per_article
      FROM knowledge_base
      WHERE status != 'archived'
    `);

    // 获取各分类统计
    const categoryStats = await req.db.query(`
      SELECT 
        category,
        COUNT(*) as article_count,
        SUM(view_count) as total_views,
        SUM(like_count) as total_likes,
        AVG(view_count) as avg_views
      FROM knowledge_base
      WHERE status = 'published'
      GROUP BY category
      ORDER BY total_views DESC
      LIMIT 10
    `);

    // 获取热门文章
    const popularArticles = await req.db.query(`
      SELECT 
        id, title, category, view_count, like_count, created_at
      FROM knowledge_base
      WHERE status = 'published'
      ORDER BY view_count DESC
      LIMIT 10
    `);

    // 获取最新发布的文章
    const recentArticles = await req.db.query(`
      SELECT 
        id, title, category, view_count, like_count, created_at
      FROM knowledge_base
      WHERE status = 'published'
      ORDER BY created_at DESC
      LIMIT 5
    `);

    res.json({
      success: true,
      message: '获取知识库统计成功',
      data: {
        overview: basicStats[0],
        by_category: categoryStats,
        popular_articles: popularArticles,
        recent_articles: recentArticles,
        generated_at: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Get knowledge base stats error', {
      error: error.message,
      adminId: req.admin.id
    });

    res.status(500).json({
      success: false,
      message: '获取知识库统计失败'
    });
  }
});

module.exports = router;