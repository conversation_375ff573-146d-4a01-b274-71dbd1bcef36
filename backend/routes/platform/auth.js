const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const { authService, passwordUtils } = require('../../middleware/auth-unified');
const winston = require('winston');

const router = express.Router();

// Logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

// 登录限流 - 更严格的限制
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 最多5次登录尝试
  message: {
    success: false,
    message: '登录尝试过于频繁，请15分钟后重试',
    code: 'TOO_MANY_LOGIN_ATTEMPTS'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // 基于IP和用户名的限流
    return `${req.ip}-${req.body.username || 'anonymous'}`;
  }
});

// 密码重置限流
const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 最多3次密码重置请求
  message: {
    success: false,
    message: '密码重置请求过于频繁，请1小时后重试',
    code: 'TOO_MANY_RESET_ATTEMPTS'
  }
});

/**
 * @route POST /api/platform/auth/login
 * @desc 平台管理员登录
 * @access Public
 */
router.post('/login', 
  loginLimiter,
  [
    body('username')
      .notEmpty()
      .withMessage('用户名不能为空')
      .isLength({ min: 3, max: 50 })
      .withMessage('用户名长度必须在3-50个字符之间')
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('用户名只能包含字母、数字和下划线'),
    
    body('password')
      .notEmpty()
      .withMessage('密码不能为空')
      .isLength({ min: 6 })
      .withMessage('密码至少6个字符')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const { username, password, rememberMe = false } = req.body;

      // 记录登录尝试
      logger.info('Platform admin login attempt', {
        username,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
      });

      // 认证管理员
      const result = await authService.authenticatePlatformAdmin(
        req.db, 
        username, 
        password
      );

      // 记录成功登录
      logger.info('Platform admin login successful', {
        adminId: result.user.id,
        username: result.user.username,
        role: result.user.role,
        ip: req.ip
      });

      // 设置安全的Cookie选项
      const cookieOptions = {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: rememberMe ? 7 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000 // 7天或1天
      };

      // 设置认证Cookie
      res.cookie('platform_token', result.token, cookieOptions);

      res.json({
        success: true,
        message: '登录成功',
        data: {
          user: result.user,
          expiresIn: result.expiresIn,
          // 不返回token到前端，使用httpOnly cookie
          permissions: result.user.permissions || []
        }
      });

    } catch (error) {
      // 记录登录失败
      logger.error('Platform admin login failed', {
        username: req.body.username,
        ip: req.ip,
        error: error.message,
        timestamp: new Date().toISOString()
      });

      // 根据错误类型返回不同状态码
      if (error.message.includes('用户名或密码错误')) {
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误',
          code: 'INVALID_CREDENTIALS'
        });
      }

      if (error.message.includes('账户已被停用')) {
        return res.status(403).json({
          success: false,
          message: '账户已被停用，请联系超级管理员',
          code: 'ACCOUNT_DISABLED'
        });
      }

      res.status(500).json({
        success: false,
        message: '登录过程中发生错误',
        code: 'LOGIN_ERROR'
      });
    }
  }
);

/**
 * @route POST /api/platform/auth/logout
 * @desc 平台管理员登出
 * @access Private
 */
router.post('/logout', async (req, res) => {
  try {
    // 清除认证Cookie
    res.clearCookie('platform_token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });

    // 记录登出
    logger.info('Platform admin logout', {
      ip: req.ip,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: '已安全登出'
    });

  } catch (error) {
    logger.error('Logout error', {
      error: error.message,
      ip: req.ip
    });

    res.status(500).json({
      success: false,
      message: '登出过程中发生错误'
    });
  }
});

/**
 * @route GET /api/platform/auth/profile
 * @desc 获取当前管理员信息
 * @access Private
 */
router.get('/profile', async (req, res) => {
  try {
    // 这里需要从token中获取管理员信息
    // 由于使用httpOnly cookie，需要在中间件中解析
    
    res.json({
      success: true,
      message: '获取用户信息成功',
      data: {
        // 这里将由认证中间件填充
        user: req.admin || null
      }
    });

  } catch (error) {
    logger.error('Get profile error', {
      error: error.message,
      ip: req.ip
    });

    res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    });
  }
});

/**
 * @route POST /api/platform/auth/change-password
 * @desc 修改密码
 * @access Private
 */
router.post('/change-password',
  [
    body('currentPassword')
      .notEmpty()
      .withMessage('当前密码不能为空'),
    
    body('newPassword')
      .isLength({ min: 8 })
      .withMessage('新密码至少需要8个字符')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])/)
      .withMessage('新密码必须包含大小写字母、数字和特殊字符'),
    
    body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.newPassword) {
          throw new Error('确认密码不匹配');
        }
        return true;
      })
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const { currentPassword, newPassword } = req.body;
      const adminId = req.admin.id; // 从认证中间件获取

      // 验证当前密码
      const admin = await req.db.query(
        'SELECT password FROM platform_admins WHERE id = ?',
        [adminId]
      );

      if (!admin || !Array.isArray(admin) || admin.length === 0) {
        return res.status(404).json({
          success: false,
          message: '管理员不存在'
        });
      }

      const isCurrentPasswordValid = await passwordUtils.compare(
        currentPassword, 
        admin[0].password
      );

      if (!isCurrentPasswordValid) {
        return res.status(400).json({
          success: false,
          message: '当前密码不正确',
          code: 'INVALID_CURRENT_PASSWORD'
        });
      }

      // 加密新密码
      const hashedNewPassword = await passwordUtils.hash(newPassword);

      // 更新密码
      await req.db.query(
        'UPDATE platform_admins SET password = ?, updated_at = NOW() WHERE id = ?',
        [hashedNewPassword, adminId]
      );

      // 记录密码修改
      logger.info('Platform admin password changed', {
        adminId,
        username: req.admin.username,
        ip: req.ip,
        timestamp: new Date().toISOString()
      });

      res.json({
        success: true,
        message: '密码修改成功'
      });

    } catch (error) {
      logger.error('Change password error', {
        adminId: req.admin?.id,
        error: error.message,
        ip: req.ip
      });

      res.status(500).json({
        success: false,
        message: '密码修改失败'
      });
    }
  }
);

/**
 * @route POST /api/platform/auth/reset-password-request
 * @desc 请求密码重置
 * @access Public
 */
router.post('/reset-password-request',
  passwordResetLimiter,
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('请输入有效的邮箱地址')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const { email } = req.body;

      // 查找管理员
      const admin = await req.db.query(
        'SELECT id, username, email FROM platform_admins WHERE email = ? AND status = "active"',
        [email]
      );

      // 即使找不到用户也返回成功，避免邮箱枚举攻击
      if (!admin || !Array.isArray(admin) || admin.length === 0) {
        logger.warn('Password reset requested for non-existent email', {
          email,
          ip: req.ip
        });
        
        return res.json({
          success: true,
          message: '如果邮箱存在，重置链接已发送到您的邮箱'
        });
      }

      // TODO: 实现邮件发送功能
      // 生成重置令牌
      // 发送重置邮件

      logger.info('Password reset requested', {
        adminId: admin[0].id,
        email,
        ip: req.ip
      });

      res.json({
        success: true,
        message: '密码重置链接已发送到您的邮箱'
      });

    } catch (error) {
      logger.error('Password reset request error', {
        error: error.message,
        ip: req.ip
      });

      res.status(500).json({
        success: false,
        message: '密码重置请求失败'
      });
    }
  }
);

/**
 * @route GET /api/platform/auth/verify-token
 * @desc 验证token有效性
 * @access Private
 */
router.get('/verify-token', async (req, res) => {
  try {
    // 如果能到达这里，说明token有效（通过认证中间件验证）
    res.json({
      success: true,
      message: 'Token有效',
      data: {
        user: req.admin,
        isValid: true
      }
    });

  } catch (error) {
    res.status(401).json({
      success: false,
      message: 'Token无效',
      isValid: false
    });
  }
});

module.exports = router;