const express = require('express');
const { body, query, validationResult } = require('express-validator');
const winston = require('winston');

const router = express.Router();

// Logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

/**
 * @route GET /api/platform/ai-configs
 * @desc 获取AI配置列表
 * @access Private (Platform Admin)
 */
router.get('/',
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
    query('provider').optional().isIn(['openai', 'azure', 'claude', 'gemini', 'qwen', 'custom']).withMessage('无效的AI提供商'),
    query('status').optional().isIn(['active', 'inactive', 'testing']).withMessage('无效的状态值'),
    query('search').optional().isLength({ max: 100 }).withMessage('搜索关键词最多100个字符')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        page = 1,
        pageSize = 20,
        provider,
        status,
        search,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = req.query;

      // 构建查询条件
      const whereClauses = [];
      const params = [];

      if (provider) {
        whereClauses.push('provider = ?');
        params.push(provider);
      }

      if (status) {
        whereClauses.push('status = ?');
        params.push(status);
      }

      if (search) {
        whereClauses.push('(name LIKE ? OR description LIKE ? OR model_name LIKE ?)');
        const searchTerm = `%${search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      const whereClause = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

      // 验证排序字段
      const allowedSortFields = ['created_at', 'name', 'provider', 'status', 'priority', 'usage_count'];
      const safeSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
      const safeSortOrder = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

      // 构建查询（隐藏敏感信息）
      const baseQuery = `
        SELECT 
          id, name, description, provider, model_name, status, priority, usage_count,
          rate_limit_per_minute, rate_limit_per_hour, rate_limit_per_day,
          cost_per_token, max_tokens, temperature_default, temperature_range,
          features, is_default, created_at, updated_at
        FROM ai_configs 
        ${whereClause}
        ORDER BY ${safeSortBy} ${safeSortOrder}
      `;

      const countQuery = `SELECT COUNT(*) as total FROM ai_configs ${whereClause}`;

      // 执行分页查询
      const result = await req.db.paginate(baseQuery, params, parseInt(page), parseInt(pageSize), countQuery);

      // 处理JSON字段
      const configsWithProcessedData = result.data.map(config => {
        // 解析features JSON
        if (config.features) {
          try {
            config.features = JSON.parse(config.features);
          } catch (e) {
            config.features = [];
          }
        }
        
        // 解析temperature_range JSON
        if (config.temperature_range) {
          try {
            config.temperature_range = JSON.parse(config.temperature_range);
          } catch (e) {
            config.temperature_range = { min: 0, max: 1 };
          }
        }
        
        return config;
      });

      // 获取统计信息
      const stats = await req.db.query(`
        SELECT 
          provider,
          status,
          COUNT(*) as count,
          SUM(usage_count) as total_usage,
          AVG(cost_per_token) as avg_cost
        FROM ai_configs
        GROUP BY provider, status
        ORDER BY count DESC
      `);

      res.json({
        success: true,
        message: '获取AI配置列表成功',
        data: configsWithProcessedData,
        pagination: result.pagination,
        statistics: {
          breakdown: stats
        }
      });

    } catch (error) {
      logger.error('Get AI configs error', {
        error: error.message,
        adminId: req.admin.id,
        query: req.query
      });

      res.status(500).json({
        success: false,
        message: '获取AI配置列表失败'
      });
    }
  }
);

/**
 * @route GET /api/platform/ai-configs/:id
 * @desc 获取AI配置详情
 * @access Private (Platform Admin)
 */
router.get('/:id', async (req, res) => {
  try {
    const configId = parseInt(req.params.id);

    if (isNaN(configId)) {
      return res.status(400).json({
        success: false,
        message: '无效的配置ID'
      });
    }

    // 获取AI配置详情（包含敏感信息）
    const config = await req.db.query(`
      SELECT 
        id, name, description, provider, model_name, api_endpoint, api_key,
        status, priority, usage_count, rate_limit_per_minute, rate_limit_per_hour,
        rate_limit_per_day, cost_per_token, max_tokens, temperature_default,
        temperature_range, features, custom_headers, timeout_seconds, retry_config,
        is_default, health_check_url, created_at, updated_at
      FROM ai_configs 
      WHERE id = ?
    `, [configId]);

    if (!config || config.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'AI配置不存在'
      });
    }

    const configData = config[0];

    // 解析JSON字段
    if (configData.features) {
      try {
        configData.features = JSON.parse(configData.features);
      } catch (e) {
        configData.features = [];
      }
    }

    if (configData.temperature_range) {
      try {
        configData.temperature_range = JSON.parse(configData.temperature_range);
      } catch (e) {
        configData.temperature_range = { min: 0, max: 1 };
      }
    }

    if (configData.custom_headers) {
      try {
        configData.custom_headers = JSON.parse(configData.custom_headers);
      } catch (e) {
        configData.custom_headers = {};
      }
    }

    if (configData.retry_config) {
      try {
        configData.retry_config = JSON.parse(configData.retry_config);
      } catch (e) {
        configData.retry_config = {};
      }
    }

    // 敏感信息处理（显示前几位和后几位）
    if (configData.api_key) {
      const key = configData.api_key;
      if (key.length > 8) {
        configData.api_key_masked = key.substring(0, 4) + '*'.repeat(key.length - 8) + key.substring(key.length - 4);
      } else {
        configData.api_key_masked = '*'.repeat(key.length);
      }
    }

    res.json({
      success: true,
      message: '获取AI配置详情成功',
      data: configData
    });

  } catch (error) {
    logger.error('Get AI config details error', {
      error: error.message,
      adminId: req.admin.id,
      configId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '获取AI配置详情失败'
    });
  }
});

/**
 * @route POST /api/platform/ai-configs
 * @desc 创建AI配置
 * @access Private (Platform Admin)
 */
router.post('/',
  [
    body('name')
      .notEmpty().withMessage('配置名称不能为空')
      .isLength({ min: 2, max: 100 }).withMessage('配置名称长度必须在2-100个字符之间'),
    
    body('description')
      .optional()
      .isLength({ max: 500 }).withMessage('描述最多500个字符'),
    
    body('provider')
      .notEmpty().withMessage('AI提供商不能为空')
      .isIn(['openai', 'azure', 'claude', 'gemini', 'qwen', 'custom']).withMessage('无效的AI提供商'),
    
    body('modelName')
      .notEmpty().withMessage('模型名称不能为空')
      .isLength({ min: 2, max: 100 }).withMessage('模型名称长度必须在2-100个字符之间'),
    
    body('apiEndpoint')
      .notEmpty().withMessage('API端点不能为空')
      .isURL().withMessage('API端点必须是有效的URL'),
    
    body('apiKey')
      .notEmpty().withMessage('API密钥不能为空')
      .isLength({ min: 8 }).withMessage('API密钥至少8个字符'),
    
    body('rateLimitPerMinute').optional().isInt({ min: 1 }).withMessage('每分钟限制必须是正整数'),
    body('rateLimitPerHour').optional().isInt({ min: 1 }).withMessage('每小时限制必须是正整数'),
    body('rateLimitPerDay').optional().isInt({ min: 1 }).withMessage('每天限制必须是正整数'),
    body('costPerToken').optional().isFloat({ min: 0 }).withMessage('每Token成本必须大于等于0'),
    body('maxTokens').optional().isInt({ min: 1 }).withMessage('最大Token数必须是正整数'),
    body('temperatureDefault').optional().isFloat({ min: 0, max: 2 }).withMessage('默认温度必须在0-2之间'),
    body('priority').optional().isInt({ min: 1, max: 10 }).withMessage('优先级必须在1-10之间'),
    body('timeoutSeconds').optional().isInt({ min: 1, max: 300 }).withMessage('超时时间必须在1-300秒之间')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        name,
        description,
        provider,
        modelName,
        apiEndpoint,
        apiKey,
        rateLimitPerMinute = 60,
        rateLimitPerHour = 1000,
        rateLimitPerDay = 10000,
        costPerToken = 0.0001,
        maxTokens = 4096,
        temperatureDefault = 0.7,
        temperatureRange = { min: 0, max: 2 },
        features = [],
        customHeaders = {},
        timeoutSeconds = 30,
        retryConfig = { maxRetries: 3, backoffMs: 1000 },
        priority = 5,
        healthCheckUrl,
        isDefault = false
      } = req.body;

      // 检查配置名称是否重复
      const existingConfig = await req.db.query(
        'SELECT id FROM ai_configs WHERE name = ?',
        [name]
      );

      if (existingConfig && existingConfig.length > 0) {
        return res.status(409).json({
          success: false,
          message: 'AI配置名称已存在',
          code: 'CONFIG_NAME_EXISTS'
        });
      }

      // 如果设置为默认配置，先取消其他默认配置
      if (isDefault) {
        await req.db.query(
          'UPDATE ai_configs SET is_default = false WHERE provider = ?',
          [provider]
        );
      }

      // 插入新配置
      const insertResult = await req.db.query(`
        INSERT INTO ai_configs (
          name, description, provider, model_name, api_endpoint, api_key,
          status, priority, usage_count, rate_limit_per_minute, rate_limit_per_hour,
          rate_limit_per_day, cost_per_token, max_tokens, temperature_default,
          temperature_range, features, custom_headers, timeout_seconds, retry_config,
          is_default, health_check_url, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, 'testing', ?, 0, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        name, description, provider, modelName, apiEndpoint, apiKey,
        priority, rateLimitPerMinute, rateLimitPerHour, rateLimitPerDay,
        costPerToken, maxTokens, temperatureDefault,
        JSON.stringify(temperatureRange), JSON.stringify(features),
        JSON.stringify(customHeaders), timeoutSeconds, JSON.stringify(retryConfig),
        isDefault, healthCheckUrl
      ]);

      // 记录操作日志
      logger.info('AI config created', {
        configId: insertResult.insertId,
        name,
        provider,
        modelName,
        createdBy: req.admin.id,
        createdByUsername: req.admin.username
      });

      // 获取创建的配置（不包含敏感信息）
      const newConfig = await req.db.query(`
        SELECT 
          id, name, description, provider, model_name, status, priority,
          rate_limit_per_minute, rate_limit_per_hour, rate_limit_per_day,
          cost_per_token, max_tokens, temperature_default, temperature_range,
          features, is_default, created_at, updated_at
        FROM ai_configs WHERE id = ?
      `, [insertResult.insertId]);

      const configData = newConfig[0];
      
      // 解析JSON字段用于响应
      if (configData.features) {
        try {
          configData.features = JSON.parse(configData.features);
        } catch (e) {
          configData.features = [];
        }
      }

      if (configData.temperature_range) {
        try {
          configData.temperature_range = JSON.parse(configData.temperature_range);
        } catch (e) {
          configData.temperature_range = { min: 0, max: 1 };
        }
      }

      res.status(201).json({
        success: true,
        message: 'AI配置创建成功',
        data: configData
      });

    } catch (error) {
      logger.error('Create AI config error', {
        error: error.message,
        adminId: req.admin.id,
        requestBody: { ...req.body, apiKey: '[HIDDEN]' }
      });

      res.status(500).json({
        success: false,
        message: '创建AI配置失败'
      });
    }
  }
);

/**
 * @route PUT /api/platform/ai-configs/:id
 * @desc 更新AI配置
 * @access Private (Platform Admin)
 */
router.put('/:id',
  [
    body('name').optional().isLength({ min: 2, max: 100 }).withMessage('配置名称长度必须在2-100个字符之间'),
    body('description').optional().isLength({ max: 500 }).withMessage('描述最多500个字符'),
    body('provider').optional().isIn(['openai', 'azure', 'claude', 'gemini', 'qwen', 'custom']).withMessage('无效的AI提供商'),
    body('modelName').optional().isLength({ min: 2, max: 100 }).withMessage('模型名称长度必须在2-100个字符之间'),
    body('apiEndpoint').optional().isURL().withMessage('API端点必须是有效的URL'),
    body('apiKey').optional().isLength({ min: 8 }).withMessage('API密钥至少8个字符'),
    body('status').optional().isIn(['active', 'inactive', 'testing']).withMessage('无效的状态值'),
    body('rateLimitPerMinute').optional().isInt({ min: 1 }).withMessage('每分钟限制必须是正整数'),
    body('rateLimitPerHour').optional().isInt({ min: 1 }).withMessage('每小时限制必须是正整数'),
    body('rateLimitPerDay').optional().isInt({ min: 1 }).withMessage('每天限制必须是正整数'),
    body('costPerToken').optional().isFloat({ min: 0 }).withMessage('每Token成本必须大于等于0'),
    body('maxTokens').optional().isInt({ min: 1 }).withMessage('最大Token数必须是正整数'),
    body('temperatureDefault').optional().isFloat({ min: 0, max: 2 }).withMessage('默认温度必须在0-2之间'),
    body('priority').optional().isInt({ min: 1, max: 10 }).withMessage('优先级必须在1-10之间'),
    body('timeoutSeconds').optional().isInt({ min: 1, max: 300 }).withMessage('超时时间必须在1-300秒之间')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const configId = parseInt(req.params.id);
      if (isNaN(configId)) {
        return res.status(400).json({
          success: false,
          message: '无效的配置ID'
        });
      }

      // 检查配置是否存在
      const existingConfig = await req.db.query(
        'SELECT * FROM ai_configs WHERE id = ?',
        [configId]
      );

      if (!existingConfig || existingConfig.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'AI配置不存在'
        });
      }

      // 如果更新配置名称，检查是否重复
      if (req.body.name && req.body.name !== existingConfig[0].name) {
        const duplicateName = await req.db.query(
          'SELECT id FROM ai_configs WHERE name = ? AND id != ?',
          [req.body.name, configId]
        );

        if (duplicateName && duplicateName.length > 0) {
          return res.status(409).json({
            success: false,
            message: 'AI配置名称已存在',
            code: 'CONFIG_NAME_EXISTS'
          });
        }
      }

      // 如果设置为默认配置，先取消其他默认配置
      if (req.body.isDefault === true) {
        const provider = req.body.provider || existingConfig[0].provider;
        await req.db.query(
          'UPDATE ai_configs SET is_default = false WHERE provider = ? AND id != ?',
          [provider, configId]
        );
      }

      // 构建更新字段
      const updateFields = [];
      const updateValues = [];

      const allowedFields = {
        name: 'name',
        description: 'description',
        provider: 'provider',
        modelName: 'model_name',
        apiEndpoint: 'api_endpoint',
        apiKey: 'api_key',
        status: 'status',
        priority: 'priority',
        rateLimitPerMinute: 'rate_limit_per_minute',
        rateLimitPerHour: 'rate_limit_per_hour',
        rateLimitPerDay: 'rate_limit_per_day',
        costPerToken: 'cost_per_token',
        maxTokens: 'max_tokens',
        temperatureDefault: 'temperature_default',
        timeoutSeconds: 'timeout_seconds',
        isDefault: 'is_default',
        healthCheckUrl: 'health_check_url'
      };

      Object.keys(req.body).forEach(key => {
        const dbField = allowedFields[key];
        if (dbField && req.body[key] !== undefined) {
          updateFields.push(`${dbField} = ?`);
          updateValues.push(req.body[key]);
        }
      });

      // 处理特殊字段
      if (req.body.temperatureRange !== undefined) {
        updateFields.push('temperature_range = ?');
        updateValues.push(JSON.stringify(req.body.temperatureRange));
      }

      if (req.body.features !== undefined) {
        updateFields.push('features = ?');
        updateValues.push(JSON.stringify(req.body.features));
      }

      if (req.body.customHeaders !== undefined) {
        updateFields.push('custom_headers = ?');
        updateValues.push(JSON.stringify(req.body.customHeaders));
      }

      if (req.body.retryConfig !== undefined) {
        updateFields.push('retry_config = ?');
        updateValues.push(JSON.stringify(req.body.retryConfig));
      }

      if (updateFields.length === 0) {
        return res.status(400).json({
          success: false,
          message: '没有可更新的字段'
        });
      }

      // 添加更新时间
      updateFields.push('updated_at = NOW()');
      updateValues.push(configId);

      // 执行更新
      await req.db.query(
        `UPDATE ai_configs SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );

      // 记录操作日志
      logger.info('AI config updated', {
        configId,
        updatedFields: Object.keys(req.body),
        updatedBy: req.admin.id,
        updatedByUsername: req.admin.username
      });

      // 获取更新后的配置（不包含敏感信息）
      const updatedConfig = await req.db.query(`
        SELECT 
          id, name, description, provider, model_name, status, priority,
          rate_limit_per_minute, rate_limit_per_hour, rate_limit_per_day,
          cost_per_token, max_tokens, temperature_default, temperature_range,
          features, is_default, created_at, updated_at
        FROM ai_configs WHERE id = ?
      `, [configId]);

      const configData = updatedConfig[0];
      
      // 解析JSON字段用于响应
      if (configData.features) {
        try {
          configData.features = JSON.parse(configData.features);
        } catch (e) {
          configData.features = [];
        }
      }

      if (configData.temperature_range) {
        try {
          configData.temperature_range = JSON.parse(configData.temperature_range);
        } catch (e) {
          configData.temperature_range = { min: 0, max: 1 };
        }
      }

      res.json({
        success: true,
        message: 'AI配置更新成功',
        data: configData
      });

    } catch (error) {
      logger.error('Update AI config error', {
        error: error.message,
        adminId: req.admin.id,
        configId: req.params.id,
        requestBody: { ...req.body, apiKey: req.body.apiKey ? '[HIDDEN]' : undefined }
      });

      res.status(500).json({
        success: false,
        message: '更新AI配置失败'
      });
    }
  }
);

/**
 * @route DELETE /api/platform/ai-configs/:id
 * @desc 删除AI配置
 * @access Private (Platform Admin)
 */
router.delete('/:id', async (req, res) => {
  try {
    const configId = parseInt(req.params.id);
    if (isNaN(configId)) {
      return res.status(400).json({
        success: false,
        message: '无效的配置ID'
      });
    }

    // 检查配置是否存在
    const config = await req.db.query(
      'SELECT * FROM ai_configs WHERE id = ?',
      [configId]
    );

    if (!config || config.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'AI配置不存在'
      });
    }

    // 检查是否是默认配置
    if (config[0].is_default) {
      return res.status(400).json({
        success: false,
        message: '不能删除默认AI配置',
        code: 'CANNOT_DELETE_DEFAULT_CONFIG'
      });
    }

    // 检查配置是否正在使用中
    if (config[0].usage_count > 0 && config[0].status === 'active') {
      return res.status(400).json({
        success: false,
        message: 'AI配置正在使用中，请先停用后再删除',
        code: 'CONFIG_IN_USE'
      });
    }

    // 删除配置
    await req.db.query(
      'DELETE FROM ai_configs WHERE id = ?',
      [configId]
    );

    // 记录操作日志
    logger.warn('AI config deleted', {
      configId,
      name: config[0].name,
      provider: config[0].provider,
      deletedBy: req.admin.id,
      deletedByUsername: req.admin.username
    });

    res.json({
      success: true,
      message: 'AI配置已删除'
    });

  } catch (error) {
    logger.error('Delete AI config error', {
      error: error.message,
      adminId: req.admin.id,
      configId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '删除AI配置失败'
    });
  }
});

/**
 * @route POST /api/platform/ai-configs/:id/test
 * @desc 测试AI配置连接
 * @access Private (Platform Admin)
 */
router.post('/:id/test', async (req, res) => {
  try {
    const configId = parseInt(req.params.id);

    if (isNaN(configId)) {
      return res.status(400).json({
        success: false,
        message: '无效的配置ID'
      });
    }

    // 获取配置信息
    const config = await req.db.query(
      'SELECT * FROM ai_configs WHERE id = ?',
      [configId]
    );

    if (!config || config.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'AI配置不存在'
      });
    }

    const configData = config[0];

    // TODO: 实现实际的AI服务连接测试
    // 这里应该根据不同的provider调用相应的API进行测试
    
    // 模拟测试结果
    const testResult = {
      success: true,
      responseTime: Math.floor(Math.random() * 1000) + 200, // 200-1200ms
      provider: configData.provider,
      model: configData.model_name,
      apiEndpoint: configData.api_endpoint,
      testMessage: 'AI服务连接测试成功',
      timestamp: new Date().toISOString()
    };

    // 记录测试结果
    logger.info('AI config test performed', {
      configId,
      name: configData.name,
      provider: configData.provider,
      testResult: testResult.success,
      responseTime: testResult.responseTime,
      testedBy: req.admin.id,
      testedByUsername: req.admin.username
    });

    res.json({
      success: true,
      message: 'AI配置测试完成',
      data: testResult
    });

  } catch (error) {
    logger.error('Test AI config error', {
      error: error.message,
      adminId: req.admin.id,
      configId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: 'AI配置测试失败'
    });
  }
});

/**
 * @route GET /api/platform/ai-configs/providers/available
 * @desc 获取可用的AI提供商列表
 * @access Private (Platform Admin)
 */
router.get('/providers/available', async (req, res) => {
  try {
    const providers = [
      {
        id: 'openai',
        name: 'OpenAI',
        description: 'OpenAI GPT系列模型',
        supportedModels: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'gpt-4o'],
        features: ['text-generation', 'conversation', 'code-generation', 'analysis'],
        defaultEndpoint: 'https://api.openai.com/v1',
        documentation: 'https://platform.openai.com/docs'
      },
      {
        id: 'azure',
        name: 'Azure OpenAI',
        description: 'Microsoft Azure OpenAI服务',
        supportedModels: ['gpt-35-turbo', 'gpt-4', 'gpt-4-turbo'],
        features: ['text-generation', 'conversation', 'enterprise-grade'],
        defaultEndpoint: 'https://your-resource.openai.azure.com',
        documentation: 'https://docs.microsoft.com/en-us/azure/cognitive-services/openai'
      },
      {
        id: 'claude',
        name: 'Anthropic Claude',
        description: 'Anthropic Claude AI助手',
        supportedModels: ['claude-3-haiku', 'claude-3-sonnet', 'claude-3-opus'],
        features: ['text-generation', 'conversation', 'analysis', 'coding'],
        defaultEndpoint: 'https://api.anthropic.com/v1',
        documentation: 'https://docs.anthropic.com'
      },
      {
        id: 'gemini',
        name: 'Google Gemini',
        description: 'Google Gemini AI模型',
        supportedModels: ['gemini-pro', 'gemini-pro-vision', 'gemini-ultra'],
        features: ['text-generation', 'multimodal', 'vision'],
        defaultEndpoint: 'https://generativelanguage.googleapis.com/v1',
        documentation: 'https://cloud.google.com/vertex-ai/docs'
      },
      {
        id: 'qwen',
        name: '通义千问',
        description: '阿里云通义千问大模型',
        supportedModels: ['qwen-turbo', 'qwen-plus', 'qwen-max'],
        features: ['text-generation', 'conversation', 'chinese-optimized'],
        defaultEndpoint: 'https://dashscope.aliyuncs.com/api/v1',
        documentation: 'https://help.aliyun.com/document_detail/2712568.html'
      },
      {
        id: 'custom',
        name: '自定义API',
        description: '兼容OpenAI API格式的自定义服务',
        supportedModels: ['custom-model'],
        features: ['text-generation', 'flexible'],
        defaultEndpoint: 'https://your-custom-api.com/v1',
        documentation: 'https://platform.openai.com/docs/api-reference'
      }
    ];

    // 获取当前已配置的提供商统计
    const configuredStats = await req.db.query(`
      SELECT 
        provider,
        COUNT(*) as config_count,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_count
      FROM ai_configs
      GROUP BY provider
    `);

    // 为每个提供商添加配置统计
    const providersWithStats = providers.map(provider => {
      const stats = configuredStats.find(s => s.provider === provider.id);
      return {
        ...provider,
        configCount: stats ? stats.config_count : 0,
        activeCount: stats ? stats.active_count : 0
      };
    });

    res.json({
      success: true,
      message: '获取可用AI提供商成功',
      data: providersWithStats
    });

  } catch (error) {
    logger.error('Get available AI providers error', {
      error: error.message,
      adminId: req.admin.id
    });

    res.status(500).json({
      success: false,
      message: '获取可用AI提供商失败'
    });
  }
});

module.exports = router;