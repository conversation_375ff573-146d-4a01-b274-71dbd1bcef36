const express = require('express');
const { body, query, validationResult } = require('express-validator');
const winston = require('winston');

const router = express.Router();

// Logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

/**
 * @route GET /api/platform/announcements
 * @desc 获取公告列表
 * @access Private (Platform Admin)
 */
router.get('/',
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
    query('type').optional().isIn(['system', 'maintenance', 'feature', 'promotion', 'warning']).withMessage('无效的公告类型'),
    query('status').optional().isIn(['draft', 'published', 'expired', 'archived']).withMessage('无效的状态值'),
    query('targetType').optional().isIn(['all', 'tenant', 'subscription_plan']).withMessage('无效的目标类型'),
    query('search').optional().isLength({ max: 100 }).withMessage('搜索关键词最多100个字符')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        page = 1,
        pageSize = 20,
        type,
        status,
        targetType,
        search,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = req.query;

      // 构建查询条件
      const whereClauses = [];
      const params = [];

      if (type) {
        whereClauses.push('type = ?');
        params.push(type);
      }

      if (status) {
        whereClauses.push('status = ?');
        params.push(status);
      }

      if (targetType) {
        whereClauses.push('target_type = ?');
        params.push(targetType);
      }

      if (search) {
        whereClauses.push('(title LIKE ? OR content LIKE ?)');
        const searchTerm = `%${search}%`;
        params.push(searchTerm, searchTerm);
      }

      const whereClause = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

      // 验证排序字段
      const allowedSortFields = ['created_at', 'title', 'type', 'status', 'publish_time', 'expire_time', 'priority'];
      const safeSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
      const safeSortOrder = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

      // 构建查询
      const baseQuery = `
        SELECT 
          id, title, content, type, status, target_type, target_value, priority,
          publish_time, expire_time, sticky, created_by, created_at, updated_at
        FROM platform_announcements 
        ${whereClause}
        ORDER BY sticky DESC, ${safeSortBy} ${safeSortOrder}
      `;

      const countQuery = `SELECT COUNT(*) as total FROM platform_announcements ${whereClause}`;

      // 执行分页查询
      const result = await req.db.paginate(baseQuery, params, parseInt(page), parseInt(pageSize), countQuery);

      // 获取创建人信息
      const announcementsWithCreator = await Promise.all(
        result.data.map(async (announcement) => {
          if (announcement.created_by) {
            const creator = await req.db.query(
              'SELECT username, name FROM platform_admins WHERE id = ?',
              [announcement.created_by]
            );
            announcement.creator = creator[0] || null;
          }
          
          // 解析target_value JSON
          if (announcement.target_value) {
            try {
              announcement.target_value = JSON.parse(announcement.target_value);
            } catch (e) {
              announcement.target_value = null;
            }
          }
          
          return announcement;
        })
      );

      // 获取统计信息
      const stats = await req.db.query(`
        SELECT 
          status,
          type,
          COUNT(*) as count
        FROM platform_announcements
        GROUP BY status, type
        ORDER BY count DESC
      `);

      res.json({
        success: true,
        message: '获取公告列表成功',
        data: announcementsWithCreator,
        pagination: result.pagination,
        statistics: {
          breakdown: stats
        }
      });

    } catch (error) {
      logger.error('Get announcements error', {
        error: error.message,
        adminId: req.admin.id,
        query: req.query
      });

      res.status(500).json({
        success: false,
        message: '获取公告列表失败'
      });
    }
  }
);

/**
 * @route GET /api/platform/announcements/:id
 * @desc 获取公告详情
 * @access Private (Platform Admin)
 */
router.get('/:id', async (req, res) => {
  try {
    const announcementId = parseInt(req.params.id);

    if (isNaN(announcementId)) {
      return res.status(400).json({
        success: false,
        message: '无效的公告ID'
      });
    }

    // 获取公告详情
    const announcement = await req.db.query(`
      SELECT 
        id, title, content, type, status, target_type, target_value, priority,
        publish_time, expire_time, sticky, created_by, created_at, updated_at
      FROM platform_announcements 
      WHERE id = ?
    `, [announcementId]);

    if (!announcement || announcement.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }

    const announcementData = announcement[0];

    // 获取创建人信息
    if (announcementData.created_by) {
      const creator = await req.db.query(
        'SELECT username, name FROM platform_admins WHERE id = ?',
        [announcementData.created_by]
      );
      announcementData.creator = creator[0] || null;
    }

    // 解析target_value JSON
    if (announcementData.target_value) {
      try {
        announcementData.target_value = JSON.parse(announcementData.target_value);
      } catch (e) {
        announcementData.target_value = null;
      }
    }

    res.json({
      success: true,
      message: '获取公告详情成功',
      data: announcementData
    });

  } catch (error) {
    logger.error('Get announcement details error', {
      error: error.message,
      adminId: req.admin.id,
      announcementId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '获取公告详情失败'
    });
  }
});

/**
 * @route POST /api/platform/announcements
 * @desc 创建公告
 * @access Private (Platform Admin)
 */
router.post('/',
  [
    body('title')
      .notEmpty().withMessage('标题不能为空')
      .isLength({ min: 5, max: 200 }).withMessage('标题长度必须在5-200个字符之间'),
    
    body('content')
      .notEmpty().withMessage('内容不能为空')
      .isLength({ min: 10 }).withMessage('内容至少需要10个字符'),
    
    body('type')
      .notEmpty().withMessage('类型不能为空')
      .isIn(['system', 'maintenance', 'feature', 'promotion', 'warning']).withMessage('无效的公告类型'),
    
    body('targetType')
      .notEmpty().withMessage('目标类型不能为空')
      .isIn(['all', 'tenant', 'subscription_plan']).withMessage('无效的目标类型'),
    
    body('priority')
      .optional()
      .isInt({ min: 1, max: 5 }).withMessage('优先级必须在1-5之间'),
    
    body('publishTime').optional().isISO8601().withMessage('发布时间格式无效'),
    body('expireTime').optional().isISO8601().withMessage('过期时间格式无效'),
    body('sticky').optional().isBoolean().withMessage('置顶状态必须是布尔值')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        title,
        content,
        type,
        targetType,
        targetValue = null,
        priority = 3,
        publishTime,
        expireTime,
        sticky = false,
        status = 'draft'
      } = req.body;

      // 验证目标值
      let processedTargetValue = null;
      if (targetType !== 'all' && targetValue) {
        processedTargetValue = JSON.stringify(targetValue);
      }

      // 验证时间逻辑
      if (publishTime && expireTime) {
        const publishDate = new Date(publishTime);
        const expireDate = new Date(expireTime);
        
        if (expireDate <= publishDate) {
          return res.status(400).json({
            success: false,
            message: '过期时间必须晚于发布时间'
          });
        }
      }

      // 插入新公告
      const insertResult = await req.db.query(`
        INSERT INTO platform_announcements (
          title, content, type, target_type, target_value, priority,
          publish_time, expire_time, sticky, status, created_by, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        title, content, type, targetType, processedTargetValue, priority,
        publishTime, expireTime, sticky, status, req.admin.id
      ]);

      // 记录操作日志
      logger.info('Announcement created', {
        announcementId: insertResult.insertId,
        title,
        type,
        targetType,
        createdBy: req.admin.id,
        createdByUsername: req.admin.username
      });

      // 获取创建的公告
      const newAnnouncement = await req.db.query(
        'SELECT * FROM platform_announcements WHERE id = ?',
        [insertResult.insertId]
      );

      const announcementData = newAnnouncement[0];
      
      // 解析target_value用于响应
      if (announcementData.target_value) {
        try {
          announcementData.target_value = JSON.parse(announcementData.target_value);
        } catch (e) {
          announcementData.target_value = null;
        }
      }

      res.status(201).json({
        success: true,
        message: '公告创建成功',
        data: announcementData
      });

    } catch (error) {
      logger.error('Create announcement error', {
        error: error.message,
        adminId: req.admin.id,
        requestBody: req.body
      });

      res.status(500).json({
        success: false,
        message: '创建公告失败'
      });
    }
  }
);

/**
 * @route PUT /api/platform/announcements/:id
 * @desc 更新公告
 * @access Private (Platform Admin)
 */
router.put('/:id',
  [
    body('title').optional().isLength({ min: 5, max: 200 }).withMessage('标题长度必须在5-200个字符之间'),
    body('content').optional().isLength({ min: 10 }).withMessage('内容至少需要10个字符'),
    body('type').optional().isIn(['system', 'maintenance', 'feature', 'promotion', 'warning']).withMessage('无效的公告类型'),
    body('targetType').optional().isIn(['all', 'tenant', 'subscription_plan']).withMessage('无效的目标类型'),
    body('priority').optional().isInt({ min: 1, max: 5 }).withMessage('优先级必须在1-5之间'),
    body('publishTime').optional().isISO8601().withMessage('发布时间格式无效'),
    body('expireTime').optional().isISO8601().withMessage('过期时间格式无效'),
    body('sticky').optional().isBoolean().withMessage('置顶状态必须是布尔值'),
    body('status').optional().isIn(['draft', 'published', 'expired', 'archived']).withMessage('无效的状态值')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const announcementId = parseInt(req.params.id);
      if (isNaN(announcementId)) {
        return res.status(400).json({
          success: false,
          message: '无效的公告ID'
        });
      }

      // 检查公告是否存在
      const existingAnnouncement = await req.db.query(
        'SELECT * FROM platform_announcements WHERE id = ?',
        [announcementId]
      );

      if (!existingAnnouncement || existingAnnouncement.length === 0) {
        return res.status(404).json({
          success: false,
          message: '公告不存在'
        });
      }

      // 验证时间逻辑
      const { publishTime, expireTime } = req.body;
      if (publishTime && expireTime) {
        const publishDate = new Date(publishTime);
        const expireDate = new Date(expireTime);
        
        if (expireDate <= publishDate) {
          return res.status(400).json({
            success: false,
            message: '过期时间必须晚于发布时间'
          });
        }
      }

      // 构建更新字段
      const updateFields = [];
      const updateValues = [];

      const allowedFields = {
        title: 'title',
        content: 'content',
        type: 'type',
        targetType: 'target_type',
        priority: 'priority',
        publishTime: 'publish_time',
        expireTime: 'expire_time',
        sticky: 'sticky',
        status: 'status'
      };

      Object.keys(req.body).forEach(key => {
        const dbField = allowedFields[key];
        if (dbField && req.body[key] !== undefined) {
          updateFields.push(`${dbField} = ?`);
          updateValues.push(req.body[key]);
        }
      });

      // 处理特殊字段
      if (req.body.targetValue !== undefined) {
        updateFields.push('target_value = ?');
        updateValues.push(req.body.targetValue ? JSON.stringify(req.body.targetValue) : null);
      }

      if (updateFields.length === 0) {
        return res.status(400).json({
          success: false,
          message: '没有可更新的字段'
        });
      }

      // 添加更新时间
      updateFields.push('updated_at = NOW()');
      updateValues.push(announcementId);

      // 执行更新
      await req.db.query(
        `UPDATE platform_announcements SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );

      // 记录操作日志
      logger.info('Announcement updated', {
        announcementId,
        updatedFields: Object.keys(req.body),
        updatedBy: req.admin.id,
        updatedByUsername: req.admin.username
      });

      // 获取更新后的公告
      const updatedAnnouncement = await req.db.query(
        'SELECT * FROM platform_announcements WHERE id = ?',
        [announcementId]
      );

      const announcementData = updatedAnnouncement[0];
      
      // 解析target_value用于响应
      if (announcementData.target_value) {
        try {
          announcementData.target_value = JSON.parse(announcementData.target_value);
        } catch (e) {
          announcementData.target_value = null;
        }
      }

      res.json({
        success: true,
        message: '公告更新成功',
        data: announcementData
      });

    } catch (error) {
      logger.error('Update announcement error', {
        error: error.message,
        adminId: req.admin.id,
        announcementId: req.params.id,
        requestBody: req.body
      });

      res.status(500).json({
        success: false,
        message: '更新公告失败'
      });
    }
  }
);

/**
 * @route DELETE /api/platform/announcements/:id
 * @desc 删除公告（软删除）
 * @access Private (Platform Admin)
 */
router.delete('/:id', async (req, res) => {
  try {
    const announcementId = parseInt(req.params.id);
    if (isNaN(announcementId)) {
      return res.status(400).json({
        success: false,
        message: '无效的公告ID'
      });
    }

    // 检查公告是否存在
    const announcement = await req.db.query(
      'SELECT * FROM platform_announcements WHERE id = ?',
      [announcementId]
    );

    if (!announcement || announcement.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }

    // 软删除（更改状态为archived）
    await req.db.query(
      'UPDATE platform_announcements SET status = "archived", updated_at = NOW() WHERE id = ?',
      [announcementId]
    );

    // 记录操作日志
    logger.warn('Announcement deleted (soft delete)', {
      announcementId,
      title: announcement[0].title,
      type: announcement[0].type,
      deletedBy: req.admin.id,
      deletedByUsername: req.admin.username
    });

    res.json({
      success: true,
      message: '公告已删除'
    });

  } catch (error) {
    logger.error('Delete announcement error', {
      error: error.message,
      adminId: req.admin.id,
      announcementId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '删除公告失败'
    });
  }
});

/**
 * @route POST /api/platform/announcements/:id/publish
 * @desc 发布公告
 * @access Private (Platform Admin)
 */
router.post('/:id/publish', async (req, res) => {
  try {
    const announcementId = parseInt(req.params.id);

    if (isNaN(announcementId)) {
      return res.status(400).json({
        success: false,
        message: '无效的公告ID'
      });
    }

    // 检查公告是否存在
    const announcement = await req.db.query(
      'SELECT * FROM platform_announcements WHERE id = ?',
      [announcementId]
    );

    if (!announcement || announcement.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }

    if (announcement[0].status === 'published') {
      return res.status(400).json({
        success: false,
        message: '公告已发布'
      });
    }

    // 更新状态为已发布，设置发布时间
    await req.db.query(
      'UPDATE platform_announcements SET status = "published", publish_time = NOW(), updated_at = NOW() WHERE id = ?',
      [announcementId]
    );

    // 记录操作日志
    logger.info('Announcement published', {
      announcementId,
      title: announcement[0].title,
      publishedBy: req.admin.id,
      publishedByUsername: req.admin.username
    });

    res.json({
      success: true,
      message: '公告已发布'
    });

  } catch (error) {
    logger.error('Publish announcement error', {
      error: error.message,
      adminId: req.admin.id,
      announcementId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '发布公告失败'
    });
  }
});

/**
 * @route GET /api/platform/announcements/public/active
 * @desc 获取当前有效的公告（公共接口，供前端显示）
 * @access Public
 */
router.get('/public/active',
  [
    query('targetType').optional().isIn(['all', 'tenant', 'subscription_plan']).withMessage('无效的目标类型'),
    query('targetValue').optional().isLength({ max: 100 }).withMessage('目标值最多100个字符'),
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('限制数量必须在1-50之间')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const { targetType = 'all', targetValue, limit = 10 } = req.query;

      // 构建查询条件
      const whereClauses = [
        'status = "published"',
        '(publish_time IS NULL OR publish_time <= NOW())',
        '(expire_time IS NULL OR expire_time > NOW())'
      ];
      const params = [];

      // 目标筛选
      if (targetType === 'all') {
        whereClauses.push('target_type = "all"');
      } else if (targetType && targetValue) {
        whereClauses.push('(target_type = "all" OR (target_type = ? AND JSON_CONTAINS(target_value, ?)))');
        params.push(targetType, JSON.stringify([targetValue]));
      }

      const whereClause = `WHERE ${whereClauses.join(' AND ')}`;

      // 获取有效公告
      const query = `
        SELECT 
          id, title, content, type, priority, publish_time, expire_time, sticky, created_at
        FROM platform_announcements 
        ${whereClause}
        ORDER BY sticky DESC, priority DESC, publish_time DESC
        LIMIT ?
      `;

      params.push(parseInt(limit));

      const activeAnnouncements = await req.db.query(query, params);

      res.json({
        success: true,
        message: '获取有效公告成功',
        data: activeAnnouncements,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Get active announcements error', {
        error: error.message,
        query: req.query
      });

      res.status(500).json({
        success: false,
        message: '获取有效公告失败'
      });
    }
  }
);

module.exports = router;