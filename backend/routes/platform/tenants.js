const express = require('express');
const { body, query, validationResult } = require('express-validator');
const winston = require('winston');

const router = express.Router();

// Logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

/**
 * @route GET /api/platform/tenants
 * @desc 获取租户列表（分页）
 * @access Private (Platform Admin)
 */
router.get('/',
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
    query('status').optional().isIn(['pending', 'active', 'suspended', 'expired', 'cancelled']).withMessage('无效的状态值'),
    query('subscriptionPlan').optional().isIn(['trial', 'basic', 'standard', 'premium', 'enterprise']).withMessage('无效的订阅计划'),
    query('search').optional().isLength({ max: 100 }).withMessage('搜索关键词最多100个字符')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        page = 1,
        pageSize = 20,
        status,
        subscriptionPlan,
        search,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = req.query;

      // 构建查询条件
      const conditions = {};
      const params = [];
      let whereClause = '';
      
      const whereClauses = [];
      
      if (status) {
        whereClauses.push('status = ?');
        params.push(status);
      }
      
      if (subscriptionPlan) {
        whereClauses.push('subscription_plan = ?');
        params.push(subscriptionPlan);
      }
      
      if (search) {
        whereClauses.push('(company_name LIKE ? OR contact_name LIKE ? OR contact_email LIKE ? OR tenant_code LIKE ?)');
        const searchTerm = `%${search}%`;
        params.push(searchTerm, searchTerm, searchTerm, searchTerm);
      }
      
      if (whereClauses.length > 0) {
        whereClause = `WHERE ${whereClauses.join(' AND ')}`;
      }

      // 验证排序字段
      const allowedSortFields = ['created_at', 'company_name', 'status', 'subscription_plan', 'subscription_end_date'];
      const safeSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
      const safeSortOrder = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

      // 构建查询
      const baseQuery = `
        SELECT 
          id, tenant_code, company_name, contact_name, contact_phone, contact_email,
          address, business_license, industry, scale, status, subscription_plan,
          subscription_start_date, subscription_end_date, max_users, max_flocks,
          max_storage_gb, max_api_calls_per_month, custom_domain, logo_url,
          last_active_at, created_at, updated_at
        FROM tenants 
        ${whereClause}
        ORDER BY ${safeSortBy} ${safeSortOrder}
      `;

      const countQuery = `SELECT COUNT(*) as total FROM tenants ${whereClause}`;

      // 执行分页查询
      const result = await req.db.paginate(baseQuery, params, parseInt(page), parseInt(pageSize), countQuery);

      // 添加统计信息
      const stats = await req.db.query(`
        SELECT 
          status,
          subscription_plan,
          COUNT(*) as count
        FROM tenants
        GROUP BY status, subscription_plan
      `);

      // 处理统计数据
      const statusStats = {};
      const planStats = {};
      
      stats.forEach(stat => {
        if (!statusStats[stat.status]) statusStats[stat.status] = 0;
        if (!planStats[stat.subscription_plan]) planStats[stat.subscription_plan] = 0;
        
        statusStats[stat.status] += stat.count;
        planStats[stat.subscription_plan] += stat.count;
      });

      res.json({
        success: true,
        message: '获取租户列表成功',
        data: result.data,
        pagination: result.pagination,
        statistics: {
          byStatus: statusStats,
          byPlan: planStats
        }
      });

    } catch (error) {
      logger.error('Get tenants list error', {
        error: error.message,
        adminId: req.admin.id,
        query: req.query
      });

      res.status(500).json({
        success: false,
        message: '获取租户列表失败'
      });
    }
  }
);

/**
 * @route GET /api/platform/tenants/:id
 * @desc 获取租户详情
 * @access Private (Platform Admin)
 */
router.get('/:id',
  [
    query('includeStats').optional().isBoolean().withMessage('includeStats必须是布尔值')
  ],
  async (req, res) => {
    try {
      const tenantId = parseInt(req.params.id);
      const includeStats = req.query.includeStats === 'true';

      if (isNaN(tenantId)) {
        return res.status(400).json({
          success: false,
          message: '无效的租户ID'
        });
      }

      // 获取租户基本信息
      const tenant = await req.db.query(`
        SELECT 
          id, tenant_code, company_name, contact_name, contact_phone, contact_email,
          address, business_license, industry, scale, status, subscription_plan,
          subscription_start_date, subscription_end_date, trial_start_date, trial_end_date,
          max_users, max_flocks, max_storage_gb, max_api_calls_per_month,
          custom_domain, logo_url, theme_config, last_active_at, created_at, updated_at
        FROM tenants 
        WHERE id = ?
      `, [tenantId]);

      if (!tenant || tenant.length === 0) {
        return res.status(404).json({
          success: false,
          message: '租户不存在'
        });
      }

      const tenantData = tenant[0];

      // 如果需要统计信息
      if (includeStats) {
        // 获取用户统计
        const userStats = await req.db.query(`
          SELECT 
            COUNT(*) as total_users,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
            SUM(CASE WHEN last_login_at > DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as active_last_30_days
          FROM tenant_users 
          WHERE tenant_id = ?
        `, [tenantId]);

        // 获取鹅群统计
        const flockStats = await req.db.query(`
          SELECT 
            COUNT(*) as total_flocks,
            SUM(current_count) as total_geese,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_flocks
          FROM flocks 
          WHERE tenant_id = ?
        `, [tenantId]);

        // 获取存储统计
        const storageStats = await req.db.query(`
          SELECT 
            COUNT(*) as total_files,
            COALESCE(SUM(file_size), 0) as total_storage_bytes
          FROM file_uploads 
          WHERE tenant_id = ? AND status = 'active'
        `, [tenantId]);

        // 获取最近活动
        const recentActivity = await req.db.query(`
          SELECT 
            action, resource, created_at,
            JSON_EXTRACT(request_data, '$.description') as description
          FROM system_logs 
          WHERE tenant_id = ? 
          ORDER BY created_at DESC 
          LIMIT 10
        `, [tenantId]);

        tenantData.statistics = {
          users: userStats[0] || { total_users: 0, active_users: 0, active_last_30_days: 0 },
          flocks: flockStats[0] || { total_flocks: 0, total_geese: 0, active_flocks: 0 },
          storage: {
            ...storageStats[0] || { total_files: 0, total_storage_bytes: 0 },
            total_storage_mb: Math.round((storageStats[0]?.total_storage_bytes || 0) / 1024 / 1024),
            storage_usage_percentage: Math.round(
              ((storageStats[0]?.total_storage_bytes || 0) / 1024 / 1024 / 1024) / 
              (tenantData.max_storage_gb || 1) * 100
            )
          },
          recent_activity: recentActivity
        };
      }

      res.json({
        success: true,
        message: '获取租户详情成功',
        data: tenantData
      });

    } catch (error) {
      logger.error('Get tenant details error', {
        error: error.message,
        adminId: req.admin.id,
        tenantId: req.params.id
      });

      res.status(500).json({
        success: false,
        message: '获取租户详情失败'
      });
    }
  }
);

/**
 * @route POST /api/platform/tenants
 * @desc 创建新租户
 * @access Private (Platform Admin)
 */
router.post('/',
  [
    body('tenantCode')
      .notEmpty().withMessage('租户代码不能为空')
      .isLength({ min: 3, max: 50 }).withMessage('租户代码长度必须在3-50个字符之间')
      .matches(/^[a-zA-Z0-9_-]+$/).withMessage('租户代码只能包含字母、数字、下划线和连字符'),
    
    body('companyName')
      .notEmpty().withMessage('公司名称不能为空')
      .isLength({ min: 2, max: 200 }).withMessage('公司名称长度必须在2-200个字符之间'),
    
    body('contactName')
      .notEmpty().withMessage('联系人姓名不能为空')
      .isLength({ min: 2, max: 100 }).withMessage('联系人姓名长度必须在2-100个字符之间'),
    
    body('contactPhone')
      .notEmpty().withMessage('联系电话不能为空')
      .isMobilePhone('zh-CN').withMessage('请输入有效的手机号码'),
    
    body('contactEmail')
      .notEmpty().withMessage('联系邮箱不能为空')
      .isEmail().normalizeEmail().withMessage('请输入有效的邮箱地址'),
    
    body('industry').optional().isLength({ max: 100 }).withMessage('行业类型最多100个字符'),
    body('address').optional().isLength({ max: 500 }).withMessage('地址最多500个字符'),
    body('businessLicense').optional().isLength({ max: 100 }).withMessage('营业执照号最多100个字符'),
    
    body('scale')
      .optional()
      .isIn(['startup', 'small', 'medium', 'large', 'enterprise'])
      .withMessage('无效的企业规模'),
    
    body('subscriptionPlan')
      .optional()
      .isIn(['trial', 'basic', 'standard', 'premium', 'enterprise'])
      .withMessage('无效的订阅计划'),
    
    body('maxUsers').optional().isInt({ min: 1, max: 10000 }).withMessage('最大用户数必须在1-10000之间'),
    body('maxFlocks').optional().isInt({ min: 1, max: 1000 }).withMessage('最大鹅群数必须在1-1000之间'),
    body('maxStorageGb').optional().isInt({ min: 1, max: 1000 }).withMessage('最大存储空间必须在1-1000GB之间')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        tenantCode,
        companyName,
        contactName,
        contactPhone,
        contactEmail,
        address,
        businessLicense,
        industry,
        scale = 'small',
        subscriptionPlan = 'trial',
        maxUsers = 5,
        maxFlocks = 10,
        maxStorageGb = 5,
        maxApiCallsPerMonth = 10000
      } = req.body;

      // 检查租户代码是否已存在
      const existingTenant = await req.db.query(
        'SELECT id FROM tenants WHERE tenant_code = ?',
        [tenantCode]
      );

      if (existingTenant && existingTenant.length > 0) {
        return res.status(409).json({
          success: false,
          message: '租户代码已存在',
          code: 'TENANT_CODE_EXISTS'
        });
      }

      // 检查邮箱是否已存在
      const existingEmail = await req.db.query(
        'SELECT id FROM tenants WHERE contact_email = ?',
        [contactEmail]
      );

      if (existingEmail && existingEmail.length > 0) {
        return res.status(409).json({
          success: false,
          message: '联系邮箱已被使用',
          code: 'EMAIL_EXISTS'
        });
      }

      // 设置试用期（如果是试用计划）
      let trialStartDate = null;
      let trialEndDate = null;
      let subscriptionStartDate = null;
      let subscriptionEndDate = null;

      if (subscriptionPlan === 'trial') {
        trialStartDate = new Date();
        trialEndDate = new Date();
        trialEndDate.setDate(trialEndDate.getDate() + 30); // 30天试用期
      } else {
        subscriptionStartDate = new Date();
        subscriptionEndDate = new Date();
        subscriptionEndDate.setMonth(subscriptionEndDate.getMonth() + 12); // 12个月订阅
      }

      // 使用事务创建租户
      const result = await req.db.transaction(async (connection) => {
        // 插入租户记录
        const insertResult = await connection.execute(`
          INSERT INTO tenants (
            tenant_code, company_name, contact_name, contact_phone, contact_email,
            address, business_license, industry, scale, status, subscription_plan,
            trial_start_date, trial_end_date, subscription_start_date, subscription_end_date,
            max_users, max_flocks, max_storage_gb, max_api_calls_per_month,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          tenantCode, companyName, contactName, contactPhone, contactEmail,
          address, businessLicense, industry, scale, subscriptionPlan,
          trialStartDate, trialEndDate, subscriptionStartDate, subscriptionEndDate,
          maxUsers, maxFlocks, maxStorageGb, maxApiCallsPerMonth
        ]);

        const tenantId = insertResult.insertId;

        // 创建默认管理员用户（可选）
        // TODO: 可以在这里创建默认的租户管理员账户

        return tenantId;
      });

      // 记录操作日志
      logger.info('Tenant created', {
        tenantId: result,
        tenantCode,
        companyName,
        createdBy: req.admin.id,
        createdByUsername: req.admin.username
      });

      // 获取创建的租户信息
      const newTenant = await req.db.query(
        'SELECT * FROM tenants WHERE id = ?',
        [result]
      );

      res.status(201).json({
        success: true,
        message: '租户创建成功',
        data: newTenant[0]
      });

    } catch (error) {
      logger.error('Create tenant error', {
        error: error.message,
        adminId: req.admin.id,
        requestBody: req.body
      });

      res.status(500).json({
        success: false,
        message: '创建租户失败'
      });
    }
  }
);

/**
 * @route PUT /api/platform/tenants/:id
 * @desc 更新租户信息
 * @access Private (Platform Admin)
 */
router.put('/:id',
  [
    body('companyName').optional().isLength({ min: 2, max: 200 }).withMessage('公司名称长度必须在2-200个字符之间'),
    body('contactName').optional().isLength({ min: 2, max: 100 }).withMessage('联系人姓名长度必须在2-100个字符之间'),
    body('contactPhone').optional().isMobilePhone('zh-CN').withMessage('请输入有效的手机号码'),
    body('contactEmail').optional().isEmail().normalizeEmail().withMessage('请输入有效的邮箱地址'),
    body('status').optional().isIn(['pending', 'active', 'suspended', 'expired', 'cancelled']).withMessage('无效的状态值'),
    body('subscriptionPlan').optional().isIn(['trial', 'basic', 'standard', 'premium', 'enterprise']).withMessage('无效的订阅计划'),
    body('maxUsers').optional().isInt({ min: 1, max: 10000 }).withMessage('最大用户数必须在1-10000之间'),
    body('maxFlocks').optional().isInt({ min: 1, max: 1000 }).withMessage('最大鹅群数必须在1-1000之间'),
    body('maxStorageGb').optional().isInt({ min: 1, max: 1000 }).withMessage('最大存储空间必须在1-1000GB之间')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const tenantId = parseInt(req.params.id);
      if (isNaN(tenantId)) {
        return res.status(400).json({
          success: false,
          message: '无效的租户ID'
        });
      }

      // 检查租户是否存在
      const existingTenant = await req.db.query(
        'SELECT * FROM tenants WHERE id = ?',
        [tenantId]
      );

      if (!existingTenant || existingTenant.length === 0) {
        return res.status(404).json({
          success: false,
          message: '租户不存在'
        });
      }

      // 构建更新字段
      const updateFields = [];
      const updateValues = [];

      const allowedFields = [
        'company_name', 'contact_name', 'contact_phone', 'contact_email',
        'address', 'business_license', 'industry', 'scale', 'status',
        'subscription_plan', 'max_users', 'max_flocks', 'max_storage_gb',
        'max_api_calls_per_month', 'custom_domain'
      ];

      // 映射前端字段到数据库字段
      const fieldMapping = {
        companyName: 'company_name',
        contactName: 'contact_name',
        contactPhone: 'contact_phone',
        contactEmail: 'contact_email',
        businessLicense: 'business_license',
        subscriptionPlan: 'subscription_plan',
        maxUsers: 'max_users',
        maxFlocks: 'max_flocks',
        maxStorageGb: 'max_storage_gb',
        maxApiCallsPerMonth: 'max_api_calls_per_month',
        customDomain: 'custom_domain'
      };

      Object.keys(req.body).forEach(key => {
        const dbField = fieldMapping[key] || key;
        if (allowedFields.includes(dbField) && req.body[key] !== undefined) {
          updateFields.push(`${dbField} = ?`);
          updateValues.push(req.body[key]);
        }
      });

      if (updateFields.length === 0) {
        return res.status(400).json({
          success: false,
          message: '没有可更新的字段'
        });
      }

      // 添加更新时间
      updateFields.push('updated_at = NOW()');
      updateValues.push(tenantId);

      // 执行更新
      await req.db.query(
        `UPDATE tenants SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );

      // 记录操作日志
      logger.info('Tenant updated', {
        tenantId,
        updatedFields: Object.keys(req.body),
        updatedBy: req.admin.id,
        updatedByUsername: req.admin.username
      });

      // 获取更新后的租户信息
      const updatedTenant = await req.db.query(
        'SELECT * FROM tenants WHERE id = ?',
        [tenantId]
      );

      res.json({
        success: true,
        message: '租户信息更新成功',
        data: updatedTenant[0]
      });

    } catch (error) {
      logger.error('Update tenant error', {
        error: error.message,
        adminId: req.admin.id,
        tenantId: req.params.id,
        requestBody: req.body
      });

      res.status(500).json({
        success: false,
        message: '更新租户信息失败'
      });
    }
  }
);

/**
 * @route DELETE /api/platform/tenants/:id
 * @desc 删除租户（软删除）
 * @access Private (Platform Admin - Super Admin only)
 */
router.delete('/:id', async (req, res) => {
  try {
    // 只有超级管理员才能删除租户
    if (req.admin.role !== 'super_admin') {
      return res.status(403).json({
        success: false,
        message: '只有超级管理员才能删除租户',
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }

    const tenantId = parseInt(req.params.id);
    if (isNaN(tenantId)) {
      return res.status(400).json({
        success: false,
        message: '无效的租户ID'
      });
    }

    // 检查租户是否存在
    const tenant = await req.db.query(
      'SELECT * FROM tenants WHERE id = ?',
      [tenantId]
    );

    if (!tenant || tenant.length === 0) {
      return res.status(404).json({
        success: false,
        message: '租户不存在'
      });
    }

    // 软删除（更改状态为cancelled）
    await req.db.query(
      'UPDATE tenants SET status = "cancelled", updated_at = NOW() WHERE id = ?',
      [tenantId]
    );

    // 记录操作日志
    logger.warn('Tenant deleted (soft delete)', {
      tenantId,
      tenantCode: tenant[0].tenant_code,
      companyName: tenant[0].company_name,
      deletedBy: req.admin.id,
      deletedByUsername: req.admin.username
    });

    res.json({
      success: true,
      message: '租户已删除'
    });

  } catch (error) {
    logger.error('Delete tenant error', {
      error: error.message,
      adminId: req.admin.id,
      tenantId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '删除租户失败'
    });
  }
});

/**
 * @route POST /api/platform/tenants/:id/suspend
 * @desc 暂停租户
 * @access Private (Platform Admin)
 */
router.post('/:id/suspend',
  [
    body('reason').optional().isLength({ max: 500 }).withMessage('暂停原因最多500个字符')
  ],
  async (req, res) => {
    try {
      const tenantId = parseInt(req.params.id);
      const { reason } = req.body;

      if (isNaN(tenantId)) {
        return res.status(400).json({
          success: false,
          message: '无效的租户ID'
        });
      }

      // 更新租户状态
      await req.db.query(
        'UPDATE tenants SET status = "suspended", updated_at = NOW() WHERE id = ?',
        [tenantId]
      );

      // 记录操作日志
      logger.warn('Tenant suspended', {
        tenantId,
        reason,
        suspendedBy: req.admin.id,
        suspendedByUsername: req.admin.username
      });

      res.json({
        success: true,
        message: '租户已暂停'
      });

    } catch (error) {
      logger.error('Suspend tenant error', {
        error: error.message,
        adminId: req.admin.id,
        tenantId: req.params.id
      });

      res.status(500).json({
        success: false,
        message: '暂停租户失败'
      });
    }
  }
);

/**
 * @route POST /api/platform/tenants/:id/activate
 * @desc 激活租户
 * @access Private (Platform Admin)
 */
router.post('/:id/activate', async (req, res) => {
  try {
    const tenantId = parseInt(req.params.id);

    if (isNaN(tenantId)) {
      return res.status(400).json({
        success: false,
        message: '无效的租户ID'
      });
    }

    // 更新租户状态
    await req.db.query(
      'UPDATE tenants SET status = "active", updated_at = NOW() WHERE id = ?',
      [tenantId]
    );

    // 记录操作日志
    logger.info('Tenant activated', {
      tenantId,
      activatedBy: req.admin.id,
      activatedByUsername: req.admin.username
    });

    res.json({
      success: true,
      message: '租户已激活'
    });

  } catch (error) {
    logger.error('Activate tenant error', {
      error: error.message,
      adminId: req.admin.id,
      tenantId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '激活租户失败'
    });
  }
});

module.exports = router;