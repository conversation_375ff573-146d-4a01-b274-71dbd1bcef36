const express = require('express');
const { body, query, validationResult } = require('express-validator');
const winston = require('winston');

const router = express.Router();

// Logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

/**
 * @route GET /api/platform/mall
 * @desc 获取商城商品列表
 * @access Private (Platform Admin)
 */
router.get('/',
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
    query('category').optional().isLength({ max: 100 }).withMessage('分类名称最多100个字符'),
    query('status').optional().isIn(['draft', 'active', 'inactive', 'out_of_stock']).withMessage('无效的状态值'),
    query('featured').optional().isBoolean().withMessage('精选状态必须是布尔值'),
    query('search').optional().isLength({ max: 100 }).withMessage('搜索关键词最多100个字符'),
    query('priceMin').optional().isFloat({ min: 0 }).withMessage('最低价格必须大于等于0'),
    query('priceMax').optional().isFloat({ min: 0 }).withMessage('最高价格必须大于等于0')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        page = 1,
        pageSize = 20,
        category,
        status,
        featured,
        search,
        priceMin,
        priceMax,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = req.query;

      // 构建查询条件
      const whereClauses = [];
      const params = [];

      if (category) {
        whereClauses.push('category LIKE ?');
        params.push(`%${category}%`);
      }

      if (status) {
        whereClauses.push('status = ?');
        params.push(status);
      }

      if (featured !== undefined) {
        whereClauses.push('featured = ?');
        params.push(featured === 'true');
      }

      if (search) {
        whereClauses.push('(name LIKE ? OR description LIKE ? OR tags LIKE ?)');
        const searchTerm = `%${search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      if (priceMin !== undefined) {
        whereClauses.push('price >= ?');
        params.push(parseFloat(priceMin));
      }

      if (priceMax !== undefined) {
        whereClauses.push('price <= ?');
        params.push(parseFloat(priceMax));
      }

      const whereClause = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

      // 验证排序字段
      const allowedSortFields = ['created_at', 'name', 'price', 'sales_count', 'stock_quantity', 'rating'];
      const safeSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
      const safeSortOrder = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

      // 构建查询（不包含description字段以提高性能）
      const baseQuery = `
        SELECT 
          id, name, category, price, original_price, stock_quantity, sales_count,
          rating, review_count, image_urls, tags, status, featured, sort_order,
          created_at, updated_at
        FROM mall_products 
        ${whereClause}
        ORDER BY featured DESC, ${safeSortBy} ${safeSortOrder}
      `;

      const countQuery = `SELECT COUNT(*) as total FROM mall_products ${whereClause}`;

      // 执行分页查询
      const result = await req.db.paginate(baseQuery, params, parseInt(page), parseInt(pageSize), countQuery);

      // 处理图片URL和标签JSON
      const productsWithProcessedData = result.data.map(product => {
        // 解析image_urls JSON
        if (product.image_urls) {
          try {
            product.image_urls = JSON.parse(product.image_urls);
          } catch (e) {
            product.image_urls = [];
          }
        }
        
        // 解析tags JSON
        if (product.tags) {
          try {
            product.tags = JSON.parse(product.tags);
          } catch (e) {
            product.tags = [];
          }
        }
        
        return product;
      });

      // 获取统计信息
      const stats = await req.db.query(`
        SELECT 
          category,
          status,
          COUNT(*) as count,
          AVG(price) as avg_price,
          SUM(sales_count) as total_sales,
          SUM(stock_quantity) as total_stock
        FROM mall_products
        GROUP BY category, status
        ORDER BY count DESC
      `);

      res.json({
        success: true,
        message: '获取商城商品列表成功',
        data: productsWithProcessedData,
        pagination: result.pagination,
        statistics: {
          breakdown: stats
        }
      });

    } catch (error) {
      logger.error('Get mall products error', {
        error: error.message,
        adminId: req.admin.id,
        query: req.query
      });

      res.status(500).json({
        success: false,
        message: '获取商城商品列表失败'
      });
    }
  }
);

/**
 * @route GET /api/platform/mall/:id
 * @desc 获取商品详情
 * @access Private (Platform Admin) or Public (if active)
 */
router.get('/:id', async (req, res) => {
  try {
    const productId = parseInt(req.params.id);

    if (isNaN(productId)) {
      return res.status(400).json({
        success: false,
        message: '无效的商品ID'
      });
    }

    // 获取商品详情
    const product = await req.db.query(`
      SELECT 
        id, name, description, category, price, original_price, stock_quantity,
        sales_count, rating, review_count, image_urls, tags, specifications,
        shipping_info, return_policy, status, featured, sort_order,
        created_at, updated_at
      FROM mall_products 
      WHERE id = ?
    `, [productId]);

    if (!product || product.length === 0) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }

    const productData = product[0];

    // 如果不是管理员且商品不是active状态，拒绝访问
    if (!req.admin && productData.status !== 'active') {
      return res.status(403).json({
        success: false,
        message: '商品不可用',
        code: 'PRODUCT_UNAVAILABLE'
      });
    }

    // 解析JSON字段
    if (productData.image_urls) {
      try {
        productData.image_urls = JSON.parse(productData.image_urls);
      } catch (e) {
        productData.image_urls = [];
      }
    }

    if (productData.tags) {
      try {
        productData.tags = JSON.parse(productData.tags);
      } catch (e) {
        productData.tags = [];
      }
    }

    if (productData.specifications) {
      try {
        productData.specifications = JSON.parse(productData.specifications);
      } catch (e) {
        productData.specifications = {};
      }
    }

    if (productData.shipping_info) {
      try {
        productData.shipping_info = JSON.parse(productData.shipping_info);
      } catch (e) {
        productData.shipping_info = {};
      }
    }

    if (productData.return_policy) {
      try {
        productData.return_policy = JSON.parse(productData.return_policy);
      } catch (e) {
        productData.return_policy = {};
      }
    }

    res.json({
      success: true,
      message: '获取商品详情成功',
      data: productData
    });

  } catch (error) {
    logger.error('Get mall product details error', {
      error: error.message,
      adminId: req.admin?.id,
      productId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '获取商品详情失败'
    });
  }
});

/**
 * @route POST /api/platform/mall
 * @desc 创建商品
 * @access Private (Platform Admin)
 */
router.post('/',
  [
    body('name')
      .notEmpty().withMessage('商品名称不能为空')
      .isLength({ min: 2, max: 200 }).withMessage('商品名称长度必须在2-200个字符之间'),
    
    body('description')
      .notEmpty().withMessage('商品描述不能为空')
      .isLength({ min: 10 }).withMessage('商品描述至少需要10个字符'),
    
    body('category')
      .notEmpty().withMessage('商品分类不能为空')
      .isLength({ min: 2, max: 100 }).withMessage('商品分类长度必须在2-100个字符之间'),
    
    body('price')
      .notEmpty().withMessage('商品价格不能为空')
      .isFloat({ min: 0.01 }).withMessage('商品价格必须大于0'),
    
    body('originalPrice')
      .optional()
      .isFloat({ min: 0 }).withMessage('原价必须大于等于0'),
    
    body('stockQuantity')
      .notEmpty().withMessage('库存数量不能为空')
      .isInt({ min: 0 }).withMessage('库存数量必须大于等于0'),
    
    body('imageUrls').optional().isArray().withMessage('图片URL必须是数组'),
    body('tags').optional().isArray().withMessage('标签必须是数组'),
    body('specifications').optional().isObject().withMessage('规格参数必须是对象'),
    body('shippingInfo').optional().isObject().withMessage('配送信息必须是对象'),
    body('returnPolicy').optional().isObject().withMessage('退换政策必须是对象'),
    body('featured').optional().isBoolean().withMessage('精选状态必须是布尔值'),
    body('sortOrder').optional().isInt({ min: 0 }).withMessage('排序权重必须大于等于0')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        name,
        description,
        category,
        price,
        originalPrice,
        stockQuantity,
        imageUrls = [],
        tags = [],
        specifications = {},
        shippingInfo = {},
        returnPolicy = {},
        status = 'draft',
        featured = false,
        sortOrder = 0
      } = req.body;

      // 检查商品名称是否重复
      const existingProduct = await req.db.query(
        'SELECT id FROM mall_products WHERE name = ?',
        [name]
      );

      if (existingProduct && existingProduct.length > 0) {
        return res.status(409).json({
          success: false,
          message: '商品名称已存在',
          code: 'PRODUCT_NAME_EXISTS'
        });
      }

      // 插入新商品
      const insertResult = await req.db.query(`
        INSERT INTO mall_products (
          name, description, category, price, original_price, stock_quantity,
          sales_count, rating, review_count, image_urls, tags, specifications,
          shipping_info, return_policy, status, featured, sort_order,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, 0, 5.0, 0, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        name, description, category, price, originalPrice, stockQuantity,
        JSON.stringify(imageUrls), JSON.stringify(tags), JSON.stringify(specifications),
        JSON.stringify(shippingInfo), JSON.stringify(returnPolicy), status, featured, sortOrder
      ]);

      // 记录操作日志
      logger.info('Mall product created', {
        productId: insertResult.insertId,
        name,
        category,
        price,
        createdBy: req.admin.id,
        createdByUsername: req.admin.username
      });

      // 获取创建的商品
      const newProduct = await req.db.query(
        'SELECT * FROM mall_products WHERE id = ?',
        [insertResult.insertId]
      );

      const productData = newProduct[0];
      
      // 解析JSON字段用于响应
      if (productData.image_urls) {
        try {
          productData.image_urls = JSON.parse(productData.image_urls);
        } catch (e) {
          productData.image_urls = [];
        }
      }

      if (productData.tags) {
        try {
          productData.tags = JSON.parse(productData.tags);
        } catch (e) {
          productData.tags = [];
        }
      }

      if (productData.specifications) {
        try {
          productData.specifications = JSON.parse(productData.specifications);
        } catch (e) {
          productData.specifications = {};
        }
      }

      if (productData.shipping_info) {
        try {
          productData.shipping_info = JSON.parse(productData.shipping_info);
        } catch (e) {
          productData.shipping_info = {};
        }
      }

      if (productData.return_policy) {
        try {
          productData.return_policy = JSON.parse(productData.return_policy);
        } catch (e) {
          productData.return_policy = {};
        }
      }

      res.status(201).json({
        success: true,
        message: '商品创建成功',
        data: productData
      });

    } catch (error) {
      logger.error('Create mall product error', {
        error: error.message,
        adminId: req.admin.id,
        requestBody: req.body
      });

      res.status(500).json({
        success: false,
        message: '创建商品失败'
      });
    }
  }
);

/**
 * @route PUT /api/platform/mall/:id
 * @desc 更新商品
 * @access Private (Platform Admin)
 */
router.put('/:id',
  [
    body('name').optional().isLength({ min: 2, max: 200 }).withMessage('商品名称长度必须在2-200个字符之间'),
    body('description').optional().isLength({ min: 10 }).withMessage('商品描述至少需要10个字符'),
    body('category').optional().isLength({ min: 2, max: 100 }).withMessage('商品分类长度必须在2-100个字符之间'),
    body('price').optional().isFloat({ min: 0.01 }).withMessage('商品价格必须大于0'),
    body('originalPrice').optional().isFloat({ min: 0 }).withMessage('原价必须大于等于0'),
    body('stockQuantity').optional().isInt({ min: 0 }).withMessage('库存数量必须大于等于0'),
    body('imageUrls').optional().isArray().withMessage('图片URL必须是数组'),
    body('tags').optional().isArray().withMessage('标签必须是数组'),
    body('specifications').optional().isObject().withMessage('规格参数必须是对象'),
    body('shippingInfo').optional().isObject().withMessage('配送信息必须是对象'),
    body('returnPolicy').optional().isObject().withMessage('退换政策必须是对象'),
    body('status').optional().isIn(['draft', 'active', 'inactive', 'out_of_stock']).withMessage('无效的状态值'),
    body('featured').optional().isBoolean().withMessage('精选状态必须是布尔值'),
    body('sortOrder').optional().isInt({ min: 0 }).withMessage('排序权重必须大于等于0')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const productId = parseInt(req.params.id);
      if (isNaN(productId)) {
        return res.status(400).json({
          success: false,
          message: '无效的商品ID'
        });
      }

      // 检查商品是否存在
      const existingProduct = await req.db.query(
        'SELECT * FROM mall_products WHERE id = ?',
        [productId]
      );

      if (!existingProduct || existingProduct.length === 0) {
        return res.status(404).json({
          success: false,
          message: '商品不存在'
        });
      }

      // 如果更新商品名称，检查是否重复
      if (req.body.name && req.body.name !== existingProduct[0].name) {
        const duplicateName = await req.db.query(
          'SELECT id FROM mall_products WHERE name = ? AND id != ?',
          [req.body.name, productId]
        );

        if (duplicateName && duplicateName.length > 0) {
          return res.status(409).json({
            success: false,
            message: '商品名称已存在',
            code: 'PRODUCT_NAME_EXISTS'
          });
        }
      }

      // 构建更新字段
      const updateFields = [];
      const updateValues = [];

      const allowedFields = {
        name: 'name',
        description: 'description',
        category: 'category',
        price: 'price',
        originalPrice: 'original_price',
        stockQuantity: 'stock_quantity',
        status: 'status',
        featured: 'featured',
        sortOrder: 'sort_order'
      };

      Object.keys(req.body).forEach(key => {
        const dbField = allowedFields[key];
        if (dbField && req.body[key] !== undefined) {
          updateFields.push(`${dbField} = ?`);
          updateValues.push(req.body[key]);
        }
      });

      // 处理特殊字段
      if (req.body.imageUrls !== undefined) {
        updateFields.push('image_urls = ?');
        updateValues.push(JSON.stringify(req.body.imageUrls));
      }

      if (req.body.tags !== undefined) {
        updateFields.push('tags = ?');
        updateValues.push(JSON.stringify(req.body.tags));
      }

      if (req.body.specifications !== undefined) {
        updateFields.push('specifications = ?');
        updateValues.push(JSON.stringify(req.body.specifications));
      }

      if (req.body.shippingInfo !== undefined) {
        updateFields.push('shipping_info = ?');
        updateValues.push(JSON.stringify(req.body.shippingInfo));
      }

      if (req.body.returnPolicy !== undefined) {
        updateFields.push('return_policy = ?');
        updateValues.push(JSON.stringify(req.body.returnPolicy));
      }

      if (updateFields.length === 0) {
        return res.status(400).json({
          success: false,
          message: '没有可更新的字段'
        });
      }

      // 添加更新时间
      updateFields.push('updated_at = NOW()');
      updateValues.push(productId);

      // 执行更新
      await req.db.query(
        `UPDATE mall_products SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );

      // 记录操作日志
      logger.info('Mall product updated', {
        productId,
        updatedFields: Object.keys(req.body),
        updatedBy: req.admin.id,
        updatedByUsername: req.admin.username
      });

      // 获取更新后的商品
      const updatedProduct = await req.db.query(
        'SELECT * FROM mall_products WHERE id = ?',
        [productId]
      );

      const productData = updatedProduct[0];
      
      // 解析JSON字段用于响应
      if (productData.image_urls) {
        try {
          productData.image_urls = JSON.parse(productData.image_urls);
        } catch (e) {
          productData.image_urls = [];
        }
      }

      if (productData.tags) {
        try {
          productData.tags = JSON.parse(productData.tags);
        } catch (e) {
          productData.tags = [];
        }
      }

      if (productData.specifications) {
        try {
          productData.specifications = JSON.parse(productData.specifications);
        } catch (e) {
          productData.specifications = {};
        }
      }

      if (productData.shipping_info) {
        try {
          productData.shipping_info = JSON.parse(productData.shipping_info);
        } catch (e) {
          productData.shipping_info = {};
        }
      }

      if (productData.return_policy) {
        try {
          productData.return_policy = JSON.parse(productData.return_policy);
        } catch (e) {
          productData.return_policy = {};
        }
      }

      res.json({
        success: true,
        message: '商品更新成功',
        data: productData
      });

    } catch (error) {
      logger.error('Update mall product error', {
        error: error.message,
        adminId: req.admin.id,
        productId: req.params.id,
        requestBody: req.body
      });

      res.status(500).json({
        success: false,
        message: '更新商品失败'
      });
    }
  }
);

/**
 * @route DELETE /api/platform/mall/:id
 * @desc 删除商品（软删除）
 * @access Private (Platform Admin)
 */
router.delete('/:id', async (req, res) => {
  try {
    const productId = parseInt(req.params.id);
    if (isNaN(productId)) {
      return res.status(400).json({
        success: false,
        message: '无效的商品ID'
      });
    }

    // 检查商品是否存在
    const product = await req.db.query(
      'SELECT * FROM mall_products WHERE id = ?',
      [productId]
    );

    if (!product || product.length === 0) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }

    // 软删除（更改状态为inactive）
    await req.db.query(
      'UPDATE mall_products SET status = "inactive", updated_at = NOW() WHERE id = ?',
      [productId]
    );

    // 记录操作日志
    logger.warn('Mall product deleted (soft delete)', {
      productId,
      name: product[0].name,
      category: product[0].category,
      deletedBy: req.admin.id,
      deletedByUsername: req.admin.username
    });

    res.json({
      success: true,
      message: '商品已删除'
    });

  } catch (error) {
    logger.error('Delete mall product error', {
      error: error.message,
      adminId: req.admin.id,
      productId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '删除商品失败'
    });
  }
});

/**
 * @route GET /api/platform/mall/public/featured
 * @desc 获取精选商品列表（公共接口）
 * @access Public
 */
router.get('/public/featured',
  [
    query('category').optional().isLength({ max: 100 }).withMessage('分类名称最多100个字符'),
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('限制数量必须在1-50之间')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const { category, limit = 20 } = req.query;

      // 构建查询条件
      const whereClauses = ['status = "active"', 'featured = true'];
      const params = [];

      if (category) {
        whereClauses.push('category = ?');
        params.push(category);
      }

      const whereClause = `WHERE ${whereClauses.join(' AND ')}`;

      // 获取精选商品
      const query = `
        SELECT 
          id, name, category, price, original_price, image_urls, rating, review_count, sales_count
        FROM mall_products 
        ${whereClause}
        ORDER BY sort_order DESC, sales_count DESC
        LIMIT ?
      `;

      params.push(parseInt(limit));

      const featuredProducts = await req.db.query(query, params);

      // 处理图片URL
      const processedProducts = featuredProducts.map(product => {
        if (product.image_urls) {
          try {
            product.image_urls = JSON.parse(product.image_urls);
          } catch (e) {
            product.image_urls = [];
          }
        }
        return product;
      });

      res.json({
        success: true,
        message: '获取精选商品成功',
        data: processedProducts,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Get featured products error', {
        error: error.message,
        query: req.query
      });

      res.status(500).json({
        success: false,
        message: '获取精选商品失败'
      });
    }
  }
);

/**
 * @route GET /api/platform/mall/stats
 * @desc 获取商城统计信息
 * @access Private (Platform Admin)
 */
router.get('/stats', async (req, res) => {
  try {
    // 获取基本统计
    const basicStats = await req.db.query(`
      SELECT 
        COUNT(*) as total_products,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_products,
        COUNT(CASE WHEN featured = true THEN 1 END) as featured_products,
        COUNT(DISTINCT category) as total_categories,
        SUM(sales_count) as total_sales,
        SUM(stock_quantity) as total_stock,
        AVG(price) as avg_price,
        AVG(rating) as avg_rating
      FROM mall_products
      WHERE status != 'inactive'
    `);

    // 获取各分类统计
    const categoryStats = await req.db.query(`
      SELECT 
        category,
        COUNT(*) as product_count,
        SUM(sales_count) as total_sales,
        AVG(price) as avg_price,
        SUM(stock_quantity) as total_stock
      FROM mall_products
      WHERE status = 'active'
      GROUP BY category
      ORDER BY total_sales DESC
      LIMIT 10
    `);

    // 获取热销商品
    const topSellingProducts = await req.db.query(`
      SELECT 
        id, name, category, price, sales_count, stock_quantity
      FROM mall_products
      WHERE status = 'active'
      ORDER BY sales_count DESC
      LIMIT 10
    `);

    // 获取低库存商品
    const lowStockProducts = await req.db.query(`
      SELECT 
        id, name, category, price, stock_quantity
      FROM mall_products
      WHERE status = 'active' AND stock_quantity <= 10
      ORDER BY stock_quantity ASC
      LIMIT 10
    `);

    res.json({
      success: true,
      message: '获取商城统计成功',
      data: {
        overview: basicStats[0],
        by_category: categoryStats,
        top_selling: topSellingProducts,
        low_stock: lowStockProducts,
        generated_at: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Get mall stats error', {
      error: error.message,
      adminId: req.admin.id
    });

    res.status(500).json({
      success: false,
      message: '获取商城统计失败'
    });
  }
});

module.exports = router;