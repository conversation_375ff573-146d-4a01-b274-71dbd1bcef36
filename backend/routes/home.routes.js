/**
 * 首页相关路由
 * Home Routes
 */

const express = require('express');
const { verifyToken } = require('../middleware/auth.middleware');
const { identifyTenant } = require('../middleware/tenant.middleware');
const { authenticateTenantUser } = require('../middleware/tenant-auth.middleware');
const { recordTenantUsage } = require('../middleware/usage.middleware');
const ResponseHelper = require('../utils/response-helper');

const router = express.Router();

// 应用中间件
router.use(verifyToken);
router.use(identifyTenant);
router.use(authenticateTenantUser);
router.use(recordTenantUsage);

/**
 * @route   GET /home/<USER>
 * @desc    获取首页数据（仪表板数据）
 * @access  Private (All authenticated users)
 */
router.get('/data', async (req, res) => {
  try {
    const tenantDb = req.tenantDb;
    const userId = req.user.id;

    // 获取用户基本信息
    const [userResult] = await tenantDb.query(
      'SELECT id, username, name, email, role, avatar FROM users WHERE id = ?',
      { replacements: [userId] }
    );

    const userInfo = userResult[0] || {};

    // 获取鹅群统计
    const [flockStats] = await tenantDb.query(
      `
      SELECT 
        COUNT(*) as totalFlocks,
        SUM(currentCount) as totalGeese,
        AVG(avgDailyEggProduction) as avgProduction
      FROM flocks 
      WHERE userId = ? AND status = 'active'
    `,
      { replacements: [userId] }
    );

    // 获取健康数据统计
    const [healthStats] = await tenantDb.query(
      `
      SELECT 
        COUNT(CASE WHEN healthStatus = 'healthy' THEN 1 END) as healthyCount,
        COUNT(CASE WHEN healthStatus = 'sick' THEN 1 END) as sickCount,
        COUNT(CASE WHEN healthStatus = 'critical' THEN 1 END) as criticalCount
      FROM health_records 
      WHERE userId = ? AND checkDate >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    `,
      { replacements: [userId] }
    );

    // 获取今日生产数据
    const [todayProduction] = await tenantDb.query(
      `
      SELECT 
        SUM(eggCount) as todayEggs,
        AVG(temperature) as avgTemperature,
        AVG(humidity) as avgHumidity
      FROM production_records 
      WHERE userId = ? AND DATE(recordedDate) = CURDATE()
    `,
      { replacements: [userId] }
    );

    // 获取最新公告（前3条）
    const [announcements] = await tenantDb.query(`
      SELECT id, title, content, type, createdAt
      FROM announcements 
      WHERE status = 'published' 
      ORDER BY createdAt DESC 
      LIMIT 3
    `);

    // 获取待处理任务
    const [pendingTasks] = await tenantDb.query(
      `
      SELECT 
        COUNT(CASE WHEN type = 'health_check' AND status = 'pending' THEN 1 END) as healthChecks,
        COUNT(CASE WHEN type = 'vaccination' AND status = 'pending' THEN 1 END) as vaccinations,
        COUNT(CASE WHEN type = 'maintenance' AND status = 'pending' THEN 1 END) as maintenance
      FROM tasks 
      WHERE userId = ? AND status = 'pending'
    `,
      { replacements: [userId] }
    );

    const flockData = flockStats[0] || {};
    const healthData = healthStats[0] || {};
    const productionData = todayProduction[0] || {};
    const taskData = pendingTasks[0] || {};

    // 计算健康率
    const totalHealthRecords =
      (healthData.healthyCount || 0) +
      (healthData.sickCount || 0) +
      (healthData.criticalCount || 0);
    const healthRate =
      totalHealthRecords > 0
        ? Math.round((healthData.healthyCount / totalHealthRecords) * 100)
        : 100;

    // 构建响应数据
    const responseData = {
      userInfo: {
        ...userInfo,
        inventoryCount: flockData.totalFlocks || 0,
        healthRate: `${healthRate}%`,
        environmentStatus: productionData.avgTemperature ? '良好' : '未知',
        farmName: userInfo.name ? `${userInfo.name}的养殖场` : '未设置养殖场'
      },
      healthData: {
        healthyCount: healthData.healthyCount || 0,
        sickCount: healthData.sickCount || 0,
        deathCount: healthData.criticalCount || 0,
        statusText:
          healthRate >= 90
            ? '整体健康'
            : healthRate >= 70
              ? '基本健康'
              : '需要关注',
        statusDesc:
          healthRate >= 90 ? '鹅群状态良好，无异常' : '部分鹅群需要关注'
      },
      productionData: {
        todayEggs: productionData.todayEggs || 0,
        avgTemperature: productionData.avgTemperature || 0,
        avgHumidity: productionData.avgHumidity || 0,
        avgProduction: flockData.avgProduction || 0
      },
      announcements: announcements || [],
      tasks: {
        total:
          (taskData.healthChecks || 0) +
          (taskData.vaccinations || 0) +
          (taskData.maintenance || 0),
        healthChecks: taskData.healthChecks || 0,
        vaccinations: taskData.vaccinations || 0,
        maintenance: taskData.maintenance || 0
      },
      flockStats: {
        totalFlocks: flockData.totalFlocks || 0,
        totalGeese: flockData.totalGeese || 0
      }
    };

    return ResponseHelper.success(res, responseData, '获取首页数据成功');
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取首页数据失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '获取首页数据失败',
      error:
        process.env.NODE_ENV === 'development'
          ? error.message
          : '服务器内部错误'
    });
  }
});

/**
 * @route   GET /home/<USER>
 * @desc    获取公告列表
 * @access  Private
 */
router.get('/announcements', async (req, res) => {
  try {
    const { page = 1, limit = 10, type = '' } = req.query;
    const offset = (parseInt(page) - 1) * parseInt(limit);

    let whereClause = 'WHERE status = "published"';
    const params = [];

    if (type) {
      whereClause += ' AND type = ?';
      params.push(type);
    }

    // 获取总数
    const [countResult] = await req.tenantDb.query(
      `
      SELECT COUNT(*) as total FROM announcements ${whereClause}
    `,
      { replacements: params }
    );

    // 获取列表数据
    const [announcements] = await req.tenantDb.query(
      `
      SELECT id, title, content, type, priority, publishedAt, createdAt
      FROM announcements 
      ${whereClause}
      ORDER BY priority DESC, publishedAt DESC 
      LIMIT ? OFFSET ?
    `,
      { replacements: [...params, parseInt(limit), offset] }
    );

    res.json({
      success: true,
      message: '获取公告列表成功',
      data: {
        items: announcements,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: countResult[0].total,
          pages: Math.ceil(countResult[0].total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('获取公告列表失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '获取公告列表失败'
    });
  }
});

module.exports = router;
