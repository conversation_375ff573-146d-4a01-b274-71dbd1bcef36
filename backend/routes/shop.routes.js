const express = require('express');
const shopController = require('../controllers/shop.controller');
const authMiddleware = require('../middleware/auth.middleware');

const router = express.Router();

// 公共路由（不需要认证）
router.get('/products', shopController.getProducts);
router.get('/products/:id', shopController.getProductById);
router.get('/categories', shopController.getProductCategories);

// 需要管理员权限的路由
router.post('/products', authMiddleware.requireAuth, authMiddleware.requireAdmin, shopController.createProduct);
router.put('/products/:id', authMiddleware.requireAuth, authMiddleware.requireAdmin, shopController.updateProduct);
router.delete('/products/:id', authMiddleware.requireAuth, authMiddleware.requireAdmin, shopController.deleteProduct);

module.exports = router;