const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const { authService, passwordUtils } = require('../../middleware/auth-unified');
const winston = require('winston');

const router = express.Router();

// Logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

// 登录限流 - 租户用户限制相对宽松
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 10, // 最多10次登录尝试
  message: {
    success: false,
    message: '登录尝试过于频繁，请15分钟后重试',
    code: 'TOO_MANY_LOGIN_ATTEMPTS'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return `${req.ip}-${req.body.username || req.body.email || 'anonymous'}`;
  }
});

// 注册限流
const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 5, // 最多5次注册尝试
  message: {
    success: false,
    message: '注册请求过于频繁，请1小时后重试',
    code: 'TOO_MANY_REGISTER_ATTEMPTS'
  }
});

/**
 * @route POST /api/tenant/auth/login
 * @desc 租户用户登录
 * @access Public
 */
router.post('/login',
  loginLimiter,
  [
    body('username')
      .notEmpty()
      .withMessage('用户名不能为空'),
    
    body('password')
      .notEmpty()
      .withMessage('密码不能为空')
      .isLength({ min: 6 })
      .withMessage('密码至少6个字符'),
    
    body('tenantCode')
      .notEmpty()
      .withMessage('租户代码不能为空')
      .isLength({ min: 2, max: 50 })
      .withMessage('租户代码长度必须在2-50个字符之间')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const { username, password, tenantCode, rememberMe = false } = req.body;

      // 记录登录尝试
      logger.info('Tenant user login attempt', {
        username,
        tenantCode,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
      });

      // 认证租户用户
      const result = await authService.authenticateTenantUser(
        req.db,
        tenantCode,
        username,
        password
      );

      // 记录成功登录
      logger.info('Tenant user login successful', {
        userId: result.user.id,
        username: result.user.username,
        tenantId: result.user.tenant_id,
        tenantCode: result.tenant.tenant_code,
        role: result.user.role,
        ip: req.ip
      });

      // 更新最后登录时间
      await req.db.query(
        'UPDATE tenant_users SET last_login_at = NOW() WHERE id = ?',
        [result.user.id]
      );

      // 设置安全的Cookie选项
      const cookieOptions = {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: rememberMe ? 7 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000 // 7天或1天
      };

      // 设置认证Cookie
      res.cookie('tenant_token', result.token, cookieOptions);

      res.json({
        success: true,
        message: '登录成功',
        data: {
          user: result.user,
          tenant: result.tenant,
          expiresIn: result.expiresIn,
          permissions: result.user.permissions || []
        }
      });

    } catch (error) {
      // 记录登录失败
      logger.error('Tenant user login failed', {
        username: req.body.username,
        tenantCode: req.body.tenantCode,
        ip: req.ip,
        error: error.message,
        timestamp: new Date().toISOString()
      });

      // 根据错误类型返回不同状态码
      if (error.message.includes('租户不存在') || 
          error.message.includes('用户名或密码错误')) {
        return res.status(401).json({
          success: false,
          message: '租户代码、用户名或密码错误',
          code: 'INVALID_CREDENTIALS'
        });
      }

      if (error.message.includes('账户已被停用') ||
          error.message.includes('租户已被停用')) {
        return res.status(403).json({
          success: false,
          message: '账户或租户已被停用，请联系管理员',
          code: 'ACCOUNT_DISABLED'
        });
      }

      if (error.message.includes('租户订阅已过期')) {
        return res.status(403).json({
          success: false,
          message: '租户订阅已过期，请联系管理员续费',
          code: 'SUBSCRIPTION_EXPIRED'
        });
      }

      res.status(500).json({
        success: false,
        message: '登录过程中发生错误',
        code: 'LOGIN_ERROR'
      });
    }
  }
);

/**
 * @route POST /api/tenant/auth/register
 * @desc 租户用户注册（需要租户管理员邀请）
 * @access Public with invitation token
 */
router.post('/register',
  registerLimiter,
  [
    body('username')
      .notEmpty()
      .withMessage('用户名不能为空')
      .isLength({ min: 3, max: 50 })
      .withMessage('用户名长度必须在3-50个字符之间')
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('用户名只能包含字母、数字和下划线'),
    
    body('email')
      .notEmpty()
      .withMessage('邮箱不能为空')
      .isEmail()
      .normalizeEmail()
      .withMessage('请输入有效的邮箱地址'),
    
    body('password')
      .isLength({ min: 8 })
      .withMessage('密码至少需要8个字符')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('密码必须包含大小写字母和数字'),
    
    body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.password) {
          throw new Error('确认密码不匹配');
        }
        return true;
      }),
    
    body('realName')
      .notEmpty()
      .withMessage('真实姓名不能为空')
      .isLength({ min: 2, max: 100 })
      .withMessage('真实姓名长度必须在2-100个字符之间'),
    
    body('phone')
      .optional()
      .isMobilePhone('zh-CN')
      .withMessage('请输入有效的手机号码'),
    
    body('tenantCode')
      .notEmpty()
      .withMessage('租户代码不能为空'),
    
    body('invitationCode')
      .notEmpty()
      .withMessage('邀请码不能为空')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        username,
        email,
        password,
        realName,
        phone,
        tenantCode,
        invitationCode
      } = req.body;

      // 验证租户是否存在且活跃
      const tenant = await req.db.query(
        'SELECT * FROM tenants WHERE tenant_code = ? AND status = "active"',
        [tenantCode]
      );

      if (!tenant || tenant.length === 0) {
        return res.status(400).json({
          success: false,
          message: '租户不存在或已被停用',
          code: 'INVALID_TENANT'
        });
      }

      const tenantData = tenant[0];

      // TODO: 验证邀请码的逻辑
      // 这里应该验证邀请码是否有效，是否已被使用等
      // 暂时使用简单的固定邀请码验证
      if (invitationCode !== 'INVITE2024') {
        return res.status(400).json({
          success: false,
          message: '无效的邀请码',
          code: 'INVALID_INVITATION'
        });
      }

      // 检查用户名是否在该租户下已存在
      const existingUser = await req.db.query(
        'SELECT id FROM tenant_users WHERE tenant_id = ? AND username = ?',
        [tenantData.id, username]
      );

      if (existingUser && existingUser.length > 0) {
        return res.status(409).json({
          success: false,
          message: '用户名已存在',
          code: 'USERNAME_EXISTS'
        });
      }

      // 检查邮箱是否已被使用
      if (email) {
        const existingEmail = await req.db.query(
          'SELECT id FROM tenant_users WHERE email = ?',
          [email]
        );

        if (existingEmail && existingEmail.length > 0) {
          return res.status(409).json({
            success: false,
            message: '邮箱已被使用',
            code: 'EMAIL_EXISTS'
          });
        }
      }

      // 检查租户用户数量限制
      const userCount = await req.db.query(
        'SELECT COUNT(*) as count FROM tenant_users WHERE tenant_id = ? AND status != "inactive"',
        [tenantData.id]
      );

      if (userCount[0].count >= tenantData.max_users) {
        return res.status(400).json({
          success: false,
          message: '租户用户数量已达上限',
          code: 'USER_LIMIT_EXCEEDED'
        });
      }

      // 加密密码
      const hashedPassword = await passwordUtils.hash(password);

      // 创建用户
      const insertResult = await req.db.query(`
        INSERT INTO tenant_users (
          tenant_id, username, email, phone, password, real_name, 
          role, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, 'user', 'active', NOW(), NOW())
      `, [
        tenantData.id, username, email, phone, hashedPassword, realName
      ]);

      // 记录注册日志
      logger.info('Tenant user registered', {
        userId: insertResult.insertId,
        username,
        email,
        tenantId: tenantData.id,
        tenantCode,
        ip: req.ip
      });

      // 获取创建的用户信息（不包含密码）
      const newUser = await req.db.query(`
        SELECT 
          id, tenant_id, username, email, phone, real_name, role, status,
          avatar_url, created_at, updated_at
        FROM tenant_users WHERE id = ?
      `, [insertResult.insertId]);

      res.status(201).json({
        success: true,
        message: '注册成功',
        data: {
          user: newUser[0],
          tenant: {
            id: tenantData.id,
            tenant_code: tenantData.tenant_code,
            company_name: tenantData.company_name
          }
        }
      });

    } catch (error) {
      logger.error('Tenant user registration failed', {
        username: req.body.username,
        email: req.body.email,
        tenantCode: req.body.tenantCode,
        error: error.message,
        ip: req.ip
      });

      res.status(500).json({
        success: false,
        message: '注册过程中发生错误'
      });
    }
  }
);

/**
 * @route POST /api/tenant/auth/logout
 * @desc 租户用户登出
 * @access Private
 */
router.post('/logout', async (req, res) => {
  try {
    // 清除认证Cookie
    res.clearCookie('tenant_token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });

    // 记录登出
    logger.info('Tenant user logout', {
      userId: req.user?.id,
      tenantId: req.tenant?.id,
      ip: req.ip,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: '已安全登出'
    });

  } catch (error) {
    logger.error('Tenant logout error', {
      error: error.message,
      ip: req.ip
    });

    res.status(500).json({
      success: false,
      message: '登出过程中发生错误'
    });
  }
});

/**
 * @route GET /api/tenant/auth/profile
 * @desc 获取当前用户信息
 * @access Private (Tenant User)
 */
router.get('/profile', async (req, res) => {
  try {
    // 用户信息由认证中间件提供
    const user = req.user;
    const tenant = req.tenant;

    if (!user || !tenant) {
      return res.status(401).json({
        success: false,
        message: '未授权访问'
      });
    }

    res.json({
      success: true,
      message: '获取用户信息成功',
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          phone: user.phone,
          real_name: user.real_name,
          role: user.role,
          status: user.status,
          avatar_url: user.avatar_url,
          last_login_at: user.last_login_at,
          created_at: user.created_at
        },
        tenant: {
          id: tenant.id,
          tenant_code: tenant.tenant_code,
          company_name: tenant.company_name,
          subscription_plan: tenant.subscription_plan,
          status: tenant.status
        }
      }
    });

  } catch (error) {
    logger.error('Get tenant user profile error', {
      error: error.message,
      userId: req.user?.id,
      ip: req.ip
    });

    res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    });
  }
});

/**
 * @route PUT /api/tenant/auth/profile
 * @desc 更新用户信息
 * @access Private (Tenant User)
 */
router.put('/profile',
  [
    body('email').optional().isEmail().normalizeEmail().withMessage('请输入有效的邮箱地址'),
    body('phone').optional().isMobilePhone('zh-CN').withMessage('请输入有效的手机号码'),
    body('realName').optional().isLength({ min: 2, max: 100 }).withMessage('真实姓名长度必须在2-100个字符之间'),
    body('avatarUrl').optional().isURL().withMessage('头像URL格式不正确')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const userId = req.user.id;
      const { email, phone, realName, avatarUrl } = req.body;

      // 如果更新邮箱，检查是否已被使用
      if (email) {
        const existingEmail = await req.db.query(
          'SELECT id FROM tenant_users WHERE email = ? AND id != ?',
          [email, userId]
        );

        if (existingEmail && existingEmail.length > 0) {
          return res.status(409).json({
            success: false,
            message: '邮箱已被使用',
            code: 'EMAIL_EXISTS'
          });
        }
      }

      // 构建更新字段
      const updateFields = [];
      const updateValues = [];

      if (email !== undefined) {
        updateFields.push('email = ?');
        updateValues.push(email);
      }

      if (phone !== undefined) {
        updateFields.push('phone = ?');
        updateValues.push(phone);
      }

      if (realName !== undefined) {
        updateFields.push('real_name = ?');
        updateValues.push(realName);
      }

      if (avatarUrl !== undefined) {
        updateFields.push('avatar_url = ?');
        updateValues.push(avatarUrl);
      }

      if (updateFields.length === 0) {
        return res.status(400).json({
          success: false,
          message: '没有可更新的字段'
        });
      }

      // 添加更新时间和用户ID
      updateFields.push('updated_at = NOW()');
      updateValues.push(userId);

      // 执行更新
      await req.db.query(
        `UPDATE tenant_users SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );

      // 获取更新后的用户信息
      const updatedUser = await req.db.query(`
        SELECT 
          id, tenant_id, username, email, phone, real_name, role, status,
          avatar_url, last_login_at, created_at, updated_at
        FROM tenant_users WHERE id = ?
      `, [userId]);

      // 记录操作日志
      logger.info('Tenant user profile updated', {
        userId,
        updatedFields: Object.keys(req.body),
        tenantId: req.tenant.id,
        ip: req.ip
      });

      res.json({
        success: true,
        message: '用户信息更新成功',
        data: {
          user: updatedUser[0]
        }
      });

    } catch (error) {
      logger.error('Update tenant user profile error', {
        error: error.message,
        userId: req.user?.id,
        ip: req.ip
      });

      res.status(500).json({
        success: false,
        message: '用户信息更新失败'
      });
    }
  }
);

/**
 * @route POST /api/tenant/auth/change-password
 * @desc 修改密码
 * @access Private (Tenant User)
 */
router.post('/change-password',
  [
    body('currentPassword')
      .notEmpty()
      .withMessage('当前密码不能为空'),
    
    body('newPassword')
      .isLength({ min: 8 })
      .withMessage('新密码至少需要8个字符')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('新密码必须包含大小写字母和数字'),
    
    body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.newPassword) {
          throw new Error('确认密码不匹配');
        }
        return true;
      })
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const { currentPassword, newPassword } = req.body;
      const userId = req.user.id;

      // 验证当前密码
      const user = await req.db.query(
        'SELECT password FROM tenant_users WHERE id = ?',
        [userId]
      );

      if (!user || !Array.isArray(user) || user.length === 0) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      const isCurrentPasswordValid = await passwordUtils.compare(
        currentPassword,
        user[0].password
      );

      if (!isCurrentPasswordValid) {
        return res.status(400).json({
          success: false,
          message: '当前密码不正确',
          code: 'INVALID_CURRENT_PASSWORD'
        });
      }

      // 加密新密码
      const hashedNewPassword = await passwordUtils.hash(newPassword);

      // 更新密码
      await req.db.query(
        'UPDATE tenant_users SET password = ?, updated_at = NOW() WHERE id = ?',
        [hashedNewPassword, userId]
      );

      // 记录密码修改
      logger.info('Tenant user password changed', {
        userId,
        username: req.user.username,
        tenantId: req.tenant.id,
        ip: req.ip,
        timestamp: new Date().toISOString()
      });

      res.json({
        success: true,
        message: '密码修改成功'
      });

    } catch (error) {
      logger.error('Change tenant user password error', {
        userId: req.user?.id,
        error: error.message,
        ip: req.ip
      });

      res.status(500).json({
        success: false,
        message: '密码修改失败'
      });
    }
  }
);

/**
 * @route GET /api/tenant/auth/verify-token
 * @desc 验证token有效性
 * @access Private (Tenant User)
 */
router.get('/verify-token', async (req, res) => {
  try {
    // 如果能到达这里，说明token有效（通过认证中间件验证）
    res.json({
      success: true,
      message: 'Token有效',
      data: {
        user: req.user,
        tenant: req.tenant,
        isValid: true
      }
    });

  } catch (error) {
    res.status(401).json({
      success: false,
      message: 'Token无效',
      isValid: false
    });
  }
});

module.exports = router;