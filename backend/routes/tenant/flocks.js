const express = require('express');
const { body, query, validationResult } = require('express-validator');
const winston = require('winston');

const router = express.Router();

// Logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

/**
 * @route GET /api/tenant/:tenantCode/flocks
 * @desc 获取鹅群列表
 * @access Private (Tenant User)
 */
router.get('/',
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
    query('status').optional().isIn(['active', 'inactive', 'archived']).withMessage('无效的状态值'),
    query('breed').optional().isLength({ max: 50 }).withMessage('品种名称最多50个字符'),
    query('search').optional().isLength({ max: 100 }).withMessage('搜索关键词最多100个字符')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        page = 1,
        pageSize = 20,
        status,
        breed,
        search,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = req.query;

      const tenantId = req.tenant.id;

      // 构建查询条件（自动添加租户过滤）
      const whereClauses = ['tenant_id = ?'];
      const params = [tenantId];

      if (status) {
        whereClauses.push('status = ?');
        params.push(status);
      }

      if (breed) {
        whereClauses.push('breed LIKE ?');
        params.push(`%${breed}%`);
      }

      if (search) {
        whereClauses.push('(name LIKE ? OR flock_code LIKE ? OR location LIKE ?)');
        const searchTerm = `%${search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      const whereClause = `WHERE ${whereClauses.join(' AND ')}`;

      // 验证排序字段
      const allowedSortFields = ['created_at', 'name', 'breed', 'current_count', 'birth_date'];
      const safeSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
      const safeSortOrder = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

      // 构建查询
      const baseQuery = `
        SELECT 
          f.id, f.flock_code, f.name, f.breed, f.initial_count, f.current_count,
          f.birth_date, f.source, f.status, f.location, f.notes, f.created_at, f.updated_at,
          m.real_name as manager_name,
          c.real_name as creator_name,
          DATEDIFF(CURDATE(), f.birth_date) as age_days
        FROM flocks f
        LEFT JOIN tenant_users m ON f.manager_id = m.id
        LEFT JOIN tenant_users c ON f.created_by = c.id
        ${whereClause}
        ORDER BY ${safeSortBy} ${safeSortOrder}
      `;

      const countQuery = `SELECT COUNT(*) as total FROM flocks f ${whereClause}`;

      // 执行分页查询
      const result = await req.db.paginate(baseQuery, params, parseInt(page), parseInt(pageSize), countQuery);

      // 获取统计信息
      const stats = await req.db.query(`
        SELECT 
          status,
          breed,
          COUNT(*) as count,
          SUM(current_count) as total_geese,
          AVG(current_count) as avg_count
        FROM flocks
        WHERE tenant_id = ?
        GROUP BY status, breed
        ORDER BY count DESC
      `, [tenantId]);

      res.json({
        success: true,
        message: '获取鹅群列表成功',
        data: result.data,
        pagination: result.pagination,
        statistics: {
          breakdown: stats
        }
      });

    } catch (error) {
      logger.error('Get flocks error', {
        error: error.message,
        tenantId: req.tenant?.id,
        userId: req.user?.id,
        query: req.query
      });

      res.status(500).json({
        success: false,
        message: '获取鹅群列表失败'
      });
    }
  }
);

/**
 * @route GET /api/tenant/:tenantCode/flocks/:id
 * @desc 获取鹅群详情
 * @access Private (Tenant User)
 */
router.get('/:id', async (req, res) => {
  try {
    const flockId = parseInt(req.params.id);
    const tenantId = req.tenant.id;

    if (isNaN(flockId)) {
      return res.status(400).json({
        success: false,
        message: '无效的鹅群ID'
      });
    }

    // 获取鹅群详情（确保数据隔离）
    const flock = await req.db.query(`
      SELECT 
        f.*,
        m.real_name as manager_name, m.username as manager_username,
        c.real_name as creator_name, c.username as creator_username,
        DATEDIFF(CURDATE(), f.birth_date) as age_days
      FROM flocks f
      LEFT JOIN tenant_users m ON f.manager_id = m.id
      LEFT JOIN tenant_users c ON f.created_by = c.id
      WHERE f.id = ? AND f.tenant_id = ?
    `, [flockId, tenantId]);

    if (!flock || flock.length === 0) {
      return res.status(404).json({
        success: false,
        message: '鹅群不存在'
      });
    }

    const flockData = flock[0];

    // 获取相关统计数据
    const statistics = await req.db.query(`
      SELECT 
        COUNT(DISTINCT hr.id) as health_records_count,
        COUNT(DISTINCT pr.id) as production_records_count,
        COALESCE(SUM(CASE WHEN pr.record_type = 'egg' THEN pr.egg_count ELSE 0 END), 0) as total_eggs,
        COALESCE(AVG(CASE WHEN pr.record_type = 'weight' THEN pr.average_weight ELSE NULL END), 0) as avg_weight
      FROM flocks f
      LEFT JOIN health_records hr ON f.id = hr.flock_id
      LEFT JOIN production_records pr ON f.id = pr.flock_id
      WHERE f.id = ? AND f.tenant_id = ?
    `, [flockId, tenantId]);

    flockData.statistics = statistics[0];

    res.json({
      success: true,
      message: '获取鹅群详情成功',
      data: flockData
    });

  } catch (error) {
    logger.error('Get flock details error', {
      error: error.message,
      tenantId: req.tenant?.id,
      userId: req.user?.id,
      flockId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '获取鹅群详情失败'
    });
  }
});

/**
 * @route POST /api/tenant/:tenantCode/flocks
 * @desc 创建鹅群
 * @access Private (Tenant User)
 */
router.post('/',
  [
    body('flockCode')
      .notEmpty().withMessage('鹅群编号不能为空')
      .isLength({ min: 2, max: 50 }).withMessage('鹅群编号长度必须在2-50个字符之间')
      .matches(/^[A-Za-z0-9_-]+$/).withMessage('鹅群编号只能包含字母、数字、下划线和连字符'),
    
    body('name')
      .notEmpty().withMessage('鹅群名称不能为空')
      .isLength({ min: 2, max: 100 }).withMessage('鹅群名称长度必须在2-100个字符之间'),
    
    body('breed')
      .notEmpty().withMessage('品种不能为空')
      .isLength({ min: 2, max: 50 }).withMessage('品种名称长度必须在2-50个字符之间'),
    
    body('initialCount')
      .notEmpty().withMessage('初始数量不能为空')
      .isInt({ min: 1, max: 10000 }).withMessage('初始数量必须在1-10000之间'),
    
    body('birthDate')
      .notEmpty().withMessage('出生日期不能为空')
      .isISO8601().withMessage('出生日期格式无效'),
    
    body('source')
      .optional()
      .isIn(['purchase', 'hatch', 'transfer']).withMessage('无效的来源类型'),
    
    body('location')
      .optional()
      .isLength({ max: 200 }).withMessage('饲养地点最多200个字符'),
    
    body('managerId')
      .optional()
      .isInt({ min: 1 }).withMessage('负责人ID必须是正整数'),
    
    body('notes')
      .optional()
      .isLength({ max: 1000 }).withMessage('备注最多1000个字符')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        flockCode,
        name,
        breed,
        initialCount,
        birthDate,
        source = 'purchase',
        location,
        managerId,
        notes
      } = req.body;

      const tenantId = req.tenant.id;
      const userId = req.user.id;

      // 检查鹅群编号在租户下是否唯一
      const existingFlock = await req.db.query(
        'SELECT id FROM flocks WHERE tenant_id = ? AND flock_code = ?',
        [tenantId, flockCode]
      );

      if (existingFlock && existingFlock.length > 0) {
        return res.status(409).json({
          success: false,
          message: '鹅群编号已存在',
          code: 'FLOCK_CODE_EXISTS'
        });
      }

      // 检查租户鹅群数量限制
      const flockCount = await req.db.query(
        'SELECT COUNT(*) as count FROM flocks WHERE tenant_id = ? AND status != "archived"',
        [tenantId]
      );

      if (flockCount[0].count >= req.tenant.max_flocks) {
        return res.status(400).json({
          success: false,
          message: '鹅群数量已达上限',
          code: 'FLOCK_LIMIT_EXCEEDED'
        });
      }

      // 验证负责人是否属于当前租户
      if (managerId) {
        const manager = await req.db.query(
          'SELECT id FROM tenant_users WHERE id = ? AND tenant_id = ? AND status = "active"',
          [managerId, tenantId]
        );

        if (!manager || manager.length === 0) {
          return res.status(400).json({
            success: false,
            message: '无效的负责人ID',
            code: 'INVALID_MANAGER'
          });
        }
      }

      // 插入新鹅群
      const insertResult = await req.db.query(`
        INSERT INTO flocks (
          tenant_id, flock_code, name, breed, initial_count, current_count,
          birth_date, source, status, location, manager_id, notes,
          created_by, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, ?, ?, ?, NOW(), NOW())
      `, [
        tenantId, flockCode, name, breed, initialCount, initialCount,
        birthDate, source, location, managerId, notes, userId
      ]);

      // 记录操作日志
      logger.info('Flock created', {
        flockId: insertResult.insertId,
        flockCode,
        name,
        breed,
        tenantId,
        createdBy: userId,
        createdByUsername: req.user.username
      });

      // 获取创建的鹅群信息
      const newFlock = await req.db.query(`
        SELECT 
          f.*,
          m.real_name as manager_name,
          c.real_name as creator_name,
          DATEDIFF(CURDATE(), f.birth_date) as age_days
        FROM flocks f
        LEFT JOIN tenant_users m ON f.manager_id = m.id
        LEFT JOIN tenant_users c ON f.created_by = c.id
        WHERE f.id = ?
      `, [insertResult.insertId]);

      res.status(201).json({
        success: true,
        message: '鹅群创建成功',
        data: newFlock[0]
      });

    } catch (error) {
      logger.error('Create flock error', {
        error: error.message,
        tenantId: req.tenant?.id,
        userId: req.user?.id,
        requestBody: req.body
      });

      res.status(500).json({
        success: false,
        message: '创建鹅群失败'
      });
    }
  }
);

/**
 * @route PUT /api/tenant/:tenantCode/flocks/:id
 * @desc 更新鹅群信息
 * @access Private (Tenant User)
 */
router.put('/:id',
  [
    body('name').optional().isLength({ min: 2, max: 100 }).withMessage('鹅群名称长度必须在2-100个字符之间'),
    body('breed').optional().isLength({ min: 2, max: 50 }).withMessage('品种名称长度必须在2-50个字符之间'),
    body('currentCount').optional().isInt({ min: 0, max: 10000 }).withMessage('当前数量必须在0-10000之间'),
    body('source').optional().isIn(['purchase', 'hatch', 'transfer']).withMessage('无效的来源类型'),
    body('status').optional().isIn(['active', 'inactive', 'archived']).withMessage('无效的状态值'),
    body('location').optional().isLength({ max: 200 }).withMessage('饲养地点最多200个字符'),
    body('managerId').optional().isInt({ min: 1 }).withMessage('负责人ID必须是正整数'),
    body('notes').optional().isLength({ max: 1000 }).withMessage('备注最多1000个字符')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const flockId = parseInt(req.params.id);
      const tenantId = req.tenant.id;

      if (isNaN(flockId)) {
        return res.status(400).json({
          success: false,
          message: '无效的鹅群ID'
        });
      }

      // 检查鹅群是否存在且属于当前租户
      const existingFlock = await req.db.query(
        'SELECT * FROM flocks WHERE id = ? AND tenant_id = ?',
        [flockId, tenantId]
      );

      if (!existingFlock || existingFlock.length === 0) {
        return res.status(404).json({
          success: false,
          message: '鹅群不存在'
        });
      }

      // 验证负责人是否属于当前租户
      if (req.body.managerId) {
        const manager = await req.db.query(
          'SELECT id FROM tenant_users WHERE id = ? AND tenant_id = ? AND status = "active"',
          [req.body.managerId, tenantId]
        );

        if (!manager || manager.length === 0) {
          return res.status(400).json({
            success: false,
            message: '无效的负责人ID',
            code: 'INVALID_MANAGER'
          });
        }
      }

      // 构建更新字段
      const updateFields = [];
      const updateValues = [];

      const allowedFields = {
        name: 'name',
        breed: 'breed',
        currentCount: 'current_count',
        source: 'source',
        status: 'status',
        location: 'location',
        managerId: 'manager_id',
        notes: 'notes'
      };

      Object.keys(req.body).forEach(key => {
        const dbField = allowedFields[key];
        if (dbField && req.body[key] !== undefined) {
          updateFields.push(`${dbField} = ?`);
          updateValues.push(req.body[key]);
        }
      });

      if (updateFields.length === 0) {
        return res.status(400).json({
          success: false,
          message: '没有可更新的字段'
        });
      }

      // 添加更新时间
      updateFields.push('updated_at = NOW()');
      updateValues.push(flockId);

      // 执行更新
      await req.db.query(
        `UPDATE flocks SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );

      // 记录操作日志
      logger.info('Flock updated', {
        flockId,
        updatedFields: Object.keys(req.body),
        tenantId,
        updatedBy: req.user.id,
        updatedByUsername: req.user.username
      });

      // 获取更新后的鹅群信息
      const updatedFlock = await req.db.query(`
        SELECT 
          f.*,
          m.real_name as manager_name,
          c.real_name as creator_name,
          DATEDIFF(CURDATE(), f.birth_date) as age_days
        FROM flocks f
        LEFT JOIN tenant_users m ON f.manager_id = m.id
        LEFT JOIN tenant_users c ON f.created_by = c.id
        WHERE f.id = ?
      `, [flockId]);

      res.json({
        success: true,
        message: '鹅群信息更新成功',
        data: updatedFlock[0]
      });

    } catch (error) {
      logger.error('Update flock error', {
        error: error.message,
        tenantId: req.tenant?.id,
        userId: req.user?.id,
        flockId: req.params.id,
        requestBody: req.body
      });

      res.status(500).json({
        success: false,
        message: '更新鹅群信息失败'
      });
    }
  }
);

/**
 * @route DELETE /api/tenant/:tenantCode/flocks/:id
 * @desc 删除鹅群（软删除）
 * @access Private (Tenant User with proper permissions)
 */
router.delete('/:id', async (req, res) => {
  try {
    const flockId = parseInt(req.params.id);
    const tenantId = req.tenant.id;

    if (isNaN(flockId)) {
      return res.status(400).json({
        success: false,
        message: '无效的鹅群ID'
      });
    }

    // 检查鹅群是否存在且属于当前租户
    const flock = await req.db.query(
      'SELECT * FROM flocks WHERE id = ? AND tenant_id = ?',
      [flockId, tenantId]
    );

    if (!flock || flock.length === 0) {
      return res.status(404).json({
        success: false,
        message: '鹅群不存在'
      });
    }

    // 检查是否有权限删除（只有管理员或创建者可以删除）
    if (req.user.role !== 'admin' && flock[0].created_by !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '没有权限删除该鹅群',
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }

    // 软删除（更改状态为archived）
    await req.db.query(
      'UPDATE flocks SET status = "archived", updated_at = NOW() WHERE id = ?',
      [flockId]
    );

    // 记录操作日志
    logger.warn('Flock deleted (soft delete)', {
      flockId,
      flockCode: flock[0].flock_code,
      name: flock[0].name,
      tenantId,
      deletedBy: req.user.id,
      deletedByUsername: req.user.username
    });

    res.json({
      success: true,
      message: '鹅群已删除'
    });

  } catch (error) {
    logger.error('Delete flock error', {
      error: error.message,
      tenantId: req.tenant?.id,
      userId: req.user?.id,
      flockId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '删除鹅群失败'
    });
  }
});

/**
 * @route GET /api/tenant/:tenantCode/flocks/stats
 * @desc 获取鹅群统计信息
 * @access Private (Tenant User)
 */
router.get('/stats', async (req, res) => {
  try {
    const tenantId = req.tenant.id;

    // 基本统计
    const basicStats = await req.db.query(`
      SELECT 
        COUNT(*) as total_flocks,
        SUM(current_count) as total_geese,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_flocks,
        COUNT(DISTINCT breed) as total_breeds,
        AVG(current_count) as avg_flock_size,
        AVG(DATEDIFF(CURDATE(), birth_date)) as avg_age_days
      FROM flocks
      WHERE tenant_id = ? AND status != 'archived'
    `, [tenantId]);

    // 按品种统计
    const breedStats = await req.db.query(`
      SELECT 
        breed,
        COUNT(*) as flock_count,
        SUM(current_count) as total_count,
        AVG(current_count) as avg_count,
        AVG(DATEDIFF(CURDATE(), birth_date)) as avg_age_days
      FROM flocks
      WHERE tenant_id = ? AND status = 'active'
      GROUP BY breed
      ORDER BY total_count DESC
    `, [tenantId]);

    // 按月份统计新增鹅群
    const monthlyStats = await req.db.query(`
      SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        COUNT(*) as new_flocks,
        SUM(initial_count) as new_geese
      FROM flocks
      WHERE tenant_id = ? AND created_at >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
      GROUP BY DATE_FORMAT(created_at, '%Y-%m')
      ORDER BY month DESC
      LIMIT 12
    `, [tenantId]);

    // 鹅群年龄分布
    const ageDistribution = await req.db.query(`
      SELECT 
        CASE 
          WHEN DATEDIFF(CURDATE(), birth_date) <= 30 THEN '0-1月'
          WHEN DATEDIFF(CURDATE(), birth_date) <= 90 THEN '1-3月'
          WHEN DATEDIFF(CURDATE(), birth_date) <= 180 THEN '3-6月'
          WHEN DATEDIFF(CURDATE(), birth_date) <= 365 THEN '6-12月'
          ELSE '1年以上'
        END as age_group,
        COUNT(*) as flock_count,
        SUM(current_count) as geese_count
      FROM flocks
      WHERE tenant_id = ? AND status = 'active'
      GROUP BY age_group
      ORDER BY 
        CASE 
          WHEN age_group = '0-1月' THEN 1
          WHEN age_group = '1-3月' THEN 2
          WHEN age_group = '3-6月' THEN 3
          WHEN age_group = '6-12月' THEN 4
          ELSE 5
        END
    `, [tenantId]);

    res.json({
      success: true,
      message: '获取鹅群统计成功',
      data: {
        overview: basicStats[0],
        by_breed: breedStats,
        monthly_growth: monthlyStats,
        age_distribution: ageDistribution,
        generated_at: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Get flock stats error', {
      error: error.message,
      tenantId: req.tenant?.id,
      userId: req.user?.id
    });

    res.status(500).json({
      success: false,
      message: '获取鹅群统计失败'
    });
  }
});

module.exports = router;