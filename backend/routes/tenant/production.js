const express = require('express');
const { body, query, validationResult } = require('express-validator');
const winston = require('winston');

const router = express.Router();

// Logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

/**
 * @route GET /api/tenant/:tenantCode/production
 * @desc 获取生产记录列表
 * @access Private (Tenant User)
 */
router.get('/',
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
    query('flockId').optional().isInt({ min: 1 }).withMessage('鹅群ID必须是正整数'),
    query('recordType').optional().isIn(['egg', 'weight', 'feed', 'growth']).withMessage('无效的记录类型'),
    query('dateFrom').optional().isISO8601().withMessage('开始日期格式无效'),
    query('dateTo').optional().isISO8601().withMessage('结束日期格式无效')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        page = 1,
        pageSize = 20,
        flockId,
        recordType,
        dateFrom,
        dateTo,
        sortBy = 'record_date',
        sortOrder = 'DESC'
      } = req.query;

      const tenantId = req.tenant.id;

      // 构建查询条件（自动添加租户过滤）
      const whereClauses = ['pr.tenant_id = ?'];
      const params = [tenantId];

      if (flockId) {
        whereClauses.push('pr.flock_id = ?');
        params.push(parseInt(flockId));
      }

      if (recordType) {
        whereClauses.push('pr.record_type = ?');
        params.push(recordType);
      }

      if (dateFrom) {
        whereClauses.push('pr.record_date >= ?');
        params.push(dateFrom);
      }

      if (dateTo) {
        whereClauses.push('pr.record_date <= ?');
        params.push(dateTo);
      }

      const whereClause = `WHERE ${whereClauses.join(' AND ')}`;

      // 验证排序字段
      const allowedSortFields = ['record_date', 'record_type', 'created_at'];
      const safeSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'record_date';
      const safeSortOrder = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

      // 构建查询
      const baseQuery = `
        SELECT 
          pr.*,
          f.name as flock_name, f.flock_code, f.breed,
          u.real_name as creator_name, u.username as creator_username
        FROM production_records pr
        LEFT JOIN flocks f ON pr.flock_id = f.id
        LEFT JOIN tenant_users u ON pr.created_by = u.id
        ${whereClause}
        ORDER BY pr.${safeSortBy} ${safeSortOrder}
      `;

      const countQuery = `
        SELECT COUNT(*) as total 
        FROM production_records pr 
        ${whereClause}
      `;

      // 执行分页查询
      const result = await req.db.paginate(baseQuery, params, parseInt(page), parseInt(pageSize), countQuery);

      // 处理附件JSON字段
      const recordsWithProcessedData = result.data.map(record => {
        if (record.attachments) {
          try {
            record.attachments = JSON.parse(record.attachments);
          } catch (e) {
            record.attachments = [];
          }
        }
        return record;
      });

      res.json({
        success: true,
        message: '获取生产记录列表成功',
        data: recordsWithProcessedData,
        pagination: result.pagination
      });

    } catch (error) {
      logger.error('Get production records error', {
        error: error.message,
        tenantId: req.tenant?.id,
        userId: req.user?.id,
        query: req.query
      });

      res.status(500).json({
        success: false,
        message: '获取生产记录列表失败'
      });
    }
  }
);

/**
 * @route GET /api/tenant/:tenantCode/production/:id
 * @desc 获取生产记录详情
 * @access Private (Tenant User)
 */
router.get('/:id', async (req, res) => {
  try {
    const recordId = parseInt(req.params.id);
    const tenantId = req.tenant.id;

    if (isNaN(recordId)) {
      return res.status(400).json({
        success: false,
        message: '无效的记录ID'
      });
    }

    // 获取生产记录详情（确保数据隔离）
    const record = await req.db.query(`
      SELECT 
        pr.*,
        f.name as flock_name, f.flock_code, f.breed,
        u.real_name as creator_name, u.username as creator_username
      FROM production_records pr
      LEFT JOIN flocks f ON pr.flock_id = f.id
      LEFT JOIN tenant_users u ON pr.created_by = u.id
      WHERE pr.id = ? AND pr.tenant_id = ?
    `, [recordId, tenantId]);

    if (!record || record.length === 0) {
      return res.status(404).json({
        success: false,
        message: '生产记录不存在'
      });
    }

    const recordData = record[0];

    // 处理附件JSON字段
    if (recordData.attachments) {
      try {
        recordData.attachments = JSON.parse(recordData.attachments);
      } catch (e) {
        recordData.attachments = [];
      }
    }

    res.json({
      success: true,
      message: '获取生产记录详情成功',
      data: recordData
    });

  } catch (error) {
    logger.error('Get production record details error', {
      error: error.message,
      tenantId: req.tenant?.id,
      userId: req.user?.id,
      recordId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '获取生产记录详情失败'
    });
  }
});

/**
 * @route POST /api/tenant/:tenantCode/production
 * @desc 创建生产记录
 * @access Private (Tenant User)
 */
router.post('/',
  [
    body('flockId')
      .notEmpty().withMessage('鹅群ID不能为空')
      .isInt({ min: 1 }).withMessage('鹅群ID必须是正整数'),
    
    body('recordDate')
      .notEmpty().withMessage('记录日期不能为空')
      .isISO8601().withMessage('记录日期格式无效'),
    
    body('recordType')
      .notEmpty().withMessage('记录类型不能为空')
      .isIn(['egg', 'weight', 'feed', 'growth']).withMessage('无效的记录类型'),
    
    // 产蛋记录字段
    body('eggCount')
      .if(body('recordType').equals('egg'))
      .notEmpty().withMessage('产蛋数量不能为空')
      .isInt({ min: 0, max: 10000 }).withMessage('产蛋数量必须在0-10000之间'),
    
    body('eggWeight')
      .if(body('recordType').equals('egg'))
      .optional()
      .isFloat({ min: 0, max: 1000 }).withMessage('蛋重必须在0-1000克之间'),
    
    // 体重记录字段
    body('averageWeight')
      .if(body('recordType').equals('weight'))
      .notEmpty().withMessage('平均体重不能为空')
      .isFloat({ min: 0, max: 50000 }).withMessage('平均体重必须在0-50000克之间'),
    
    body('sampleCount')
      .if(body('recordType').equals('weight'))
      .optional()
      .isInt({ min: 1, max: 1000 }).withMessage('称重样本数必须在1-1000之间'),
    
    // 饲料记录字段
    body('feedType')
      .if(body('recordType').equals('feed'))
      .notEmpty().withMessage('饲料类型不能为空')
      .isLength({ min: 1, max: 100 }).withMessage('饲料类型长度必须在1-100个字符之间'),
    
    body('feedAmount')
      .if(body('recordType').equals('feed'))
      .notEmpty().withMessage('饲料用量不能为空')
      .isFloat({ min: 0, max: 10000 }).withMessage('饲料用量必须在0-10000公斤之间'),
    
    body('feedCost')
      .if(body('recordType').equals('feed'))
      .optional()
      .isFloat({ min: 0, max: 100000 }).withMessage('饲料成本必须在0-100000元之间'),
    
    // 通用字段
    body('weather')
      .optional()
      .isLength({ max: 50 }).withMessage('天气情况最多50个字符'),
    
    body('temperature')
      .optional()
      .isFloat({ min: -50, max: 60 }).withMessage('温度必须在-50到60摄氏度之间'),
    
    body('humidity')
      .optional()
      .isFloat({ min: 0, max: 100 }).withMessage('湿度必须在0-100%之间'),
    
    body('mortalityCount')
      .optional()
      .isInt({ min: 0, max: 1000 }).withMessage('死亡数量必须在0-1000之间'),
    
    body('notes')
      .optional()
      .isLength({ max: 1000 }).withMessage('备注最多1000个字符')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const {
        flockId,
        recordDate,
        recordType,
        eggCount,
        eggWeight,
        averageWeight,
        sampleCount,
        feedType,
        feedAmount,
        feedCost,
        weather,
        temperature,
        humidity,
        mortalityCount,
        notes,
        attachments
      } = req.body;

      const tenantId = req.tenant.id;
      const userId = req.user.id;

      // 验证鹅群是否属于当前租户
      const flock = await req.db.query(
        'SELECT id FROM flocks WHERE id = ? AND tenant_id = ? AND status = "active"',
        [flockId, tenantId]
      );

      if (!flock || flock.length === 0) {
        return res.status(400).json({
          success: false,
          message: '无效的鹅群ID',
          code: 'INVALID_FLOCK'
        });
      }

      // 插入生产记录
      const insertResult = await req.db.query(`
        INSERT INTO production_records (
          tenant_id, flock_id, record_date, record_type,
          egg_count, egg_weight, average_weight, sample_count,
          feed_type, feed_amount, feed_cost,
          weather, temperature, humidity, mortality_count,
          notes, attachments, created_by, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        tenantId, flockId, recordDate, recordType,
        eggCount || 0, eggWeight || 0, averageWeight || 0, sampleCount || 0,
        feedType, feedAmount || 0, feedCost || 0,
        weather, temperature, humidity, mortalityCount || 0,
        notes, attachments ? JSON.stringify(attachments) : null, userId
      ]);

      // 如果有死亡数量，更新鹅群当前数量
      if (mortalityCount && mortalityCount > 0) {
        await req.db.query(
          'UPDATE flocks SET current_count = GREATEST(current_count - ?, 0), updated_at = NOW() WHERE id = ?',
          [mortalityCount, flockId]
        );
      }

      // 记录操作日志
      logger.info('Production record created', {
        recordId: insertResult.insertId,
        flockId,
        recordType,
        recordDate,
        tenantId,
        createdBy: userId,
        createdByUsername: req.user.username
      });

      // 获取创建的记录信息
      const newRecord = await req.db.query(`
        SELECT 
          pr.*,
          f.name as flock_name, f.flock_code, f.breed,
          u.real_name as creator_name
        FROM production_records pr
        LEFT JOIN flocks f ON pr.flock_id = f.id
        LEFT JOIN tenant_users u ON pr.created_by = u.id
        WHERE pr.id = ?
      `, [insertResult.insertId]);

      const recordData = newRecord[0];

      // 处理附件JSON字段
      if (recordData.attachments) {
        try {
          recordData.attachments = JSON.parse(recordData.attachments);
        } catch (e) {
          recordData.attachments = [];
        }
      }

      res.status(201).json({
        success: true,
        message: '生产记录创建成功',
        data: recordData
      });

    } catch (error) {
      logger.error('Create production record error', {
        error: error.message,
        tenantId: req.tenant?.id,
        userId: req.user?.id,
        requestBody: req.body
      });

      res.status(500).json({
        success: false,
        message: '创建生产记录失败'
      });
    }
  }
);

/**
 * @route PUT /api/tenant/:tenantCode/production/:id
 * @desc 更新生产记录
 * @access Private (Tenant User)
 */
router.put('/:id',
  [
    body('recordDate').optional().isISO8601().withMessage('记录日期格式无效'),
    body('eggCount').optional().isInt({ min: 0, max: 10000 }).withMessage('产蛋数量必须在0-10000之间'),
    body('eggWeight').optional().isFloat({ min: 0, max: 1000 }).withMessage('蛋重必须在0-1000克之间'),
    body('averageWeight').optional().isFloat({ min: 0, max: 50000 }).withMessage('平均体重必须在0-50000克之间'),
    body('sampleCount').optional().isInt({ min: 1, max: 1000 }).withMessage('称重样本数必须在1-1000之间'),
    body('feedType').optional().isLength({ min: 1, max: 100 }).withMessage('饲料类型长度必须在1-100个字符之间'),
    body('feedAmount').optional().isFloat({ min: 0, max: 10000 }).withMessage('饲料用量必须在0-10000公斤之间'),
    body('feedCost').optional().isFloat({ min: 0, max: 100000 }).withMessage('饲料成本必须在0-100000元之间'),
    body('weather').optional().isLength({ max: 50 }).withMessage('天气情况最多50个字符'),
    body('temperature').optional().isFloat({ min: -50, max: 60 }).withMessage('温度必须在-50到60摄氏度之间'),
    body('humidity').optional().isFloat({ min: 0, max: 100 }).withMessage('湿度必须在0-100%之间'),
    body('mortalityCount').optional().isInt({ min: 0, max: 1000 }).withMessage('死亡数量必须在0-1000之间'),
    body('notes').optional().isLength({ max: 1000 }).withMessage('备注最多1000个字符')
  ],
  async (req, res) => {
    try {
      // 验证输入
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入验证失败',
          errors: errors.array()
        });
      }

      const recordId = parseInt(req.params.id);
      const tenantId = req.tenant.id;

      if (isNaN(recordId)) {
        return res.status(400).json({
          success: false,
          message: '无效的记录ID'
        });
      }

      // 检查记录是否存在且属于当前租户
      const existingRecord = await req.db.query(
        'SELECT * FROM production_records WHERE id = ? AND tenant_id = ?',
        [recordId, tenantId]
      );

      if (!existingRecord || existingRecord.length === 0) {
        return res.status(404).json({
          success: false,
          message: '生产记录不存在'
        });
      }

      // 构建更新字段
      const updateFields = [];
      const updateValues = [];

      const allowedFields = {
        recordDate: 'record_date',
        eggCount: 'egg_count',
        eggWeight: 'egg_weight',
        averageWeight: 'average_weight',
        sampleCount: 'sample_count',
        feedType: 'feed_type',
        feedAmount: 'feed_amount',
        feedCost: 'feed_cost',
        weather: 'weather',
        temperature: 'temperature',
        humidity: 'humidity',
        mortalityCount: 'mortality_count',
        notes: 'notes'
      };

      Object.keys(req.body).forEach(key => {
        const dbField = allowedFields[key];
        if (dbField && req.body[key] !== undefined) {
          updateFields.push(`${dbField} = ?`);
          updateValues.push(req.body[key]);
        }
      });

      // 处理附件字段
      if (req.body.attachments !== undefined) {
        updateFields.push('attachments = ?');
        updateValues.push(req.body.attachments ? JSON.stringify(req.body.attachments) : null);
      }

      if (updateFields.length === 0) {
        return res.status(400).json({
          success: false,
          message: '没有可更新的字段'
        });
      }

      // 添加更新时间
      updateFields.push('updated_at = NOW()');
      updateValues.push(recordId);

      // 执行更新
      await req.db.query(
        `UPDATE production_records SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );

      // 记录操作日志
      logger.info('Production record updated', {
        recordId,
        updatedFields: Object.keys(req.body),
        tenantId,
        updatedBy: req.user.id,
        updatedByUsername: req.user.username
      });

      // 获取更新后的记录信息
      const updatedRecord = await req.db.query(`
        SELECT 
          pr.*,
          f.name as flock_name, f.flock_code, f.breed,
          u.real_name as creator_name
        FROM production_records pr
        LEFT JOIN flocks f ON pr.flock_id = f.id
        LEFT JOIN tenant_users u ON pr.created_by = u.id
        WHERE pr.id = ?
      `, [recordId]);

      const recordData = updatedRecord[0];

      // 处理附件JSON字段
      if (recordData.attachments) {
        try {
          recordData.attachments = JSON.parse(recordData.attachments);
        } catch (e) {
          recordData.attachments = [];
        }
      }

      res.json({
        success: true,
        message: '生产记录更新成功',
        data: recordData
      });

    } catch (error) {
      logger.error('Update production record error', {
        error: error.message,
        tenantId: req.tenant?.id,
        userId: req.user?.id,
        recordId: req.params.id,
        requestBody: req.body
      });

      res.status(500).json({
        success: false,
        message: '更新生产记录失败'
      });
    }
  }
);

/**
 * @route DELETE /api/tenant/:tenantCode/production/:id
 * @desc 删除生产记录
 * @access Private (Tenant User with proper permissions)
 */
router.delete('/:id', async (req, res) => {
  try {
    const recordId = parseInt(req.params.id);
    const tenantId = req.tenant.id;

    if (isNaN(recordId)) {
      return res.status(400).json({
        success: false,
        message: '无效的记录ID'
      });
    }

    // 检查记录是否存在且属于当前租户
    const record = await req.db.query(
      'SELECT * FROM production_records WHERE id = ? AND tenant_id = ?',
      [recordId, tenantId]
    );

    if (!record || record.length === 0) {
      return res.status(404).json({
        success: false,
        message: '生产记录不存在'
      });
    }

    // 检查是否有权限删除（只有管理员或创建者可以删除）
    if (req.user.role !== 'admin' && record[0].created_by !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '没有权限删除该记录',
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }

    // 删除记录
    await req.db.query(
      'DELETE FROM production_records WHERE id = ?',
      [recordId]
    );

    // 记录操作日志
    logger.warn('Production record deleted', {
      recordId,
      recordType: record[0].record_type,
      recordDate: record[0].record_date,
      flockId: record[0].flock_id,
      tenantId,
      deletedBy: req.user.id,
      deletedByUsername: req.user.username
    });

    res.json({
      success: true,
      message: '生产记录已删除'
    });

  } catch (error) {
    logger.error('Delete production record error', {
      error: error.message,
      tenantId: req.tenant?.id,
      userId: req.user?.id,
      recordId: req.params.id
    });

    res.status(500).json({
      success: false,
      message: '删除生产记录失败'
    });
  }
});

/**
 * @route GET /api/tenant/:tenantCode/production/stats
 * @desc 获取生产统计信息
 * @access Private (Tenant User)
 */
router.get('/stats', async (req, res) => {
  try {
    const tenantId = req.tenant.id;

    // 基本统计
    const basicStats = await req.db.query(`
      SELECT 
        COUNT(*) as total_records,
        COUNT(CASE WHEN record_type = 'egg' THEN 1 END) as egg_records,
        COUNT(CASE WHEN record_type = 'weight' THEN 1 END) as weight_records,
        COUNT(CASE WHEN record_type = 'feed' THEN 1 END) as feed_records,
        SUM(CASE WHEN record_type = 'egg' THEN egg_count ELSE 0 END) as total_eggs,
        SUM(CASE WHEN record_type = 'feed' THEN feed_amount ELSE 0 END) as total_feed_kg,
        SUM(CASE WHEN record_type = 'feed' THEN feed_cost ELSE 0 END) as total_feed_cost
      FROM production_records
      WHERE tenant_id = ?
    `, [tenantId]);

    // 最近7天产蛋统计
    const recentEggStats = await req.db.query(`
      SELECT 
        record_date,
        SUM(egg_count) as daily_eggs,
        AVG(egg_weight) as avg_egg_weight
      FROM production_records
      WHERE tenant_id = ? AND record_type = 'egg' 
        AND record_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
      GROUP BY record_date
      ORDER BY record_date DESC
    `, [tenantId]);

    // 按鹅群统计生产情况
    const flockStats = await req.db.query(`
      SELECT 
        f.name as flock_name,
        f.flock_code,
        f.breed,
        SUM(CASE WHEN pr.record_type = 'egg' THEN pr.egg_count ELSE 0 END) as total_eggs,
        AVG(CASE WHEN pr.record_type = 'weight' THEN pr.average_weight ELSE NULL END) as avg_weight,
        SUM(CASE WHEN pr.record_type = 'feed' THEN pr.feed_amount ELSE 0 END) as total_feed
      FROM flocks f
      LEFT JOIN production_records pr ON f.id = pr.flock_id
      WHERE f.tenant_id = ? AND f.status = 'active'
      GROUP BY f.id, f.name, f.flock_code, f.breed
      ORDER BY total_eggs DESC
    `, [tenantId]);

    // 月度生产趋势
    const monthlyTrends = await req.db.query(`
      SELECT 
        DATE_FORMAT(record_date, '%Y-%m') as month,
        SUM(CASE WHEN record_type = 'egg' THEN egg_count ELSE 0 END) as monthly_eggs,
        SUM(CASE WHEN record_type = 'feed' THEN feed_amount ELSE 0 END) as monthly_feed,
        SUM(CASE WHEN record_type = 'feed' THEN feed_cost ELSE 0 END) as monthly_feed_cost
      FROM production_records
      WHERE tenant_id = ? AND record_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
      GROUP BY DATE_FORMAT(record_date, '%Y-%m')
      ORDER BY month DESC
      LIMIT 12
    `, [tenantId]);

    res.json({
      success: true,
      message: '获取生产统计成功',
      data: {
        overview: basicStats[0],
        recent_eggs: recentEggStats,
        by_flock: flockStats,
        monthly_trends: monthlyTrends,
        generated_at: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Get production stats error', {
      error: error.message,
      tenantId: req.tenant?.id,
      userId: req.user?.id
    });

    res.status(500).json({
      success: false,
      message: '获取生产统计失败'
    });
  }
});

module.exports = router;