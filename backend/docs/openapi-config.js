/**
 * OpenAPI文档配置
 * 统一的API文档生成配置
 */

const swaggerJSDoc = require('swagger-jsdoc');
const packageJson = require('../package.json');

// 基础配置信息
const basicInfo = {
  openapi: '3.0.3',
  info: {
    title: '智慧养鹅SaaS平台 API',
    version: packageJson.version || '1.0.0',
    description: `
智慧养鹅SaaS平台的RESTful API文档。

## 功能特性

- 🐦 **鹅群管理**: 全面的鹅群生命周期管理
- 💊 **健康监控**: AI驱动的健康诊断和记录
- 📊 **生产统计**: 详细的生产数据分析
- 🛒 **商城系统**: 完整的电商功能
- 👥 **用户管理**: 多租户用户体系
- 📱 **小程序支持**: 针对微信小程序优化

## 认证方式

API使用Bearer Token进行认证：

\`\`\`
Authorization: Bearer <your-token>
\`\`\`

## 响应格式

所有API响应都遵循统一的格式：

**成功响应:**
\`\`\`json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-20T10:30:00Z"
}
\`\`\`

**错误响应:**
\`\`\`json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": []
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
\`\`\`
    `,
    contact: {
      name: '智慧养鹅开发团队',
      email: '<EMAIL>'
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT'
    }
  },
  servers: [
    {
      url: 'http://localhost:3000',
      description: '开发环境'
    },
    {
      url: 'https://api-staging.zhihuiyange.com',
      description: '测试环境'
    },
    {
      url: 'https://api.zhihuiyange.com',
      description: '生产环境'
    }
  ],
  components: {
    securitySchemes: {
      BearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'JWT token认证'
      },
      TenantAuth: {
        type: 'apiKey',
        in: 'header',
        name: 'X-Tenant-Code',
        description: '租户标识'
      }
    },
    schemas: {
      // 通用响应格式
      SuccessResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: true
          },
          data: {
            type: 'object',
            description: '响应数据'
          },
          message: {
            type: 'string',
            example: '操作成功'
          },
          timestamp: {
            type: 'string',
            format: 'date-time',
            example: '2024-01-20T10:30:00Z'
          }
        },
        required: ['success', 'timestamp']
      },
      ErrorResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: false
          },
          error: {
            type: 'object',
            properties: {
              code: {
                type: 'string',
                example: 'VALIDATION_ERROR'
              },
              message: {
                type: 'string',
                example: '参数验证失败'
              },
              details: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    field: {
                      type: 'string'
                    },
                    message: {
                      type: 'string'
                    }
                  }
                }
              }
            },
            required: ['message']
          },
          timestamp: {
            type: 'string',
            format: 'date-time'
          }
        },
        required: ['success', 'error', 'timestamp']
      },

      // 分页响应
      PaginationInfo: {
        type: 'object',
        properties: {
          page: {
            type: 'integer',
            minimum: 1,
            description: '当前页码'
          },
          limit: {
            type: 'integer',
            minimum: 1,
            maximum: 100,
            description: '每页数量'
          },
          total: {
            type: 'integer',
            minimum: 0,
            description: '总记录数'
          },
          totalPages: {
            type: 'integer',
            minimum: 0,
            description: '总页数'
          },
          hasNext: {
            type: 'boolean',
            description: '是否有下一页'
          },
          hasPrev: {
            type: 'boolean',
            description: '是否有上一页'
          }
        },
        required: ['page', 'limit', 'total', 'totalPages']
      },

      // 用户相关
      User: {
        type: 'object',
        properties: {
          id: {
            type: 'integer',
            description: '用户ID'
          },
          username: {
            type: 'string',
            description: '用户名'
          },
          email: {
            type: 'string',
            format: 'email',
            description: '邮箱地址'
          },
          name: {
            type: 'string',
            description: '真实姓名'
          },
          role: {
            type: 'string',
            enum: ['admin', 'manager', 'user'],
            description: '用户角色'
          },
          status: {
            type: 'string',
            enum: ['active', 'inactive', 'suspended'],
            description: '用户状态'
          },
          phone: {
            type: 'string',
            description: '手机号码'
          },
          avatar: {
            type: 'string',
            description: '头像URL'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: '创建时间'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: '更新时间'
          }
        },
        required: ['id', 'username', 'email', 'role', 'status']
      },

      // 鹅群相关
      Flock: {
        type: 'object',
        properties: {
          id: {
            type: 'integer',
            description: '鹅群ID'
          },
          name: {
            type: 'string',
            description: '鹅群名称'
          },
          breed: {
            type: 'string',
            enum: [
              'white_goose',
              'grey_goose',
              'toulouse',
              'embden',
              'chinese',
              'african',
              'sebastopol',
              'other'
            ],
            description: '鹅群品种'
          },
          totalCount: {
            type: 'integer',
            minimum: 0,
            description: '总数量'
          },
          currentCount: {
            type: 'integer',
            minimum: 0,
            description: '当前数量'
          },
          maleCount: {
            type: 'integer',
            minimum: 0,
            description: '雄性数量'
          },
          femaleCount: {
            type: 'integer',
            minimum: 0,
            description: '雌性数量'
          },
          ageGroup: {
            type: 'string',
            enum: ['gosling', 'young', 'adult', 'breeder'],
            description: '年龄组'
          },
          status: {
            type: 'string',
            enum: ['active', 'inactive', 'sold', 'deceased'],
            description: '鹅群状态'
          },
          establishedDate: {
            type: 'string',
            format: 'date-time',
            description: '建群日期'
          },
          location: {
            type: 'string',
            description: '位置'
          },
          description: {
            type: 'string',
            description: '描述'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: '创建时间'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: '更新时间'
          }
        },
        required: [
          'id',
          'name',
          'breed',
          'totalCount',
          'currentCount',
          'status'
        ]
      },

      // 健康记录
      HealthRecord: {
        type: 'object',
        properties: {
          id: {
            type: 'integer',
            description: '记录ID'
          },
          flockId: {
            type: 'integer',
            description: '鹅群ID'
          },
          checkType: {
            type: 'string',
            enum: ['routine', 'emergency', 'vaccination', 'treatment'],
            description: '检查类型'
          },
          checkDate: {
            type: 'string',
            format: 'date-time',
            description: '检查日期'
          },
          symptoms: {
            type: 'string',
            description: '症状描述'
          },
          temperature: {
            type: 'number',
            format: 'float',
            description: '体温'
          },
          weight: {
            type: 'number',
            format: 'float',
            description: '体重'
          },
          result: {
            type: 'string',
            enum: ['healthy', 'sick', 'recovering', 'critical'],
            description: '检查结果'
          },
          diagnosis: {
            type: 'string',
            description: '诊断结果'
          },
          treatment: {
            type: 'string',
            description: '治疗方案'
          },
          medicine: {
            type: 'string',
            description: '用药信息'
          },
          dosage: {
            type: 'string',
            description: '用药剂量'
          },
          notes: {
            type: 'string',
            description: '备注'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: '创建时间'
          }
        },
        required: ['id', 'flockId', 'checkType', 'checkDate', 'result']
      },

      // 生产记录
      ProductionRecord: {
        type: 'object',
        properties: {
          id: {
            type: 'integer',
            description: '记录ID'
          },
          flockId: {
            type: 'integer',
            description: '鹅群ID'
          },
          recordDate: {
            type: 'string',
            format: 'date',
            description: '记录日期'
          },
          eggCount: {
            type: 'integer',
            minimum: 0,
            description: '产蛋数量'
          },
          feedConsumption: {
            type: 'number',
            format: 'float',
            minimum: 0,
            description: '饲料消耗(kg)'
          },
          waterConsumption: {
            type: 'number',
            format: 'float',
            minimum: 0,
            description: '用水量(L)'
          },
          mortality: {
            type: 'integer',
            minimum: 0,
            description: '死亡数量'
          },
          notes: {
            type: 'string',
            description: '备注'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: '创建时间'
          }
        },
        required: ['id', 'flockId', 'recordDate']
      }
    },

    parameters: {
      // 通用查询参数
      PageParam: {
        name: 'page',
        in: 'query',
        description: '页码',
        required: false,
        schema: {
          type: 'integer',
          minimum: 1,
          default: 1
        }
      },
      LimitParam: {
        name: 'limit',
        in: 'query',
        description: '每页数量',
        required: false,
        schema: {
          type: 'integer',
          minimum: 1,
          maximum: 100,
          default: 10
        }
      },
      SearchParam: {
        name: 'search',
        in: 'query',
        description: '搜索关键词',
        required: false,
        schema: {
          type: 'string',
          maxLength: 100
        }
      },
      SortByParam: {
        name: 'sortBy',
        in: 'query',
        description: '排序字段',
        required: false,
        schema: {
          type: 'string'
        }
      },
      SortOrderParam: {
        name: 'sortOrder',
        in: 'query',
        description: '排序方向',
        required: false,
        schema: {
          type: 'string',
          enum: ['ASC', 'DESC'],
          default: 'DESC'
        }
      },

      // 路径参数
      IdPathParam: {
        name: 'id',
        in: 'path',
        description: '资源ID',
        required: true,
        schema: {
          type: 'integer',
          minimum: 1
        }
      }
    },

    responses: {
      // 通用响应
      Success: {
        description: '操作成功',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/SuccessResponse'
            }
          }
        }
      },
      BadRequest: {
        description: '请求参数错误',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse'
            },
            example: {
              success: false,
              error: {
                code: 'VALIDATION_ERROR',
                message: '参数验证失败',
                details: [
                  {
                    field: 'email',
                    message: '邮箱格式不正确'
                  }
                ]
              },
              timestamp: '2024-01-20T10:30:00Z'
            }
          }
        }
      },
      Unauthorized: {
        description: '未授权访问',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse'
            },
            example: {
              success: false,
              error: {
                code: 'UNAUTHORIZED',
                message: '未授权访问，请先登录'
              },
              timestamp: '2024-01-20T10:30:00Z'
            }
          }
        }
      },
      Forbidden: {
        description: '权限不足',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse'
            },
            example: {
              success: false,
              error: {
                code: 'FORBIDDEN',
                message: '权限不足'
              },
              timestamp: '2024-01-20T10:30:00Z'
            }
          }
        }
      },
      NotFound: {
        description: '资源不存在',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse'
            },
            example: {
              success: false,
              error: {
                code: 'NOT_FOUND',
                message: '请求的资源不存在'
              },
              timestamp: '2024-01-20T10:30:00Z'
            }
          }
        }
      },
      ServerError: {
        description: '服务器内部错误',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse'
            },
            example: {
              success: false,
              error: {
                code: 'INTERNAL_ERROR',
                message: '服务器内部错误'
              },
              timestamp: '2024-01-20T10:30:00Z'
            }
          }
        }
      }
    }
  },

  security: [
    {
      BearerAuth: []
    },
    {
      TenantAuth: []
    }
  ],

  tags: [
    {
      name: 'auth',
      description: '认证相关接口'
    },
    {
      name: 'users',
      description: '用户管理接口'
    },
    {
      name: 'flocks',
      description: '鹅群管理接口'
    },
    {
      name: 'health',
      description: '健康管理接口'
    },
    {
      name: 'production',
      description: '生产管理接口'
    },
    {
      name: 'inventory',
      description: '库存管理接口'
    },
    {
      name: 'announcements',
      description: '公告管理接口'
    },
    {
      name: 'knowledge',
      description: '知识库接口'
    },
    {
      name: 'orders',
      description: '订单管理接口'
    },
    {
      name: 'system',
      description: '系统管理接口'
    }
  ]
};

// Swagger配置
const swaggerOptions = {
  definition: basicInfo,
  apis: [
    './routes/*.js',
    './controllers/*.js',
    './models/*.js',
    './admin/routes/*.js',
    './admin/controllers/*.js',
    './docs/api-examples.js'
  ]
};

// 生成swagger规范
const swaggerSpec = swaggerJSDoc(swaggerOptions);

module.exports = {
  basicInfo,
  swaggerOptions,
  swaggerSpec
};
