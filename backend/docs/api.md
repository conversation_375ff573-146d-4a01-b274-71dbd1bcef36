# 智慧养鹅项目API文档

## 目录

1. [认证相关接口](#认证相关接口)
2. [用户管理接口](#用户管理接口)
3. [健康管理接口](#健康管理接口)
   - [健康记录管理](#健康记录管理)
   - [知识库管理](#知识库管理)
   - [健康报告管理](#健康报告管理)
   - [AI诊断管理](#ai诊断管理)
4. [生产管理接口](#生产管理接口)
5. [商城管理接口](#商城管理接口)
6. [任务管理接口](#任务管理接口)
7. [公告管理接口](#公告管理接口)
8. [库存管理接口](#库存管理接口)
9. [价格管理接口](#价格管理接口)
10. [AI配置管理接口](#ai配置管理接口)
11. [API设置管理接口](#api设置管理接口)

## 认证相关接口

### 用户注册

- **URL**: `/api/v1/auth/register`
- **方法**: `POST`
- **描述**: 用户注册
- **请求参数**:
  ```json
  {
    "username": "string",
    "password": "string",
    "email": "string"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "注册成功",
    "data": {
      "token": "string"
    }
  }
  ```

### 用户登录

- **URL**: `/api/v1/auth/login`
- **方法**: `POST`
- **描述**: 用户登录
- **请求参数**:
  ```json
  {
    "username": "string",
    "password": "string"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "登录成功",
    "data": {
      "token": "string",
      "user": {
        "id": "integer",
        "username": "string",
        "email": "string",
        "role": "string"
      }
    }
  }
  ```

## 用户管理接口

### 获取用户列表

- **URL**: `/api/v1/users`
- **方法**: `GET`
- **描述**: 获取用户列表（管理员权限）
- **请求参数**:
  - `page` (integer, optional): 页码，默认为1
  - `limit` (integer, optional): 每页数量，默认为10
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "users": [
        {
          "id": "integer",
          "username": "string",
          "email": "string",
          "role": "string"
        }
      ],
      "pagination": {
        "page": "integer",
        "limit": "integer",
        "total": "integer",
        "pages": "integer"
      }
    }
  }
  ```

### 创建用户

- **URL**: `/api/v1/users`
- **方法**: `POST`
- **描述**: 创建用户（管理员权限）
- **请求参数**:
  ```json
  {
    "username": "string",
    "password": "string",
    "email": "string",
    "role": "string"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "用户创建成功",
    "data": {
      "id": "integer"
    }
  }
  ```

## 健康管理接口

### 健康记录管理

#### 获取健康记录列表

- **URL**: `/api/v1/health/records`
- **方法**: `GET`
- **描述**: 获取健康记录列表
- **请求参数**:
  - `page` (integer, optional): 页码，默认为1
  - `limit` (integer, optional): 每页数量，默认为10
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "records": [
        {
          "id": "integer",
          "userId": "integer",
          "gooseId": "string",
          "symptoms": "string",
          "diagnosis": "string",
          "treatment": "string",
          "status": "string",
          "createdAt": "datetime",
          "updatedAt": "datetime"
        }
      ],
      "pagination": {
        "page": "integer",
        "limit": "integer",
        "total": "integer",
        "pages": "integer"
      }
    }
  }
  ```

#### 创建健康记录

- **URL**: `/api/v1/health/records`
- **方法**: `POST`
- **描述**: 创建健康记录
- **请求参数**:
  ```json
  {
    "gooseId": "string",
    "symptoms": "string",
    "diagnosis": "string",
    "treatment": "string",
    "status": "string"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "健康记录创建成功",
    "data": {
      "id": "integer",
      "userId": "integer",
      "gooseId": "string",
      "symptoms": "string",
      "diagnosis": "string",
      "treatment": "string",
      "status": "string",
      "createdAt": "datetime",
      "updatedAt": "datetime"
    }
  }
  ```

### 知识库管理

#### 获取知识库文章列表

- **URL**: `/api/v1/health/knowledge`
- **方法**: `GET`
- **描述**: 获取知识库文章列表
- **请求参数**:
  - `page` (integer, optional): 页码，默认为1
  - `limit` (integer, optional): 每页数量，默认为10
  - `search` (string, optional): 搜索关键词
  - `category` (string, optional): 分类筛选
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "articles": [
        {
          "id": "integer",
          "title": "string",
          "content": "string",
          "category": "string",
          "tags": "array",
          "keywords": "string",
          "difficulty": "string",
          "readTime": "integer",
          "viewCount": "integer",
          "likeCount": "integer",
          "images": "array",
          "videos": "array",
          "attachments": "array",
          "relatedArticles": "array",
          "status": "string",
          "isRecommended": "boolean",
          "publishTime": "datetime",
          "lastReviewTime": "datetime",
          "createdBy": "integer",
          "updatedBy": "integer",
          "reviewedBy": "integer",
          "createdAt": "datetime",
          "updatedAt": "datetime"
        }
      ],
      "pagination": {
        "page": "integer",
        "limit": "integer",
        "total": "integer",
        "pages": "integer"
      }
    }
  }
  ```

#### 创建知识文章

- **URL**: `/api/v1/health/knowledge`
- **方法**: `POST`
- **描述**: 创建知识文章（管理员权限）
- **请求参数**:
  ```json
  {
    "title": "string",
    "content": "string",
    "category": "string",
    "tags": "array",
    "keywords": "string",
    "difficulty": "string",
    "readTime": "integer",
    "images": "array",
    "videos": "array",
    "attachments": "array",
    "relatedArticles": "array",
    "isRecommended": "boolean"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "知识文章创建成功",
    "data": {
      // 文章对象
    }
  }
  ```

#### 获取知识文章详情

- **URL**: `/api/v1/health/knowledge/:id`
- **方法**: `GET`
- **描述**: 获取知识文章详情
- **请求参数**:
  - `id` (integer, required): 文章ID
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      // 文章对象
    }
  }
  ```

#### 更新知识文章

- **URL**: `/api/v1/health/knowledge/:id`
- **方法**: `PUT`
- **描述**: 更新知识文章（管理员权限）
- **请求参数**:
  - `id` (integer, required): 文章ID
  - 请求体包含要更新的字段
- **响应**:
  ```json
  {
    "success": true,
    "message": "知识文章更新成功",
    "data": {
      // 更新后的文章对象
    }
  }
  ```

#### 删除知识文章

- **URL**: `/api/v1/health/knowledge/:id`
- **方法**: `DELETE`
- **描述**: 删除知识文章（管理员权限）
- **请求参数**:
  - `id` (integer, required): 文章ID
- **响应**:
  ```json
  {
    "success": true,
    "message": "知识文章删除成功"
  }
  ```

#### 搜索知识库

- **URL**: `/api/v1/health/knowledge/search`
- **方法**: `GET`
- **描述**: 搜索知识库
- **请求参数**:
  - `q` (string, required): 搜索关键词
  - `category` (string, optional): 分类筛选
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "articles": [
        // 匹配的文章列表
      ]
    }
  }
  ```

### AI诊断管理

#### 获取AI诊断记录列表

- **URL**: `/api/v1/health/ai-diagnosis`
- **方法**: `GET`
- **描述**: 获取AI诊断记录列表
- **请求参数**:
  - `page` (integer, optional): 页码，默认为1
  - `limit` (integer, optional): 每页数量，默认为10
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "records": [
        {
          "id": "integer",
          "gooseId": "string",
          "diagnosis": "string",
          "result": "string",
          "createdAt": "datetime"
        }
      ],
      "pagination": {
        "page": "integer",
        "limit": "integer",
        "total": "integer",
        "pages": "integer"
      }
    }
  }
  ```

#### 创建AI诊断记录

- **URL**: `/api/v1/health/ai-diagnosis`
- **方法**: `POST`
- **描述**: 创建AI诊断记录
- **请求参数**:
  ```json
  {
    "gooseId": "string",
    "symptoms": "string",
    "diagnosis": "string",
    "treatment": "string"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "诊断记录添加成功",
    "data": {
      // 健康记录对象
    }
  }
  ```

#### AI健康诊断

- **URL**: `/api/v1/health/ai-diagnosis/analyze`
- **方法**: `POST`
- **描述**: AI健康诊断
- **请求参数**:
  ```json
  {
    "symptoms": "string",
    "images": "array" // 可选
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "AI诊断完成",
    "data": {
      "diagnosis": "string",
      "confidence": "number",
      "suggestions": "array",
      "severity": "string"
    }
  }
  ```

#### 获取AI诊断历史

- **URL**: `/api/v1/health/ai-diagnosis/history`
- **方法**: `GET`
- **描述**: 获取AI诊断历史
- **请求参数**:
  - `page` (integer, optional): 页码，默认为1
  - `limit` (integer, optional): 每页数量，默认为10
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "history": [
        {
          "id": "integer",
          "userId": "integer",
          "provider": "string",
          "model": "string",
          "scenario": "string",
          "feature": "string",
          "requestTokens": "integer",
          "responseTokens": "integer",
          "totalTokens": "integer",
          "cost": "number",
          "responseTime": "integer",
          "success": "boolean",
          "errorMessage": "string",
          "createdAt": "datetime"
        }
      ],
      "pagination": {
        "page": "integer",
        "limit": "integer",
        "total": "integer",
        "pages": "integer"
      }
    }
  }
  ```

#### 获取AI诊断详情

- **URL**: `/api/v1/health/ai-diagnosis/:id`
- **方法**: `GET`
- **描述**: 获取AI诊断详情
- **请求参数**:
  - `id` (integer, required): 诊断记录ID
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      // AI使用统计记录对象
    }
  }
  ```

## 生产管理接口

(此处省略生产管理接口的详细说明，已在现有文档中定义)

## 商城管理接口

### 获取商品列表

- **URL**: `/api/v1/shop/products`
- **方法**: `GET`
- **描述**: 获取商品列表
- **请求参数**:
  - `page` (integer, optional): 页码，默认为1
  - `limit` (integer, optional): 每页数量，默认为10
  - `category` (string, optional): 分类筛选
  - `search` (string, optional): 搜索关键词
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "products": [
        {
          "id": "integer",
          "name": "string",
          "description": "string",
          "price": "number",
          "category": "string",
          "image": "string",
          "stock": "integer",
          "status": "string",
          "tags": "array",
          "specifications": "object",
          "salesCount": "integer",
          "createdBy": "integer",
          "updatedBy": "integer",
          "createdAt": "datetime",
          "updatedAt": "datetime"
        }
      ],
      "pagination": {
        "page": "integer",
        "limit": "integer",
        "total": "integer",
        "pages": "integer"
      }
    }
  }
  ```

### 添加商品

- **URL**: `/api/v1/shop/products`
- **方法**: `POST`
- **描述**: 添加商品（管理员权限）
- **请求参数**:
  ```json
  {
    "name": "string",
    "description": "string",
    "price": "number",
    "category": "string",
    "image": "string",
    "stock": "integer",
    "tags": "array",
    "specifications": "object"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "商品添加成功",
    "data": {
      // 商品对象
    }
  }
  ```

### 获取商品详情

- **URL**: `/api/v1/shop/products/:id`
- **方法**: `GET`
- **描述**: 获取商品详情
- **请求参数**:
  - `id` (integer, required): 商品ID
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      // 商品对象
    }
  }
  ```

### 更新商品

- **URL**: `/api/v1/shop/products/:id`
- **方法**: `PUT`
- **描述**: 更新商品（管理员权限）
- **请求参数**:
  - `id` (integer, required): 商品ID
  - 请求体包含要更新的字段
- **响应**:
  ```json
  {
    "success": true,
    "message": "商品更新成功",
    "data": {
      // 更新后的商品对象
    }
  }
  ```

### 删除商品

- **URL**: `/api/v1/shop/products/:id`
- **方法**: `DELETE`
- **描述**: 删除商品（管理员权限）
- **请求参数**:
  - `id` (integer, required): 商品ID
- **响应**:
  ```json
  {
    "success": true,
    "message": "商品删除成功"
  }
  ```

### 获取商品分类

- **URL**: `/api/v1/shop/categories`
- **方法**: `GET`
- **描述**: 获取商品分类
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "categories": [
        {
          "name": "string",
          "count": "integer"
        }
      ]
    }
  }
  ```

## 任务管理接口

(此处省略任务管理接口的详细说明，已在现有文档中定义)

## 公告管理接口

(此处省略公告管理接口的详细说明，已在现有文档中定义)

## 库存管理接口

(此处省略库存管理接口的详细说明，已在现有文档中定义)

## 价格管理接口

(此处省略价格管理接口的详细说明，已在现有文档中定义)

## AI配置管理接口

(此处省略AI配置管理接口的详细说明，已在现有文档中定义)

## API设置管理接口

### 获取API设置列表

- **URL**: `/api/v1/api-settings`
- **方法**: `GET`
- **描述**: 获取API设置列表
- **响应**:
  ```json
  {
    "success": true,
    "data": [
      {
        "id": "integer",
        "provider": "string",
        "name": "string",
        "baseUrl": "string",
        "models": "array",
        "maxTokens": "integer",
        "temperature": "number",
        "enabled": "boolean",
        "isDefault": "boolean",
        "priority": "integer",
        "config": "object",
        "createdBy": "integer",
        "updatedBy": "integer",
        "createdAt": "datetime",
        "updatedAt": "datetime"
      }
    ]
  }
  ```

### 获取默认API配置

- **URL**: `/api/v1/api-settings/default`
- **方法**: `GET`
- **描述**: 获取默认API配置
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      // 默认API配置对象
    }
  }
  ```

### 更新API设置

- **URL**: `/api/v1/api-settings/:id`
- **方法**: `PUT`
- **描述**: 更新API设置（管理员权限）
- **请求参数**:
  - `id` (integer, required): 配置ID
  - 请求体包含要更新的字段
- **响应**:
  ```json
  {
    "success": true,
    "message": "API设置更新成功"
  }
  ```

### 测试API连接

- **URL**: `/api/v1/api-settings/:id/test`
- **方法**: `POST`
- **描述**: 测试API连接（管理员权限）
- **请求参数**:
  - `id` (integer, required): 配置ID
- **响应**:
  ```json
  {
    "success": true,
    "message": "API连接测试成功",
    "data": {
      "provider": "string",
      "status": "string",
      "responseTime": "integer"
    }
  }
  ```
