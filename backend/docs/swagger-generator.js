/**
 * Swagger文档生成工具
 * 自动扫描代码注释生成API文档
 */

const fs = require("fs");
const path = require("path");
const swaggerJSDoc = require("swagger-jsdoc");
const yaml = require("js-yaml");
const { swaggerOptions } = require("./openapi-config");

class SwaggerGenerator {
  constructor(options = {}) {
    this.options = {
      outputDir: path.join(__dirname, "../generated-docs"),
      generateJson: true,
      generateYaml: true,
      generateHtml: false,
      ...options,
    };

    // 确保输出目录存在
    if (!fs.existsSync(this.options.outputDir)) {
      fs.mkdirSync(this.options.outputDir, { recursive: true });
    }
  }

  /**
   * 生成API文档
   */
  async generate() {
    try {
      // 生成swagger规范
      const spec = swaggerJSDoc(swaggerOptions);

      // 验证规范
      const validation = this.validateSpec(spec);
      if (!validation.valid) {
        }

      // 生成不同格式的文档
      const results = {};

      if (this.options.generateJson) {
        results.json = await this.generateJson(spec);
      }

      if (this.options.generateYaml) {
        results.yaml = await this.generateYaml(spec);
      }

      if (this.options.generateHtml) {
        results.html = await this.generateHtml(spec);
      }

      // 生成统计报告
      results.stats = this.generateStats(spec);

      return results;
    } catch (error) {
      console.error("生成API文档失败:", error);
      throw error;
    }
  }

  /**
   * 生成JSON格式文档
   */
  async generateJson(spec) {
    const outputPath = path.join(this.options.outputDir, "openapi.json");
    const content = JSON.stringify(spec, null, 2);

    fs.writeFileSync(outputPath, content);

    return {
      format: "json",
      path: outputPath,
      size: content.length,
    };
  }

  /**
   * 生成YAML格式文档
   */
  async generateYaml(spec) {
    const outputPath = path.join(this.options.outputDir, "openapi.yaml");
    const content = yaml.dump(spec, {
      noRefs: true,
      indent: 2,
    });

    fs.writeFileSync(outputPath, content);

    return {
      format: "yaml",
      path: outputPath,
      size: content.length,
    };
  }

  /**
   * 生成HTML格式文档
   */
  async generateHtml(spec) {
    const outputPath = path.join(this.options.outputDir, "api-docs.html");

    const htmlTemplate = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${spec.info.title} - API文档</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui.css" />
    <style>
        html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
        *, *:before, *:after { box-sizing: inherit; }
        body { margin:0; background: #fafafa; }
        .swagger-ui .topbar { display: none; }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: './openapi.json',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                defaultModelsExpandDepth: 2,
                defaultModelExpandDepth: 2,
                docExpansion: "none",
                filter: true,
                showExtensions: true,
                showCommonExtensions: true
            });
        };
    </script>
</body>
</html>
    `;

    fs.writeFileSync(outputPath, htmlTemplate.trim());

    return {
      format: "html",
      path: outputPath,
      size: htmlTemplate.length,
    };
  }

  /**
   * 验证API规范
   */
  validateSpec(spec) {
    const errors = [];
    const warnings = [];

    // 基础验证
    if (!spec.info) {
      errors.push("缺少info字段");
    }

    if (!spec.paths || Object.keys(spec.paths).length === 0) {
      warnings.push("没有定义API路径");
    }

    // 检查每个路径
    if (spec.paths) {
      Object.entries(spec.paths).forEach(([path, pathItem]) => {
        Object.entries(pathItem).forEach(([method, operation]) => {
          if (
            ![
              "get",
              "post",
              "put",
              "patch",
              "delete",
              "options",
              "head",
            ].includes(method)
          ) {
            return;
          }

          if (!operation.summary) {
            warnings.push(`${method.toUpperCase()} ${path} 缺少summary`);
          }

          if (!operation.tags || operation.tags.length === 0) {
            warnings.push(`${method.toUpperCase()} ${path} 缺少tags`);
          }

          if (!operation.responses) {
            errors.push(`${method.toUpperCase()} ${path} 缺少responses定义`);
          }
        });
      });
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 生成统计信息
   */
  generateStats(spec) {
    const stats = {
      info: {
        title: spec.info?.title || "Unknown",
        version: spec.info?.version || "1.0.0",
        description: spec.info?.description || "",
      },
      endpoints: {
        total: 0,
        byMethod: {},
        byTag: {},
      },
      schemas: {
        total: 0,
        list: [],
      },
      security: {
        schemes: 0,
        list: [],
      },
      servers: spec.servers?.length || 0,
      tags: spec.tags?.length || 0,
    };

    // 统计端点
    if (spec.paths) {
      Object.entries(spec.paths).forEach(([path, pathItem]) => {
        Object.entries(pathItem).forEach(([method, operation]) => {
          if (
            [
              "get",
              "post",
              "put",
              "patch",
              "delete",
              "options",
              "head",
            ].includes(method)
          ) {
            stats.endpoints.total++;

            // 按方法统计
            const methodUpper = method.toUpperCase();
            stats.endpoints.byMethod[methodUpper] =
              (stats.endpoints.byMethod[methodUpper] || 0) + 1;

            // 按标签统计
            if (operation.tags) {
              operation.tags.forEach((tag) => {
                stats.endpoints.byTag[tag] =
                  (stats.endpoints.byTag[tag] || 0) + 1;
              });
            }
          }
        });
      });
    }

    // 统计数据模型
    if (spec.components?.schemas) {
      stats.schemas.total = Object.keys(spec.components.schemas).length;
      stats.schemas.list = Object.keys(spec.components.schemas);
    }

    // 统计安全方案
    if (spec.components?.securitySchemes) {
      stats.security.schemes = Object.keys(
        spec.components.securitySchemes,
      ).length;
      stats.security.list = Object.keys(spec.components.securitySchemes);
    }

    return stats;
  }

  /**
   * 生成覆盖率报告
   */
  generateCoverageReport(spec) {
    const coverage = {
      endpoints: {
        documented: 0,
        undocumented: 0,
        total: 0,
      },
      documentation: {
        withSummary: 0,
        withDescription: 0,
        withExamples: 0,
        withTags: 0,
      },
      quality: {
        score: 0,
        issues: [],
      },
    };

    if (spec.paths) {
      Object.entries(spec.paths).forEach(([path, pathItem]) => {
        Object.entries(pathItem).forEach(([method, operation]) => {
          if (!["get", "post", "put", "patch", "delete"].includes(method)) {
            return;
          }

          coverage.endpoints.total++;

          if (operation.summary || operation.description) {
            coverage.endpoints.documented++;
          } else {
            coverage.endpoints.undocumented++;
            coverage.quality.issues.push(
              `${method.toUpperCase()} ${path} 缺少文档`,
            );
          }

          if (operation.summary) coverage.documentation.withSummary++;
          if (operation.description) coverage.documentation.withDescription++;
          if (operation.tags && operation.tags.length > 0)
            coverage.documentation.withTags++;

          // 检查示例
          if (
            operation.requestBody?.content?.["application/json"]?.example ||
            operation.responses?.["200"]?.content?.["application/json"]?.example
          ) {
            coverage.documentation.withExamples++;
          }
        });
      });
    }

    // 计算质量分数
    const totalEndpoints = coverage.endpoints.total || 1;
    coverage.quality.score = Math.round(
      (coverage.endpoints.documented / totalEndpoints) * 100,
    );

    return coverage;
  }
}

// 命令行工具
if (require.main === module) {
  const generator = new SwaggerGenerator({
    outputDir: process.argv[2] || path.join(__dirname, "../generated-docs"),
    generateJson: true,
    generateYaml: true,
    generateHtml: true,
  });

  generator
    .generate()
    .then((results) => {
      Object.values(results).forEach((result) => {
        if (result.path) {
          console.log(`Generated: ${result.path}`);
        }
      });
    })
    .catch((error) => {
      console.error("❌ 文档生成失败:", error.message);
      process.exit(1);
    });
}

module.exports = SwaggerGenerator;
