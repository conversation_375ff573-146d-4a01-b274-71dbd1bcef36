/**
 * API示例和文档注释
 * 使用JSDoc格式为主要API端点添加文档
 */

/**
 * @swagger
 * /api/v1/auth/login:
 *   post:
 *     tags: [auth]
 *     summary: 用户登录
 *     description: 用户登录获取访问令牌
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - password
 *             properties:
 *               username:
 *                 type: string
 *                 description: 用户名或邮箱
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 format: password
 *                 description: 用户密码
 *                 example: "password123"
 *               tenantCode:
 *                 type: string
 *                 description: 租户代码（可选）
 *                 example: "tenant001"
 *     responses:
 *       200:
 *         description: 登录成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         token:
 *                           type: string
 *                           description: 访问令牌
 *                         refreshToken:
 *                           type: string
 *                           description: 刷新令牌
 *                         user:
 *                           $ref: '#/components/schemas/User'
 *                         expires:
 *                           type: string
 *                           format: date-time
 *                           description: 令牌过期时间
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */

/**
 * @swagger
 * /api/v1/flocks:
 *   get:
 *     tags: [flocks]
 *     summary: 获取鹅群列表
 *     description: 获取当前用户的鹅群列表，支持分页、搜索和排序
 *     security:
 *       - BearerAuth: []
 *       - TenantAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/LimitParam'
 *       - $ref: '#/components/parameters/SearchParam'
 *       - name: status
 *         in: query
 *         description: 鹅群状态筛选
 *         schema:
 *           type: string
 *           enum: [active, inactive, sold, deceased]
 *       - name: breed
 *         in: query
 *         description: 品种筛选
 *         schema:
 *           type: string
 *           enum: [white_goose, grey_goose, toulouse, embden, chinese, african, sebastopol, other]
 *       - name: ageGroup
 *         in: query
 *         description: 年龄组筛选
 *         schema:
 *           type: string
 *           enum: [gosling, young, adult, breeder]
 *       - $ref: '#/components/parameters/SortByParam'
 *       - $ref: '#/components/parameters/SortOrderParam'
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         items:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/Flock'
 *                         pagination:
 *                           $ref: '#/components/schemas/PaginationInfo'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *   post:
 *     tags: [flocks]
 *     summary: 创建鹅群
 *     description: 创建新的鹅群记录
 *     security:
 *       - BearerAuth: []
 *       - TenantAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - breed
 *               - totalCount
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 100
 *                 description: 鹅群名称
 *                 example: "一号鹅群"
 *               breed:
 *                 type: string
 *                 enum: [white_goose, grey_goose, toulouse, embden, chinese, african, sebastopol, other]
 *                 description: 鹅群品种
 *                 example: "white_goose"
 *               totalCount:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 10000
 *                 description: 总数量
 *                 example: 100
 *               maleCount:
 *                 type: integer
 *                 minimum: 0
 *                 description: 雄性数量
 *                 example: 30
 *               femaleCount:
 *                 type: integer
 *                 minimum: 0
 *                 description: 雌性数量
 *                 example: 70
 *               ageGroup:
 *                 type: string
 *                 enum: [gosling, young, adult, breeder]
 *                 description: 年龄组
 *                 example: "young"
 *               establishedDate:
 *                 type: string
 *                 format: date-time
 *                 description: 建群日期
 *                 example: "2024-01-01T00:00:00Z"
 *               location:
 *                 type: string
 *                 maxLength: 200
 *                 description: 位置
 *                 example: "A区鹅舍1号"
 *               description:
 *                 type: string
 *                 maxLength: 500
 *                 description: 描述信息
 *                 example: "优质白鹅，适合产蛋"
 *     responses:
 *       201:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Flock'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */

/**
 * @swagger
 * /api/v1/flocks/{id}:
 *   get:
 *     tags: [flocks]
 *     summary: 获取鹅群详情
 *     description: 根据ID获取鹅群的详细信息
 *     security:
 *       - BearerAuth: []
 *       - TenantAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdPathParam'
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Flock'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *   put:
 *     tags: [flocks]
 *     summary: 更新鹅群信息
 *     description: 更新鹅群的完整信息
 *     security:
 *       - BearerAuth: []
 *       - TenantAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdPathParam'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 100
 *               breed:
 *                 type: string
 *                 enum: [white_goose, grey_goose, toulouse, embden, chinese, african, sebastopol, other]
 *               totalCount:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 10000
 *               currentCount:
 *                 type: integer
 *                 minimum: 0
 *               status:
 *                 type: string
 *                 enum: [active, inactive, sold, deceased]
 *               location:
 *                 type: string
 *                 maxLength: 200
 *               description:
 *                 type: string
 *                 maxLength: 500
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Flock'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *   delete:
 *     tags: [flocks]
 *     summary: 删除鹅群
 *     description: 删除指定的鹅群记录
 *     security:
 *       - BearerAuth: []
 *       - TenantAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdPathParam'
 *     responses:
 *       200:
 *         $ref: '#/components/responses/Success'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 */

/**
 * @swagger
 * /api/v1/health/records:
 *   get:
 *     tags: [health]
 *     summary: 获取健康记录列表
 *     description: 获取健康检查记录列表，支持分页和筛选
 *     security:
 *       - BearerAuth: []
 *       - TenantAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/LimitParam'
 *       - name: flockId
 *         in: query
 *         description: 鹅群ID筛选
 *         schema:
 *           type: integer
 *       - name: checkType
 *         in: query
 *         description: 检查类型筛选
 *         schema:
 *           type: string
 *           enum: [routine, emergency, vaccination, treatment]
 *       - name: result
 *         in: query
 *         description: 检查结果筛选
 *         schema:
 *           type: string
 *           enum: [healthy, sick, recovering, critical]
 *       - name: startDate
 *         in: query
 *         description: 开始日期
 *         schema:
 *           type: string
 *           format: date
 *       - name: endDate
 *         in: query
 *         description: 结束日期
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         items:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/HealthRecord'
 *                         pagination:
 *                           $ref: '#/components/schemas/PaginationInfo'
 *   post:
 *     tags: [health]
 *     summary: 创建健康记录
 *     description: 创建新的健康检查记录
 *     security:
 *       - BearerAuth: []
 *       - TenantAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - flockId
 *               - checkType
 *               - checkDate
 *               - result
 *             properties:
 *               flockId:
 *                 type: integer
 *                 description: 鹅群ID
 *               checkType:
 *                 type: string
 *                 enum: [routine, emergency, vaccination, treatment]
 *                 description: 检查类型
 *               checkDate:
 *                 type: string
 *                 format: date-time
 *                 description: 检查日期
 *               symptoms:
 *                 type: string
 *                 description: 症状描述
 *               temperature:
 *                 type: number
 *                 format: float
 *                 description: 体温
 *               weight:
 *                 type: number
 *                 format: float
 *                 description: 体重
 *               result:
 *                 type: string
 *                 enum: [healthy, sick, recovering, critical]
 *                 description: 检查结果
 *               diagnosis:
 *                 type: string
 *                 description: 诊断结果
 *               treatment:
 *                 type: string
 *                 description: 治疗方案
 *               medicine:
 *                 type: string
 *                 description: 用药信息
 *               dosage:
 *                 type: string
 *                 description: 用药剂量
 *               notes:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       201:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/HealthRecord'
 */

/**
 * @swagger
 * /api/v1/production/records:
 *   get:
 *     tags: [production]
 *     summary: 获取生产记录列表
 *     description: 获取生产记录列表，支持分页和筛选
 *     security:
 *       - BearerAuth: []
 *       - TenantAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/LimitParam'
 *       - name: flockId
 *         in: query
 *         description: 鹅群ID筛选
 *         schema:
 *           type: integer
 *       - name: startDate
 *         in: query
 *         description: 开始日期
 *         schema:
 *           type: string
 *           format: date
 *       - name: endDate
 *         in: query
 *         description: 结束日期
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         items:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/ProductionRecord'
 *                         pagination:
 *                           $ref: '#/components/schemas/PaginationInfo'
 *   post:
 *     tags: [production]
 *     summary: 创建生产记录
 *     description: 创建新的生产记录
 *     security:
 *       - BearerAuth: []
 *       - TenantAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - flockId
 *               - recordDate
 *             properties:
 *               flockId:
 *                 type: integer
 *                 description: 鹅群ID
 *               recordDate:
 *                 type: string
 *                 format: date
 *                 description: 记录日期
 *               eggCount:
 *                 type: integer
 *                 minimum: 0
 *                 description: 产蛋数量
 *               feedConsumption:
 *                 type: number
 *                 format: float
 *                 minimum: 0
 *                 description: 饲料消耗(kg)
 *               waterConsumption:
 *                 type: number
 *                 format: float
 *                 minimum: 0
 *                 description: 用水量(L)
 *               mortality:
 *                 type: integer
 *                 minimum: 0
 *                 description: 死亡数量
 *               notes:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       201:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/ProductionRecord'
 */

/**
 * @swagger
 * /api/v1/health/stats:
 *   get:
 *     tags: [health]
 *     summary: 获取健康统计数据
 *     description: 获取健康相关的统计数据和趋势
 *     security:
 *       - BearerAuth: []
 *       - TenantAuth: []
 *     parameters:
 *       - name: period
 *         in: query
 *         description: 统计周期
 *         schema:
 *           type: string
 *           enum: [week, month, quarter, year]
 *           default: month
 *       - name: flockId
 *         in: query
 *         description: 鹅群ID筛选
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         overview:
 *                           type: object
 *                           properties:
 *                             totalRecords:
 *                               type: integer
 *                             healthyCount:
 *                               type: integer
 *                             sickCount:
 *                               type: integer
 *                             criticalCount:
 *                               type: integer
 *                             healthRate:
 *                               type: number
 *                               format: float
 *                               minimum: 0
 *                               maximum: 100
 *                         trends:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               date:
 *                                 type: string
 *                                 format: date
 *                               healthyCount:
 *                                 type: integer
 *                               sickCount:
 *                                 type: integer
 *                               checkCount:
 *                                 type: integer
 */

/**
 * @swagger
 * /api/v1/production/stats:
 *   get:
 *     tags: [production]
 *     summary: 获取生产统计数据
 *     description: 获取生产相关的统计数据和趋势
 *     security:
 *       - BearerAuth: []
 *       - TenantAuth: []
 *     parameters:
 *       - name: period
 *         in: query
 *         description: 统计周期
 *         schema:
 *           type: string
 *           enum: [week, month, quarter, year]
 *           default: month
 *       - name: flockId
 *         in: query
 *         description: 鹅群ID筛选
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         overview:
 *                           type: object
 *                           properties:
 *                             totalEggs:
 *                               type: integer
 *                             totalFeed:
 *                               type: number
 *                               format: float
 *                             totalWater:
 *                               type: number
 *                               format: float
 *                             avgEggsPerDay:
 *                               type: number
 *                               format: float
 *                             feedEfficiency:
 *                               type: number
 *                               format: float
 *                         trends:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               date:
 *                                 type: string
 *                                 format: date
 *                               eggCount:
 *                                 type: integer
 *                               feedConsumption:
 *                                 type: number
 *                                 format: float
 *                               waterConsumption:
 *                                 type: number
 *                                 format: float
 */
