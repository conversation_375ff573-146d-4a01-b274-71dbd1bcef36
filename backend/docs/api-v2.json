"{\"openapi\":\"3.1.0\",\"info\":{\"title\":\"智慧养鹅SaaS平台 API v2\",\"version\":\"2.0.0\"},\"paths\":{\"/health\":{\"get\":{\"operationId\":\"GetHealth\",\"summary\":\"健康检查\",\"description\":\"检查服务器和相关服务的健康状态\",\"tags\":[\"system\"],\"responses\":{\"200\":{\"description\":\"GET /health 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetHealth\"}}}},\"400\":{\"description\":\"GET /health 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetHealth\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/flocks\":{\"get\":{\"operationId\":\"GetV2Flocks\",\"summary\":\"获取鹅群列表\",\"description\":\"获取当前用户的鹅群列表，支持分页、搜索和排序\",\"tags\":[\"flocks\"],\"parameters\":[{\"name\":\"page\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Page\"}},{\"name\":\"limit\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Limit\"}},{\"name\":\"search\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Search\"}},{\"name\":\"status\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Status\"}},{\"name\":\"breed\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Breed\"}},{\"name\":\"ageGroup\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/AgeGroup\"}},{\"name\":\"sortBy\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/SortBy\"}},{\"name\":\"sortOrder\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/SortOrder\"}}],\"responses\":{\"200\":{\"description\":\"GET /v2/flocks 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2Flocks\"}}}},\"400\":{\"description\":\"GET /v2/flocks 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2Flocks\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/flocks/create\":{\"post\":{\"operationId\":\"PostV2FlocksCreate\",\"summary\":\"创建鹅群\",\"description\":\"创建新的鹅群记录\",\"tags\":[\"flocks\"],\"requestBody\":{\"description\":\"PostV2FlocksCreate 请求体\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PostV2FlocksCreate\"}}}},\"responses\":{\"200\":{\"description\":\"POST /v2/flocks/create 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PostV2FlocksCreate\"}}}},\"400\":{\"description\":\"POST /v2/flocks/create 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PostV2FlocksCreate\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/flocks/{id}\":{\"get\":{\"operationId\":\"GetV2FlocksId\",\"summary\":\"获取鹅群详情\",\"description\":\"根据ID获取鹅群的详细信息\",\"tags\":[\"flocks\"],\"parameters\":[{\"name\":\"id\",\"in\":\"path\",\"required\":true,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Id\"}}],\"responses\":{\"200\":{\"description\":\"GET /v2/flocks/:id 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2FlocksId\"}}}},\"400\":{\"description\":\"GET /v2/flocks/:id 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2FlocksId\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/flocks/{id}/update\":{\"patch\":{\"operationId\":\"PatchV2FlocksIdUpdate\",\"summary\":\"更新鹅群\",\"description\":\"更新鹅群的信息\",\"tags\":[\"flocks\"],\"parameters\":[{\"name\":\"id\",\"in\":\"path\",\"required\":true,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Id\"}}],\"requestBody\":{\"description\":\"PatchV2FlocksIdUpdate 请求体\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PatchV2FlocksIdUpdate\"}}}},\"responses\":{\"200\":{\"description\":\"PATCH /v2/flocks/:id/update 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PatchV2FlocksIdUpdate\"}}}},\"400\":{\"description\":\"PATCH /v2/flocks/:id/update 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PatchV2FlocksIdUpdate\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/flocks/{id}/delete\":{\"delete\":{\"operationId\":\"DeleteV2FlocksIdDelete\",\"summary\":\"删除鹅群\",\"description\":\"删除指定的鹅群记录\",\"tags\":[\"flocks\"],\"parameters\":[{\"name\":\"id\",\"in\":\"path\",\"required\":true,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Id\"}}],\"responses\":{\"200\":{\"description\":\"DELETE /v2/flocks/:id/delete 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/DeleteV2FlocksIdDelete\"}}}},\"400\":{\"description\":\"DELETE /v2/flocks/:id/delete 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/DeleteV2FlocksIdDelete\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/health/records\":{\"get\":{\"operationId\":\"GetV2HealthRecords\",\"summary\":\"获取健康记录列表\",\"description\":\"获取当前租户的健康记录列表，支持分页、搜索和过滤\",\"tags\":[\"health\"],\"parameters\":[{\"name\":\"page\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Page\"}},{\"name\":\"limit\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Limit\"}},{\"name\":\"flockId\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/FlockId\"}},{\"name\":\"status\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Status\"}},{\"name\":\"checkType\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/CheckType\"}},{\"name\":\"startDate\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/StartDate\"}},{\"name\":\"endDate\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/EndDate\"}},{\"name\":\"search\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Search\"}},{\"name\":\"sortBy\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/SortBy\"}},{\"name\":\"sortOrder\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/SortOrder\"}}],\"responses\":{\"200\":{\"description\":\"GET /v2/health/records 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2HealthRecords\"}}}},\"400\":{\"description\":\"GET /v2/health/records 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2HealthRecords\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/health/records/create\":{\"post\":{\"operationId\":\"PostV2HealthRecordsCreate\",\"summary\":\"创建健康记录\",\"description\":\"为指定鹅群创建新的健康检查记录\",\"tags\":[\"health\"],\"requestBody\":{\"description\":\"PostV2HealthRecordsCreate 请求体\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PostV2HealthRecordsCreate\"}}}},\"responses\":{\"200\":{\"description\":\"POST /v2/health/records/create 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PostV2HealthRecordsCreate\"}}}},\"400\":{\"description\":\"POST /v2/health/records/create 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PostV2HealthRecordsCreate\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/health/records/{id}\":{\"get\":{\"operationId\":\"GetV2HealthRecordsId\",\"summary\":\"获取健康记录详情\",\"description\":\"根据ID获取特定健康记录的详细信息\",\"tags\":[\"health\"],\"parameters\":[{\"name\":\"id\",\"in\":\"path\",\"required\":true,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Id\"}}],\"responses\":{\"200\":{\"description\":\"GET /v2/health/records/:id 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2HealthRecordsId\"}}}},\"400\":{\"description\":\"GET /v2/health/records/:id 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2HealthRecordsId\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/health/records/{id}/update\":{\"patch\":{\"operationId\":\"PatchV2HealthRecordsIdUpdate\",\"summary\":\"更新健康记录\",\"description\":\"更新指定健康记录的信息\",\"tags\":[\"health\"],\"parameters\":[{\"name\":\"id\",\"in\":\"path\",\"required\":true,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Id\"}}],\"requestBody\":{\"description\":\"PatchV2HealthRecordsIdUpdate 请求体\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PatchV2HealthRecordsIdUpdate\"}}}},\"responses\":{\"200\":{\"description\":\"PATCH /v2/health/records/:id/update 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PatchV2HealthRecordsIdUpdate\"}}}},\"400\":{\"description\":\"PATCH /v2/health/records/:id/update 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PatchV2HealthRecordsIdUpdate\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/health/records/{id}/delete\":{\"delete\":{\"operationId\":\"DeleteV2HealthRecordsIdDelete\",\"summary\":\"删除健康记录\",\"description\":\"删除指定的健康记录\",\"tags\":[\"health\"],\"parameters\":[{\"name\":\"id\",\"in\":\"path\",\"required\":true,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Id\"}}],\"responses\":{\"200\":{\"description\":\"DELETE /v2/health/records/:id/delete 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/DeleteV2HealthRecordsIdDelete\"}}}},\"400\":{\"description\":\"DELETE /v2/health/records/:id/delete 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/DeleteV2HealthRecordsIdDelete\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/health/stats\":{\"get\":{\"operationId\":\"GetV2HealthStats\",\"summary\":\"获取健康统计\",\"description\":\"获取健康记录的统计信息和趋势数据\",\"tags\":[\"health\"],\"parameters\":[{\"name\":\"flockId\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/FlockId\"}},{\"name\":\"startDate\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/StartDate\"}},{\"name\":\"endDate\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/EndDate\"}}],\"responses\":{\"200\":{\"description\":\"GET /v2/health/stats 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2HealthStats\"}}}},\"400\":{\"description\":\"GET /v2/health/stats 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2HealthStats\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/production/records\":{\"get\":{\"operationId\":\"GetV2ProductionRecords\",\"summary\":\"获取生产记录列表\",\"description\":\"获取当前租户的生产记录列表，支持分页、搜索和过滤\",\"tags\":[\"production\"],\"parameters\":[{\"name\":\"page\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Page\"}},{\"name\":\"limit\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Limit\"}},{\"name\":\"flockId\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/FlockId\"}},{\"name\":\"recordType\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/RecordType\"}},{\"name\":\"startDate\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/StartDate\"}},{\"name\":\"endDate\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/EndDate\"}},{\"name\":\"weather\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Weather\"}},{\"name\":\"search\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Search\"}},{\"name\":\"sortBy\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/SortBy\"}},{\"name\":\"sortOrder\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/SortOrder\"}}],\"responses\":{\"200\":{\"description\":\"GET /v2/production/records 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2ProductionRecords\"}}}},\"400\":{\"description\":\"GET /v2/production/records 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2ProductionRecords\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/production/records/create\":{\"post\":{\"operationId\":\"PostV2ProductionRecordsCreate\",\"summary\":\"创建生产记录\",\"description\":\"为指定鹅群创建新的生产记录\",\"tags\":[\"production\"],\"requestBody\":{\"description\":\"PostV2ProductionRecordsCreate 请求体\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PostV2ProductionRecordsCreate\"}}}},\"responses\":{\"200\":{\"description\":\"POST /v2/production/records/create 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PostV2ProductionRecordsCreate\"}}}},\"400\":{\"description\":\"POST /v2/production/records/create 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PostV2ProductionRecordsCreate\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/production/records/{id}\":{\"get\":{\"operationId\":\"GetV2ProductionRecordsId\",\"summary\":\"获取生产记录详情\",\"description\":\"根据ID获取特定生产记录的详细信息\",\"tags\":[\"production\"],\"parameters\":[{\"name\":\"id\",\"in\":\"path\",\"required\":true,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Id\"}}],\"responses\":{\"200\":{\"description\":\"GET /v2/production/records/:id 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2ProductionRecordsId\"}}}},\"400\":{\"description\":\"GET /v2/production/records/:id 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2ProductionRecordsId\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/production/records/{id}/update\":{\"patch\":{\"operationId\":\"PatchV2ProductionRecordsIdUpdate\",\"summary\":\"更新生产记录\",\"description\":\"更新指定生产记录的信息\",\"tags\":[\"production\"],\"parameters\":[{\"name\":\"id\",\"in\":\"path\",\"required\":true,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Id\"}}],\"requestBody\":{\"description\":\"PatchV2ProductionRecordsIdUpdate 请求体\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PatchV2ProductionRecordsIdUpdate\"}}}},\"responses\":{\"200\":{\"description\":\"PATCH /v2/production/records/:id/update 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PatchV2ProductionRecordsIdUpdate\"}}}},\"400\":{\"description\":\"PATCH /v2/production/records/:id/update 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/PatchV2ProductionRecordsIdUpdate\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/production/records/{id}/delete\":{\"delete\":{\"operationId\":\"DeleteV2ProductionRecordsIdDelete\",\"summary\":\"删除生产记录\",\"description\":\"删除指定的生产记录\",\"tags\":[\"production\"],\"parameters\":[{\"name\":\"id\",\"in\":\"path\",\"required\":true,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Id\"}}],\"responses\":{\"200\":{\"description\":\"DELETE /v2/production/records/:id/delete 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/DeleteV2ProductionRecordsIdDelete\"}}}},\"400\":{\"description\":\"DELETE /v2/production/records/:id/delete 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/DeleteV2ProductionRecordsIdDelete\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/production/stats\":{\"get\":{\"operationId\":\"GetV2ProductionStats\",\"summary\":\"获取生产统计\",\"description\":\"获取生产记录的统计信息\",\"tags\":[\"production\"],\"parameters\":[{\"name\":\"flockId\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/FlockId\"}},{\"name\":\"startDate\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/StartDate\"}},{\"name\":\"endDate\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/EndDate\"}},{\"name\":\"recordType\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/RecordType\"}}],\"responses\":{\"200\":{\"description\":\"GET /v2/production/stats 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2ProductionStats\"}}}},\"400\":{\"description\":\"GET /v2/production/stats 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2ProductionStats\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}},\"/v2/production/trends\":{\"get\":{\"operationId\":\"GetV2ProductionTrends\",\"summary\":\"获取生产趋势\",\"description\":\"获取生产记录的趋势数据\",\"tags\":[\"production\"],\"parameters\":[{\"name\":\"flockId\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/FlockId\"}},{\"name\":\"startDate\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/StartDate\"}},{\"name\":\"endDate\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/EndDate\"}},{\"name\":\"period\",\"in\":\"query\",\"required\":false,\"description\":\"请求参数\",\"schema\":{\"$ref\":\"#/components/schemas/Period\"}}],\"responses\":{\"200\":{\"description\":\"GET /v2/production/trends 成功响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2ProductionTrends\"}}}},\"400\":{\"description\":\"GET /v2/production/trends 错误响应\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/GetV2ProductionTrends\"},\"examples\":{\"example1\":{\"value\":{\"status\":\"error\",\"error\":{\"message\":\"Sample error message\"}}}}}}}}}}},\"components\":{\"schemas\":{\"GetHealth\":{\"type\":\"object\",\"properties\":{\"status\":{\"type\":\"string\",\"const\":\"error\"},\"error\":{\"type\":\"object\",\"properties\":{\"message\":{\"type\":\"string\"}},\"required\":[\"message\"]}},\"required\":[\"status\",\"error\"]},\"Page\":{\"type\":\"integer\",\"format\":\"int64\",\"exclusiveMinimum\":0,\"maximum\":9007199254740991,\"default\":1},\"Limit\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":1,\"maximum\":100,\"default\":10},\"Search\":{\"type\":\"string\",\"maxLength\":100},\"Status\":{\"type\":\"string\",\"enum\":[\"healthy\",\"sick\",\"recovering\",\"dead\"]},\"Breed\":{\"type\":\"string\",\"enum\":[\"white_goose\",\"grey_goose\",\"toulouse\",\"embden\",\"chinese\",\"african\",\"sebastopol\",\"other\"]},\"AgeGroup\":{\"type\":\"string\",\"enum\":[\"gosling\",\"young\",\"adult\",\"breeder\"]},\"SortBy\":{\"type\":\"string\",\"enum\":[\"recordDate\",\"createdAt\",\"eggCount\",\"feedConsumption\"],\"default\":\"recordDate\"},\"SortOrder\":{\"type\":\"string\",\"enum\":[\"ASC\",\"DESC\"],\"default\":\"DESC\"},\"GetV2Flocks\":{\"type\":\"object\",\"properties\":{\"status\":{\"type\":\"string\",\"const\":\"error\"},\"error\":{\"type\":\"object\",\"properties\":{\"message\":{\"type\":\"string\"}},\"required\":[\"message\"]}},\"required\":[\"status\",\"error\"]},\"PostV2FlocksCreate\":{\"type\":\"object\",\"properties\":{\"name\":{\"type\":\"string\",\"minLength\":1,\"maxLength\":100},\"breed\":{\"type\":\"string\",\"enum\":[\"white_goose\",\"grey_goose\",\"toulouse\",\"embden\",\"chinese\",\"african\",\"sebastopol\",\"other\"]},\"totalCount\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":1,\"maximum\":10000},\"maleCount\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":0,\"maximum\":9007199254740991,\"default\":0},\"femaleCount\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":0,\"maximum\":9007199254740991,\"default\":0},\"ageGroup\":{\"type\":\"string\",\"enum\":[\"gosling\",\"young\",\"adult\",\"breeder\"],\"default\":\"young\"},\"establishedDate\":{\"type\":\"string\",\"format\":\"date-time\",\"pattern\":\"^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}(\\\\.\\\\d+)?Z$\",\"default\":\"2025-07-31T16:23:41.200Z\"},\"location\":{\"type\":\"string\",\"maxLength\":200},\"description\":{\"type\":\"string\",\"maxLength\":500}},\"required\":[\"name\",\"breed\",\"totalCount\"]},\"Id\":{\"type\":\"integer\",\"format\":\"int64\",\"exclusiveMinimum\":0,\"maximum\":9007199254740991},\"GetV2FlocksId\":{\"type\":\"object\",\"properties\":{\"status\":{\"type\":\"string\",\"const\":\"error\"},\"error\":{\"type\":\"object\",\"properties\":{\"message\":{\"type\":\"string\"}},\"required\":[\"message\"]}},\"required\":[\"status\",\"error\"]},\"PatchV2FlocksIdUpdate\":{\"type\":\"object\",\"properties\":{\"name\":{\"type\":\"string\",\"minLength\":1,\"maxLength\":100},\"breed\":{\"type\":\"string\",\"enum\":[\"white_goose\",\"grey_goose\",\"toulouse\",\"embden\",\"chinese\",\"african\",\"sebastopol\",\"other\"]},\"totalCount\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":1,\"maximum\":10000},\"maleCount\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":0,\"maximum\":9007199254740991,\"default\":0},\"femaleCount\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":0,\"maximum\":9007199254740991,\"default\":0},\"ageGroup\":{\"type\":\"string\",\"enum\":[\"gosling\",\"young\",\"adult\",\"breeder\"],\"default\":\"young\"},\"establishedDate\":{\"type\":\"string\",\"format\":\"date-time\",\"pattern\":\"^\\\\d{4}-\\\\d{2}-\\\\d{2}T\\\\d{2}:\\\\d{2}:\\\\d{2}(\\\\.\\\\d+)?Z$\",\"default\":\"2025-07-31T16:23:41.202Z\"},\"location\":{\"type\":\"string\",\"maxLength\":200},\"description\":{\"type\":\"string\",\"maxLength\":500},\"status\":{\"type\":\"string\",\"enum\":[\"active\",\"inactive\",\"sold\",\"deceased\"]},\"currentCount\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":0,\"maximum\":9007199254740991}},\"required\":[]},\"DeleteV2FlocksIdDelete\":{\"type\":\"object\",\"properties\":{\"status\":{\"type\":\"string\",\"const\":\"error\"},\"error\":{\"type\":\"object\",\"properties\":{\"message\":{\"type\":\"string\"}},\"required\":[\"message\"]}},\"required\":[\"status\",\"error\"]},\"FlockId\":{\"type\":\"integer\",\"format\":\"int64\",\"exclusiveMinimum\":0,\"maximum\":9007199254740991},\"CheckType\":{\"type\":\"string\",\"enum\":[\"routine\",\"disease\",\"vaccination\",\"treatment\",\"emergency\"]},\"StartDate\":{\"type\":\"string\"},\"EndDate\":{\"type\":\"string\"},\"GetV2HealthRecords\":{\"type\":\"object\",\"properties\":{\"status\":{\"type\":\"string\",\"const\":\"error\"},\"error\":{\"type\":\"object\",\"properties\":{\"message\":{\"type\":\"string\"}},\"required\":[\"message\"]}},\"required\":[\"status\",\"error\"]},\"PostV2HealthRecordsCreate\":{\"type\":\"object\",\"properties\":{\"flockId\":{\"type\":\"integer\",\"format\":\"int64\",\"exclusiveMinimum\":0,\"maximum\":9007199254740991},\"checkType\":{\"type\":\"string\",\"enum\":[\"routine\",\"disease\",\"vaccination\",\"treatment\",\"emergency\"]},\"checkDate\":{\"type\":\"string\"},\"symptoms\":{\"type\":\"string\",\"maxLength\":1000},\"temperature\":{\"type\":\"number\",\"format\":\"double\",\"minimum\":35,\"maximum\":45},\"weight\":{\"type\":\"number\",\"format\":\"double\",\"exclusiveMinimum\":0,\"maximum\":1.7976931348623157e+308},\"result\":{\"type\":\"string\",\"enum\":[\"healthy\",\"sick\",\"recovering\",\"dead\"]},\"diagnosis\":{\"type\":\"string\",\"maxLength\":1000},\"treatment\":{\"type\":\"string\",\"maxLength\":1000},\"medicine\":{\"type\":\"string\",\"maxLength\":500},\"dosage\":{\"type\":\"string\",\"maxLength\":200},\"notes\":{\"type\":\"string\",\"maxLength\":1000}},\"required\":[\"flockId\",\"checkType\",\"checkDate\",\"result\"]},\"GetV2HealthRecordsId\":{\"type\":\"object\",\"properties\":{\"status\":{\"type\":\"string\",\"const\":\"error\"},\"error\":{\"type\":\"object\",\"properties\":{\"message\":{\"type\":\"string\"}},\"required\":[\"message\"]}},\"required\":[\"status\",\"error\"]},\"PatchV2HealthRecordsIdUpdate\":{\"type\":\"object\",\"properties\":{\"checkType\":{\"type\":\"string\",\"enum\":[\"routine\",\"disease\",\"vaccination\",\"treatment\",\"emergency\"]},\"checkDate\":{\"type\":\"string\"},\"symptoms\":{\"type\":\"string\",\"maxLength\":1000},\"temperature\":{\"type\":\"number\",\"format\":\"double\",\"minimum\":35,\"maximum\":45},\"weight\":{\"type\":\"number\",\"format\":\"double\",\"exclusiveMinimum\":0,\"maximum\":1.7976931348623157e+308},\"result\":{\"type\":\"string\",\"enum\":[\"healthy\",\"sick\",\"recovering\",\"dead\"]},\"diagnosis\":{\"type\":\"string\",\"maxLength\":1000},\"treatment\":{\"type\":\"string\",\"maxLength\":1000},\"medicine\":{\"type\":\"string\",\"maxLength\":500},\"dosage\":{\"type\":\"string\",\"maxLength\":200},\"notes\":{\"type\":\"string\",\"maxLength\":1000}},\"required\":[]},\"DeleteV2HealthRecordsIdDelete\":{\"type\":\"object\",\"properties\":{\"status\":{\"type\":\"string\",\"const\":\"error\"},\"error\":{\"type\":\"object\",\"properties\":{\"message\":{\"type\":\"string\"}},\"required\":[\"message\"]}},\"required\":[\"status\",\"error\"]},\"GetV2HealthStats\":{\"type\":\"object\",\"properties\":{\"status\":{\"type\":\"string\",\"const\":\"error\"},\"error\":{\"type\":\"object\",\"properties\":{\"message\":{\"type\":\"string\"}},\"required\":[\"message\"]}},\"required\":[\"status\",\"error\"]},\"RecordType\":{\"type\":\"string\",\"enum\":[\"daily\",\"feeding\",\"collection\",\"cleaning\",\"maintenance\"]},\"Weather\":{\"type\":\"string\",\"enum\":[\"sunny\",\"cloudy\",\"overcast\",\"rainy\",\"foggy\",\"windy\",\"stormy\"]},\"GetV2ProductionRecords\":{\"type\":\"object\",\"properties\":{\"status\":{\"type\":\"string\",\"const\":\"error\"},\"error\":{\"type\":\"object\",\"properties\":{\"message\":{\"type\":\"string\"}},\"required\":[\"message\"]}},\"required\":[\"status\",\"error\"]},\"PostV2ProductionRecordsCreate\":{\"type\":\"object\",\"properties\":{\"flockId\":{\"type\":\"integer\",\"format\":\"int64\",\"exclusiveMinimum\":0,\"maximum\":9007199254740991},\"recordType\":{\"type\":\"string\",\"enum\":[\"daily\",\"feeding\",\"collection\",\"cleaning\",\"maintenance\"],\"default\":\"daily\"},\"recordDate\":{\"type\":\"string\"},\"eggCount\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":0,\"maximum\":9007199254740991,\"default\":0},\"eggWeight\":{\"type\":\"number\",\"format\":\"double\",\"exclusiveMinimum\":0,\"maximum\":1.7976931348623157e+308},\"feedConsumption\":{\"type\":\"number\",\"format\":\"double\",\"minimum\":0,\"maximum\":1.7976931348623157e+308,\"default\":0},\"feedCost\":{\"type\":\"number\",\"format\":\"double\",\"minimum\":0,\"maximum\":1.7976931348623157e+308},\"waterConsumption\":{\"type\":\"number\",\"format\":\"double\",\"minimum\":0,\"maximum\":1.7976931348623157e+308},\"temperature\":{\"type\":\"number\",\"format\":\"double\",\"minimum\":-20,\"maximum\":50},\"humidity\":{\"type\":\"number\",\"format\":\"double\",\"minimum\":0,\"maximum\":100},\"weather\":{\"type\":\"string\",\"enum\":[\"sunny\",\"cloudy\",\"overcast\",\"rainy\",\"foggy\",\"windy\",\"stormy\"]},\"mortality\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":0,\"maximum\":9007199254740991,\"default\":0},\"healthyCount\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":0,\"maximum\":9007199254740991},\"sickCount\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":0,\"maximum\":9007199254740991,\"default\":0},\"notes\":{\"type\":\"string\",\"maxLength\":2000}},\"required\":[\"flockId\",\"recordDate\"]},\"GetV2ProductionRecordsId\":{\"type\":\"object\",\"properties\":{\"status\":{\"type\":\"string\",\"const\":\"error\"},\"error\":{\"type\":\"object\",\"properties\":{\"message\":{\"type\":\"string\"}},\"required\":[\"message\"]}},\"required\":[\"status\",\"error\"]},\"PatchV2ProductionRecordsIdUpdate\":{\"type\":\"object\",\"properties\":{\"recordType\":{\"type\":\"string\",\"enum\":[\"daily\",\"feeding\",\"collection\",\"cleaning\",\"maintenance\"]},\"recordDate\":{\"type\":\"string\"},\"eggCount\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":0,\"maximum\":9007199254740991},\"eggWeight\":{\"type\":\"number\",\"format\":\"double\",\"exclusiveMinimum\":0,\"maximum\":1.7976931348623157e+308},\"feedConsumption\":{\"type\":\"number\",\"format\":\"double\",\"minimum\":0,\"maximum\":1.7976931348623157e+308},\"feedCost\":{\"type\":\"number\",\"format\":\"double\",\"minimum\":0,\"maximum\":1.7976931348623157e+308},\"waterConsumption\":{\"type\":\"number\",\"format\":\"double\",\"minimum\":0,\"maximum\":1.7976931348623157e+308},\"temperature\":{\"type\":\"number\",\"format\":\"double\",\"minimum\":-20,\"maximum\":50},\"humidity\":{\"type\":\"number\",\"format\":\"double\",\"minimum\":0,\"maximum\":100},\"weather\":{\"type\":\"string\",\"enum\":[\"sunny\",\"cloudy\",\"overcast\",\"rainy\",\"foggy\",\"windy\",\"stormy\"]},\"mortality\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":0,\"maximum\":9007199254740991},\"healthyCount\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":0,\"maximum\":9007199254740991},\"sickCount\":{\"type\":\"integer\",\"format\":\"int64\",\"minimum\":0,\"maximum\":9007199254740991},\"notes\":{\"type\":\"string\",\"maxLength\":2000}},\"required\":[]},\"DeleteV2ProductionRecordsIdDelete\":{\"type\":\"object\",\"properties\":{\"status\":{\"type\":\"string\",\"const\":\"error\"},\"error\":{\"type\":\"object\",\"properties\":{\"message\":{\"type\":\"string\"}},\"required\":[\"message\"]}},\"required\":[\"status\",\"error\"]},\"GetV2ProductionStats\":{\"type\":\"object\",\"properties\":{\"status\":{\"type\":\"string\",\"const\":\"error\"},\"error\":{\"type\":\"object\",\"properties\":{\"message\":{\"type\":\"string\"}},\"required\":[\"message\"]}},\"required\":[\"status\",\"error\"]},\"Period\":{\"type\":\"string\",\"enum\":[\"day\",\"week\",\"month\"],\"default\":\"day\"},\"GetV2ProductionTrends\":{\"type\":\"object\",\"properties\":{\"status\":{\"type\":\"string\",\"const\":\"error\"},\"error\":{\"type\":\"object\",\"properties\":{\"message\":{\"type\":\"string\"}},\"required\":[\"message\"]}},\"required\":[\"status\",\"error\"]}},\"responses\":{},\"parameters\":{},\"examples\":{},\"requestBodies\":{},\"headers\":{},\"securitySchemes\":{},\"links\":{},\"callbacks\":{}},\"tags\":[],\"servers\":[{\"url\":\"http://localhost:3001\"}]}"