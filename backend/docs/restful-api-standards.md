# RESTful API 设计规范 v2.9.2

## 概述

本文档定义了智慧养鹅项目后端API的RESTful设计规范，确保API接口的一致性、可维护性和易用性。

## 基本原则

### 1. URL设计规范

#### 资源命名

- 使用名词复数形式表示资源集合：`/users`, `/health-records`, `/production-records`
- 使用小写字母，单词间用连字符分隔：`/health-records` 而不是 `/healthRecords`
- 避免使用动词：使用 `/users` 而不是 `/getUsers`

#### 层级关系

- 表示资源间的层级关系：`/users/{id}/health-records`
- 避免过深的嵌套，最多3层：`/users/{id}/records/{recordId}/comments`

#### 查询参数

- 使用查询参数进行过滤、排序、分页：
  ```
  GET /health-records?status=active&sort=createdAt&page=1&limit=20
  ```

### 2. HTTP方法规范

| 方法   | 用途         | 示例                                  |
| ------ | ------------ | ------------------------------------- |
| GET    | 获取资源     | `GET /users` - 获取用户列表           |
| POST   | 创建资源     | `POST /users` - 创建新用户            |
| PUT    | 完整更新资源 | `PUT /users/123` - 完整更新用户信息   |
| PATCH  | 部分更新资源 | `PATCH /users/123` - 部分更新用户信息 |
| DELETE | 删除资源     | `DELETE /users/123` - 删除用户        |

### 3. 状态码规范

#### 成功响应

- `200 OK` - 请求成功
- `201 Created` - 资源创建成功
- `204 No Content` - 请求成功但无返回内容（如删除操作）

#### 客户端错误

- `400 Bad Request` - 请求参数错误
- `401 Unauthorized` - 未认证
- `403 Forbidden` - 无权限
- `404 Not Found` - 资源不存在
- `409 Conflict` - 资源冲突
- `422 Unprocessable Entity` - 请求格式正确但语义错误

#### 服务器错误

- `500 Internal Server Error` - 服务器内部错误
- `502 Bad Gateway` - 网关错误
- `503 Service Unavailable` - 服务不可用

### 4. 响应格式规范

#### 成功响应格式

```json
{
  "success": true,
  "data": {
    // 实际数据
  },
  "message": "操作成功",
  "timestamp": "2024-01-20T10:30:00Z"
}
```

#### 列表响应格式

```json
{
  "success": true,
  "data": {
    "items": [
      // 数据项
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5
    }
  },
  "message": "获取成功",
  "timestamp": "2024-01-20T10:30:00Z"
}
```

#### 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": [
      {
        "field": "email",
        "message": "邮箱格式不正确"
      }
    ]
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

## API端点设计

### 1. 用户管理 (Users)

```
GET    /api/v1/users              # 获取用户列表
POST   /api/v1/users              # 创建用户
GET    /api/v1/users/{id}         # 获取用户详情
PUT    /api/v1/users/{id}         # 更新用户信息
PATCH  /api/v1/users/{id}         # 部分更新用户信息
DELETE /api/v1/users/{id}         # 删除用户
GET    /api/v1/users/me           # 获取当前用户信息
PUT    /api/v1/users/me           # 更新当前用户信息
```

### 2. 认证管理 (Authentication)

```
POST   /api/v1/auth/register      # 用户注册
POST   /api/v1/auth/login         # 用户登录
POST   /api/v1/auth/logout        # 用户登出
POST   /api/v1/auth/refresh       # 刷新token
POST   /api/v1/auth/forgot-password  # 忘记密码
POST   /api/v1/auth/reset-password   # 重置密码
```

### 3. 健康记录 (Health Records)

```
GET    /api/v1/health-records     # 获取健康记录列表
POST   /api/v1/health-records     # 创建健康记录
GET    /api/v1/health-records/{id}  # 获取健康记录详情
PUT    /api/v1/health-records/{id}  # 更新健康记录
DELETE /api/v1/health-records/{id}  # 删除健康记录
GET    /api/v1/health-records/stats # 获取健康统计数据
```

### 4. 生产记录 (Production Records)

```
GET    /api/v1/production-records    # 获取生产记录列表
POST   /api/v1/production-records    # 创建生产记录
GET    /api/v1/production-records/{id}  # 获取生产记录详情
PUT    /api/v1/production-records/{id}  # 更新生产记录
DELETE /api/v1/production-records/{id}  # 删除生产记录
GET    /api/v1/production-records/stats # 获取生产统计数据
```

### 5. 物料管理 (Materials)

```
GET    /api/v1/materials          # 获取物料列表
POST   /api/v1/materials          # 创建物料
GET    /api/v1/materials/{id}     # 获取物料详情
PUT    /api/v1/materials/{id}     # 更新物料信息
DELETE /api/v1/materials/{id}     # 删除物料
GET    /api/v1/materials/categories  # 获取物料分类
GET    /api/v1/materials/low-stock   # 获取低库存物料
```

### 6. 库存记录 (Inventory Records)

```
GET    /api/v1/materials/{id}/inventory-records     # 获取物料库存记录
POST   /api/v1/materials/{id}/inventory-records     # 创建库存记录
GET    /api/v1/inventory-records/{id}               # 获取库存记录详情
PUT    /api/v1/inventory-records/{id}               # 更新库存记录
DELETE /api/v1/inventory-records/{id}               # 删除库存记录
```

## 查询参数规范

### 分页参数

- `page`: 页码，从1开始，默认1
- `limit`: 每页数量，默认20，最大100
- `offset`: 偏移量（可选，与page二选一）

### 排序参数

- `sort`: 排序字段，如 `createdAt`, `-createdAt`（负号表示降序）
- `order`: 排序方向，`asc` 或 `desc`

### 过滤参数

- `status`: 状态过滤
- `type`: 类型过滤
- `startDate`: 开始日期
- `endDate`: 结束日期
- `search`: 搜索关键词

### 字段选择

- `fields`: 指定返回字段，如 `fields=id,name,email`
- `include`: 包含关联数据，如 `include=profile,roles`

## 示例

### 获取用户列表

```
GET /api/v1/users?page=1&limit=20&sort=-createdAt&status=active&search=john
```

### 创建健康记录

```
POST /api/v1/health-records
Content-Type: application/json

{
  "type": "vaccination",
  "description": "疫苗接种",
  "quantity": 100,
  "date": "2024-01-20"
}
```

### 更新用户信息

```
PATCH /api/v1/users/123
Content-Type: application/json

{
  "name": "新用户名",
  "email": "<EMAIL>"
}
```

## 版本控制

- 使用URL路径进行版本控制：`/api/v1/`, `/api/v2/`
- 保持向后兼容性
- 在响应头中包含API版本信息：`X-API-Version: 1.0`

## 安全规范

### 认证

- 使用JWT Bearer Token进行认证
- Token放在Authorization头中：`Authorization: Bearer <token>`

### 权限控制

- 实现基于角色的访问控制(RBAC)
- 在路由中间件中进行权限检查

### 数据验证

- 对所有输入数据进行验证
- 使用适当的验证库（如Joi、express-validator）
- 返回详细的验证错误信息

## 错误处理

### 错误代码规范

- `VALIDATION_ERROR`: 数据验证错误
- `AUTHENTICATION_ERROR`: 认证错误
- `AUTHORIZATION_ERROR`: 授权错误
- `NOT_FOUND_ERROR`: 资源不存在
- `CONFLICT_ERROR`: 资源冲突
- `INTERNAL_ERROR`: 服务器内部错误

### 错误日志

- 记录所有4xx和5xx错误
- 包含请求ID、用户ID、时间戳等信息
- 不在响应中暴露敏感信息

## 性能优化

### 缓存策略

- 对不经常变化的数据使用缓存
- 设置适当的Cache-Control头
- 使用ETag进行条件请求

### 数据库优化

- 使用适当的索引
- 避免N+1查询问题
- 实现数据库连接池

### 响应优化

- 压缩响应数据
- 使用分页减少数据量
- 支持字段选择减少传输数据
