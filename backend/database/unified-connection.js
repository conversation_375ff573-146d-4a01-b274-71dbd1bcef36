const mysql = require('mysql2/promise');
const winston = require('winston');

// Logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

/**
 * 数据库连接配置
 */
const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_DATABASE || 'smart_goose_saas',
  charset: 'utf8mb4',
  timezone: '+08:00',
  
  // 连接池配置
  connectionLimit: process.env.DB_CONNECTION_LIMIT || 20,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  
  // SSL配置（生产环境建议启用）
  ssl: process.env.DB_SSL === 'true' ? {
    rejectUnauthorized: false
  } : false
};

/**
 * 数据库连接池
 */
let pool = null;

/**
 * 初始化数据库连接池
 */
async function initializePool() {
  try {
    pool = mysql.createPool({
      ...DB_CONFIG,
      waitForConnections: true,
      queueLimit: 0,
      
      // 连接事件处理
      acquireTimeout: 60000,
      timeout: 60000,
      
      // 重连配置
      reconnect: true,
      idleTimeout: 300000, // 5分钟
      maxIdle: 10,
      
      // 字符集配置
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    });

    // 测试连接
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();

    logger.info('Database connection pool initialized', {
      host: DB_CONFIG.host,
      port: DB_CONFIG.port,
      database: DB_CONFIG.database,
      connectionLimit: DB_CONFIG.connectionLimit
    });

    // 设置连接池事件监听
    pool.on('connection', (connection) => {
      logger.debug('New database connection established', {
        connectionId: connection.threadId
      });
    });

    pool.on('error', (error) => {
      logger.error('Database pool error', {
        error: error.message,
        code: error.code
      });
      
      // 如果是连接错误，尝试重新初始化
      if (error.code === 'PROTOCOL_CONNECTION_LOST') {
        handleDisconnect();
      }
    });

    return pool;
  } catch (error) {
    logger.error('Failed to initialize database connection pool', {
      error: error.message,
      code: error.code
    });
    throw error;
  }
}

/**
 * 处理数据库连接断开
 */
function handleDisconnect() {
  setTimeout(async () => {
    try {
      logger.info('Attempting to reconnect to database');
      await initializePool();
      logger.info('Database reconnection successful');
    } catch (error) {
      logger.error('Database reconnection failed', {
        error: error.message
      });
      handleDisconnect();
    }
  }, 5000);
}

/**
 * 数据库工具类
 */
class DatabaseConnection {
  /**
   * 获取连接池
   */
  static getPool() {
    if (!pool) {
      throw new Error('Database pool not initialized. Call initializePool() first.');
    }
    return pool;
  }

  /**
   * 获取单个连接
   */
  static async getConnection() {
    try {
      const connection = await pool.getConnection();
      return connection;
    } catch (error) {
      logger.error('Failed to get database connection', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 执行查询
   */
  static async query(sql, params = []) {
    const connection = await this.getConnection();
    
    try {
      const startTime = Date.now();
      const [rows] = await connection.execute(sql, params);
      const executionTime = Date.now() - startTime;

      logger.debug('Database query executed', {
        sql: sql.substring(0, 100) + (sql.length > 100 ? '...' : ''),
        paramCount: params.length,
        resultCount: Array.isArray(rows) ? rows.length : 1,
        executionTime
      });

      return rows;
    } catch (error) {
      logger.error('Database query failed', {
        sql: sql.substring(0, 100) + (sql.length > 100 ? '...' : ''),
        params: JSON.stringify(params).substring(0, 200),
        error: error.message,
        code: error.code
      });
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 执行事务
   */
  static async transaction(callback) {
    const connection = await this.getConnection();
    
    try {
      await connection.beginTransaction();
      
      const result = await callback(connection);
      
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      logger.error('Transaction failed and rolled back', {
        error: error.message
      });
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 批量插入
   */
  static async batchInsert(table, columns, data) {
    if (!data || data.length === 0) {
      return { affectedRows: 0 };
    }

    const placeholders = data.map(() => 
      `(${columns.map(() => '?').join(', ')})`
    ).join(', ');

    const sql = `INSERT INTO ${table} (${columns.join(', ')}) VALUES ${placeholders}`;
    const params = data.flat();

    return await this.query(sql, params);
  }

  /**
   * 分页查询
   */
  static async paginate(baseQuery, params = [], page = 1, pageSize = 20, countQuery = null) {
    const offset = (page - 1) * pageSize;
    
    // 获取总数
    const totalCountQuery = countQuery || baseQuery.replace(/SELECT .+ FROM/, 'SELECT COUNT(*) as total FROM');
    const totalResult = await this.query(totalCountQuery, params);
    const total = Array.isArray(totalResult) ? totalResult[0].total : totalResult.total;

    // 获取分页数据
    const paginatedQuery = `${baseQuery} LIMIT ? OFFSET ?`;
    const data = await this.query(paginatedQuery, [...params, pageSize, offset]);

    return {
      data,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
        hasNext: page < Math.ceil(total / pageSize),
        hasPrev: page > 1
      }
    };
  }

  /**
   * 健康检查
   */
  static async ping() {
    try {
      const connection = await this.getConnection();
      await connection.ping();
      connection.release();
      return true;
    } catch (error) {
      logger.error('Database ping failed', {
        error: error.message
      });
      return false;
    }
  }

  /**
   * 获取数据库状态信息
   */
  static async getStatus() {
    try {
      const statusQuery = `
        SHOW STATUS WHERE Variable_name IN (
          'Connections', 'Threads_connected', 'Threads_running',
          'Queries', 'Slow_queries', 'Uptime'
        )
      `;
      const status = await this.query(statusQuery);
      
      const statusObj = {};
      status.forEach(row => {
        statusObj[row.Variable_name] = row.Value;
      });

      return statusObj;
    } catch (error) {
      logger.error('Failed to get database status', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 清理连接池
   */
  static async close() {
    if (pool) {
      await pool.end();
      pool = null;
      logger.info('Database connection pool closed');
    }
  }

  /**
   * 构建WHERE条件（支持租户数据隔离）
   */
  static buildWhereClause(conditions = {}, tenantFilter = null) {
    const where = [];
    const params = [];

    // 添加租户过滤条件
    if (tenantFilter && tenantFilter.tenant_id) {
      where.push('tenant_id = ?');
      params.push(tenantFilter.tenant_id);
    }

    // 添加其他条件
    Object.entries(conditions).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          where.push(`${key} IN (${value.map(() => '?').join(', ')})`);
          params.push(...value);
        } else if (typeof value === 'object' && value.operator && value.value !== undefined) {
          where.push(`${key} ${value.operator} ?`);
          params.push(value.value);
        } else {
          where.push(`${key} = ?`);
          params.push(value);
        }
      }
    });

    return {
      whereClause: where.length > 0 ? `WHERE ${where.join(' AND ')}` : '',
      params
    };
  }

  /**
   * 安全的表名和列名处理（防SQL注入）
   */
  static escapeIdentifier(identifier) {
    return '`' + identifier.replace(/`/g, '``') + '`';
  }
}

/**
 * 数据库中间件
 * 为每个请求提供数据库连接
 */
function databaseMiddleware(req, res, next) {
  req.db = DatabaseConnection;
  next();
}

// 初始化数据库连接池
initializePool().catch(error => {
  logger.error('Failed to initialize database on startup', {
    error: error.message
  });
  process.exit(1);
});

// 优雅关闭处理
process.on('SIGINT', async () => {
  logger.info('Received SIGINT, closing database connections');
  await DatabaseConnection.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, closing database connections');
  await DatabaseConnection.close();
  process.exit(0);
});

module.exports = {
  DatabaseConnection,
  databaseMiddleware,
  initializePool,
  
  // 向后兼容的导出
  getConnection: () => DatabaseConnection.getConnection(),
  query: (sql, params) => DatabaseConnection.query(sql, params),
  transaction: (callback) => DatabaseConnection.transaction(callback),
  ping: () => DatabaseConnection.ping(),
  close: () => DatabaseConnection.close()
};