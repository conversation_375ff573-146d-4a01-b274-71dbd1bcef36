/**
 * 智慧养鹅SaaS平台 - 统一数据库连接配置
 * 支持多租户架构的数据库连接管理
 */

const { Sequelize } = require('sequelize');
const path = require('path');

// 环境变量配置
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

/**
 * 数据库连接配置
 */
const config = {
  development: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    username: process.env.DB_USER || 'saas_admin',
    password: process.env.DB_PASSWORD || 'your_password',
    database: process.env.DB_NAME || 'smart_goose_saas',
    dialect: 'mysql',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    timezone: '+08:00',
    dialectOptions: {
      charset: 'utf8mb4',
      timezone: '+08:00',
    },
    define: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      timestamps: true,
      paranoid: true, // 支持软删除
    },
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  },
  
  production: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    username: process.env.DB_USER || 'saas_admin', 
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME || 'smart_goose_saas',
    dialect: 'mysql',
    logging: false, // 生产环境关闭SQL日志
    timezone: '+08:00',
    dialectOptions: {
      charset: 'utf8mb4',
      timezone: '+08:00',
      ssl: process.env.DB_SSL === 'true' ? {
        rejectUnauthorized: false
      } : false
    },
    define: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      timestamps: true,
      paranoid: true,
    },
    pool: {
      max: 20,
      min: 5,
      acquire: 60000,
      idle: 10000
    }
  }
};

// 获取当前环境配置
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

console.log(`🌍 数据库连接环境: ${env}`);
console.log(`📡 连接地址: ${dbConfig.host}:${dbConfig.port}`);
console.log(`🗄️  数据库名: ${dbConfig.database}`);

/**
 * 创建Sequelize实例
 */
let sequelize;

try {
  // 检查mysql2包是否可用
  try {
    require('mysql2');
  } catch (mysql2Error) {
    throw new Error('Please install mysql2 package manually');
  }
  
  sequelize = new Sequelize(
    dbConfig.database,
    dbConfig.username, 
    dbConfig.password,
    {
      host: dbConfig.host,
      port: dbConfig.port,
      dialect: dbConfig.dialect,
      logging: dbConfig.logging,
      timezone: dbConfig.timezone,
      dialectOptions: dbConfig.dialectOptions,
      define: dbConfig.define,
      pool: dbConfig.pool,
      
      // 查询配置
      query: {
        raw: false, // 返回模型实例而不是普通对象
      },
      
      // 错误处理
      retry: {
        max: 3,
        timeout: 10000,
        match: [
          /ETIMEDOUT/,
          /EHOSTUNREACH/,
          /ECONNRESET/,
          /ECONNREFUSED/,
          /ENOTFOUND/,
          /ER_LOCK_WAIT_TIMEOUT/,
          /ER_LOCK_DEADLOCK/,
        ]
      }
    }
  );
  
  console.log('✅ Sequelize实例创建成功');
} catch (error) {
  console.error('❌ SAAS平台数据库连接失败:', error.message);
  
  // 检查常见问题
  if (error.message.includes('mysql2')) {
    console.error('🔧 解决方案: npm install mysql2');
  }
  
  if (error.message.includes('ECONNREFUSED')) {
    console.error('🔧 请检查MySQL服务是否启动');
    console.error('🔧 检查数据库连接配置是否正确');
  }
  
  process.exit(1);
}

/**
 * 测试数据库连接
 */
async function testConnection() {
  try {
    // 如果sequelize实例创建失败，直接返回false
    if (!sequelize) {
      console.log('⚠️ 数据库连接实例未创建，跳过连接测试');
      return false;
    }
    
    await sequelize.authenticate();
    console.log('✅ 数据库连接测试成功');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接测试失败:', error.message);
    return false;
  }
}

/**
 * 数据库健康检查
 */
async function healthCheck() {
  try {
    const startTime = Date.now();
    await sequelize.query('SELECT 1 as health');
    const responseTime = Date.now() - startTime;
    
    console.log(`💓 数据库健康检查通过 (${responseTime}ms)`);
    return { healthy: true, responseTime };
  } catch (error) {
    console.error('❌ 数据库健康检查失败:', error.message);
    return { healthy: false, error: error.message };
  }
}

/**
 * 优雅关闭数据库连接
 */
async function closeConnection() {
  try {
    await sequelize.close();
    console.log('✅ 数据库连接已安全关闭');
  } catch (error) {
    console.error('❌ 关闭数据库连接时出错:', error.message);
  }
}

// 进程退出时自动关闭连接
process.on('SIGINT', closeConnection);
process.on('SIGTERM', closeConnection);

module.exports = {
  sequelize,
  Sequelize,
  testConnection,
  healthCheck,
  closeConnection,
  config: dbConfig
};