const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ProductionRecord = sequelize.define(
  'ProductionRecord',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id'
    },
    flockId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'flock_id',
      comment: '鹅群ID'
    },
    eggCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'egg_count'
    },
    feedConsumption: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
      field: 'feed_consumption'
    },
    temperature: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      field: 'temperature'
    },
    humidity: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      field: 'humidity'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'notes'
    },
    recordedDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      field: 'recorded_date'
    },
    productionDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'production_date',
      comment: '生产日期'
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
      comment: '产量'
    }
  },
  {
    tableName: 'production_records',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
);

module.exports = ProductionRecord;
