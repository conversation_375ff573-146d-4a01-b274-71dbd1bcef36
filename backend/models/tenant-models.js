/**
 * 多租户扩展模型
 * Multi-Tenant Extension Models
 * 
 * 为智慧养鹅全栈系统添加多租户支持
 */

const { DataTypes } = require('sequelize');

/**
 * 租户模型扩展类
 * 包含所有租户相关的数据模型定义
 */
class TenantModelExtension {
  constructor(sequelize) {
    this.sequelize = sequelize;
    this.models = {};
  }

  /**
   * 初始化所有租户相关模型
   */
  initializeTenantModels() {
    this.defineTenantModel();
    this.defineTenantConfigModel();
    this.defineTenantUserRoleModel();
    this.defineTenantUserRoleAssignmentModel();
    this.defineTenantStorageStatsModel();
    this.defineTenantApiStatsModel();
    
    // 建立租户模型关联关系
    this.defineTenantAssociations();
    
    return this.models;
  }

  /**
   * 租户主表模型
   */
  defineTenantModel() {
    this.models.Tenant = this.sequelize.define('Tenant', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '租户ID'
      },
      tenant_code: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        comment: '租户代码'
      },
      company_name: {
        type: DataTypes.STRING(200),
        allowNull: false,
        comment: '公司名称'
      },
      contact_name: {
        type: DataTypes.STRING(100),
        comment: '联系人姓名'
      },
      contact_phone: {
        type: DataTypes.STRING(20),
        comment: '联系电话'
      },
      contact_email: {
        type: DataTypes.STRING(100),
        comment: '联系邮箱'
      },
      address: {
        type: DataTypes.TEXT,
        comment: '地址'
      },
      subscription_plan: {
        type: DataTypes.ENUM('trial', 'basic', 'standard', 'premium', 'enterprise'),
        defaultValue: 'trial',
        comment: '订阅计划'
      },
      scale: {
        type: DataTypes.ENUM('small', 'medium', 'large', 'enterprise'),
        defaultValue: 'small',
        comment: '企业规模'
      },
      status: {
        type: DataTypes.ENUM('active', 'inactive', 'suspended', 'trial', 'pending'),
        defaultValue: 'trial',
        comment: '状态'
      },
      trial_start_date: {
        type: DataTypes.DATEONLY,
        comment: '试用开始日期'
      },
      trial_end_date: {
        type: DataTypes.DATEONLY,
        comment: '试用结束日期'
      },
      subscription_start_date: {
        type: DataTypes.DATEONLY,
        comment: '订阅开始日期'
      },
      subscription_end_date: {
        type: DataTypes.DATEONLY,
        comment: '订阅结束日期'
      },
      max_users: {
        type: DataTypes.INTEGER,
        defaultValue: 10,
        comment: '最大用户数'
      },
      max_storage_gb: {
        type: DataTypes.INTEGER,
        defaultValue: 5,
        comment: '最大存储空间(GB)'
      },
      max_api_calls_per_month: {
        type: DataTypes.INTEGER,
        defaultValue: 10000,
        comment: '每月API调用限制'
      },
      last_active_at: {
        type: DataTypes.DATE,
        comment: '最后活跃时间'
      }
    }, {
      tableName: 'tenants',
      paranoid: false,
      timestamps: true,
      underscored: true,
      comment: '租户表',
      indexes: [
        { fields: ['tenant_code'], unique: true },
        { fields: ['status'] },
        { fields: ['subscription_plan'] },
        { fields: ['last_active_at'] }
      ]
    });
  }

  /**
   * 租户配置表模型
   */
  defineTenantConfigModel() {
    this.models.TenantConfig = this.sequelize.define('TenantConfig', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '配置ID'
      },
      tenant_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '租户ID'
      },
      config_key: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '配置键'
      },
      config_value: {
        type: DataTypes.TEXT,
        comment: '配置值'
      },
      data_type: {
        type: DataTypes.ENUM('string', 'number', 'boolean', 'json', 'date'),
        defaultValue: 'string',
        comment: '数据类型'
      },
      description: {
        type: DataTypes.TEXT,
        comment: '配置描述'
      },
      is_system: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: '是否系统配置'
      }
    }, {
      tableName: 'tenant_configs',
      paranoid: false,
      timestamps: true,
      underscored: true,
      comment: '租户配置表',
      indexes: [
        { fields: ['tenant_id'] },
        { fields: ['config_key'] },
        { fields: ['tenant_id', 'config_key'], unique: true }
      ]
    });
  }

  /**
   * 租户用户角色表模型
   */
  defineTenantUserRoleModel() {
    this.models.TenantUserRole = this.sequelize.define('TenantUserRole', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '角色ID'
      },
      tenant_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '租户ID'
      },
      role_name: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '角色名称'
      },
      role_code: {
        type: DataTypes.STRING(30),
        allowNull: false,
        comment: '角色代码'
      },
      description: {
        type: DataTypes.TEXT,
        comment: '角色描述'
      },
      permissions: {
        type: DataTypes.JSON,
        comment: '权限列表'
      },
      is_default: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: '是否默认角色'
      },
      is_system: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: '是否系统角色'
      }
    }, {
      tableName: 'tenant_user_roles',
      paranoid: false,
      timestamps: true,
      underscored: true,
      comment: '租户用户角色表',
      indexes: [
        { fields: ['tenant_id'] },
        { fields: ['role_code'] },
        { fields: ['tenant_id', 'role_code'], unique: true }
      ]
    });
  }

  /**
   * 租户用户角色关联表模型
   */
  defineTenantUserRoleAssignmentModel() {
    this.models.TenantUserRoleAssignment = this.sequelize.define('TenantUserRoleAssignment', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '关联ID'
      },
      tenant_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '租户ID'
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '用户ID'
      },
      role_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '角色ID'
      },
      assigned_by: {
        type: DataTypes.INTEGER,
        comment: '分配人ID'
      },
      assigned_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: '分配时间'
      },
      expires_at: {
        type: DataTypes.DATE,
        comment: '过期时间'
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
        comment: '是否激活'
      }
    }, {
      tableName: 'tenant_user_role_assignments',
      paranoid: false,
      timestamps: false,
      underscored: true,
      comment: '租户用户角色关联表',
      indexes: [
        { fields: ['tenant_id'] },
        { fields: ['user_id'] },
        { fields: ['role_id'] },
        { fields: ['tenant_id', 'user_id', 'role_id'], unique: true }
      ]
    });
  }

  /**
   * 租户存储统计表模型
   */
  defineTenantStorageStatsModel() {
    this.models.TenantStorageStats = this.sequelize.define('TenantStorageStats', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '统计ID'
      },
      tenant_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '租户ID'
      },
      storage_type: {
        type: DataTypes.ENUM('file', 'image', 'document', 'backup', 'other'),
        allowNull: false,
        comment: '存储类型'
      },
      used_bytes: {
        type: DataTypes.BIGINT,
        defaultValue: 0,
        comment: '使用字节数'
      },
      file_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '文件数量'
      },
      stats_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        comment: '统计日期'
      }
    }, {
      tableName: 'tenant_storage_stats',
      paranoid: false,
      timestamps: true,
      underscored: true,
      comment: '租户存储统计表',
      indexes: [
        { fields: ['tenant_id'] },
        { fields: ['storage_type'] },
        { fields: ['stats_date'] },
        { fields: ['tenant_id', 'storage_type', 'stats_date'], unique: true }
      ]
    });
  }

  /**
   * 租户API使用统计表模型
   */
  defineTenantApiStatsModel() {
    this.models.TenantApiStats = this.sequelize.define('TenantApiStats', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '统计ID'
      },
      tenant_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '租户ID'
      },
      api_path: {
        type: DataTypes.STRING(200),
        allowNull: false,
        comment: 'API路径'
      },
      method: {
        type: DataTypes.ENUM('GET', 'POST', 'PUT', 'DELETE', 'PATCH'),
        allowNull: false,
        comment: 'HTTP方法'
      },
      request_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '请求次数'
      },
      success_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '成功次数'
      },
      error_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '错误次数'
      },
      avg_response_time_ms: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '平均响应时间(毫秒)'
      },
      stats_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        comment: '统计日期'
      },
      stats_hour: {
        type: DataTypes.TINYINT,
        allowNull: false,
        comment: '统计小时(0-23)'
      }
    }, {
      tableName: 'tenant_api_stats',
      paranoid: false,
      timestamps: true,
      underscored: true,
      comment: '租户API统计表',
      indexes: [
        { fields: ['tenant_id'] },
        { fields: ['api_path'] },
        { fields: ['stats_date'] },
        { fields: ['tenant_id', 'api_path', 'method', 'stats_date', 'stats_hour'], unique: true }
      ]
    });
  }

  /**
   * 定义租户相关模型关联关系
   */
  defineTenantAssociations() {
    const { 
      Tenant, 
      TenantConfig, 
      TenantUserRole, 
      TenantUserRoleAssignment, 
      TenantStorageStats, 
      TenantApiStats 
    } = this.models;

    // 租户与配置关联
    Tenant.hasMany(TenantConfig, { foreignKey: 'tenant_id', as: 'configs' });
    TenantConfig.belongsTo(Tenant, { foreignKey: 'tenant_id', as: 'tenant' });

    // 租户与角色关联
    Tenant.hasMany(TenantUserRole, { foreignKey: 'tenant_id', as: 'roles' });
    TenantUserRole.belongsTo(Tenant, { foreignKey: 'tenant_id', as: 'tenant' });

    // 租户与角色分配关联
    Tenant.hasMany(TenantUserRoleAssignment, { foreignKey: 'tenant_id', as: 'roleAssignments' });
    TenantUserRoleAssignment.belongsTo(Tenant, { foreignKey: 'tenant_id', as: 'tenant' });

    // 角色与角色分配关联
    TenantUserRole.hasMany(TenantUserRoleAssignment, { foreignKey: 'role_id', as: 'assignments' });
    TenantUserRoleAssignment.belongsTo(TenantUserRole, { foreignKey: 'role_id', as: 'role' });

    // 租户与存储统计关联
    Tenant.hasMany(TenantStorageStats, { foreignKey: 'tenant_id', as: 'storageStats' });
    TenantStorageStats.belongsTo(Tenant, { foreignKey: 'tenant_id', as: 'tenant' });

    // 租户与API统计关联
    Tenant.hasMany(TenantApiStats, { foreignKey: 'tenant_id', as: 'apiStats' });
    TenantApiStats.belongsTo(Tenant, { foreignKey: 'tenant_id', as: 'tenant' });
  }

  /**
   * 获取所有租户模型
   */
  getTenantModels() {
    return this.models;
  }
}

/**
 * 租户中间件工厂
 * 为API路由添加租户隔离支持
 */
class TenantMiddleware {
  /**
   * 创建租户上下文中间件
   */
  static createTenantContext() {
    return (req, res, next) => {
      // 从请求头或用户信息中获取租户ID
      const tenantId = req.headers['x-tenant-id'] || 
                      req.user?.tenant_id || 
                      req.session?.tenant_id;
      
      if (tenantId) {
        req.tenantId = parseInt(tenantId);
        req.tenantContext = {
          tenantId: req.tenantId,
          scoped: true
        };
      } else {
        // 如果没有租户上下文，设为平台级访问
        req.tenantContext = {
          tenantId: null,
          scoped: false
        };
      }
      
      next();
    };
  }

  /**
   * 创建租户权限检查中间件
   */
  static requireTenant() {
    return (req, res, next) => {
      if (!req.tenantId) {
        return res.status(403).json({
          success: false,
          error: 'Tenant context required'
        });
      }
      next();
    };
  }

  /**
   * 为Sequelize查询添加租户范围限制
   */
  static addTenantScope(query, tenantId) {
    if (tenantId) {
      return {
        ...query,
        where: {
          ...query.where,
          tenant_id: tenantId
        }
      };
    }
    return query;
  }
}

module.exports = {
  TenantModelExtension,
  TenantMiddleware
};