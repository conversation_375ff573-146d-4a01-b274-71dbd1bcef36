// 统一的库存管理模型 - 合并Material和Inventory功能
// 包含物料管理和库存管理的所有功能

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const UnifiedInventory = sequelize.define(
  'UnifiedInventory',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },

    // ===== 基础信息 =====
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '创建用户ID',
      validate: {
        isInt: {
          msg: '用户ID必须是整数'
        },
        min: {
          args: 1,
          msg: '用户ID必须大于0'
        }
      }
    },

    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '物料名称',
      validate: {
        notEmpty: {
          msg: '物料名称不能为空'
        },
        len: {
          args: [2, 100],
          msg: '物料名称长度必须在2-100字符之间'
        },
        isValidName(value) {
          if (!/^[\u4e00-\u9fa5a-zA-Z0-9\s\-_()（）]+$/.test(value)) {
            throw new Error('物料名称只能包含中文、英文、数字、空格和常用符号');
          }
        }
      }
    },
    category: {
      type: DataTypes.ENUM(
        'feed',
        'medicine',
        'equipment',
        'materials',
        'other'
      ),
      allowNull: false,
      comment:
        '物料类别: feed-饲料, medicine-药品, equipment-设备, materials-物料, other-其他'
    },
    specification: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '规格型号'
    },
    unit: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: '个',
      comment: '计量单位'
    },

    // ===== 库存管理 =====
    currentStock: {
      type: DataTypes.DECIMAL(12, 3),
      allowNull: false,
      defaultValue: 0,
      field: 'current_stock',
      comment: '当前库存数量',
      validate: {
        isDecimal: {
          msg: '当前库存必须是有效的数字'
        },
        min: {
          args: 0,
          msg: '当前库存不能为负数'
        },
        max: {
          args: 999999999,
          msg: '当前库存不能超过999,999,999'
        }
      }
    },
    minStock: {
      type: DataTypes.DECIMAL(12, 3),
      allowNull: true,
      defaultValue: 0,
      field: 'min_stock',
      comment: '最低库存警戒线',
      validate: {
        isDecimal: {
          msg: '最低库存必须是有效的数字'
        },
        min: {
          args: 0,
          msg: '最低库存不能为负数'
        },
        isValidMinStock(value) {
          if (
            value !== null &&
            this.maxStock !== null &&
            value > this.maxStock
          ) {
            throw new Error('最低库存不能大于最高库存');
          }
        }
      }
    },
    maxStock: {
      type: DataTypes.DECIMAL(12, 3),
      allowNull: true,
      field: 'max_stock',
      comment: '最高库存限制',
      validate: {
        isDecimal: {
          msg: '最高库存必须是有效的数字'
        },
        min: {
          args: 0,
          msg: '最高库存不能为负数'
        },
        isValidMaxStock(value) {
          if (
            value !== null &&
            this.minStock !== null &&
            value < this.minStock
          ) {
            throw new Error('最高库存不能小于最低库存');
          }
        }
      }
    },

    // ===== 价格信息 =====
    unitPrice: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      field: 'unit_price',
      comment: '单价',
      validate: {
        isDecimal: {
          msg: '单价必须是有效的数字'
        },
        min: {
          args: 0,
          msg: '单价不能为负数'
        },
        max: {
          args: 99999999.99,
          msg: '单价不能超过99,999,999.99'
        }
      }
    },
    totalValue: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      field: 'total_value',
      comment: '总价值（自动计算）',
      validate: {
        isDecimal: {
          msg: '总价值必须是有效的数字'
        },
        min: {
          args: 0,
          msg: '总价值不能为负数'
        }
      }
    },

    // ===== 供应商信息 =====
    supplier: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '供应商名称',
      validate: {
        len: {
          args: [0, 100],
          msg: '供应商名称长度不能超过100字符'
        },
        isValidSupplier(value) {
          if (value && !/^[\u4e00-\u9fa5a-zA-Z0-9\s\-_.()（）]+$/.test(value)) {
            throw new Error(
              '供应商名称只能包含中文、英文、数字、空格和常用符号'
            );
          }
        }
      }
    },
    supplierContact: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'supplier_contact',
      comment: '供应商联系方式',
      validate: {
        len: {
          args: [0, 100],
          msg: '供应商联系方式长度不能超过100字符'
        },
        isValidContact(value) {
          if (value) {
            // 支持手机号、固话、邮箱等格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            const telRegex = /^0\d{2,3}-?\d{7,8}$/;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            if (
              !phoneRegex.test(value) &&
              !telRegex.test(value) &&
              !emailRegex.test(value)
            ) {
              throw new Error('请输入有效的手机号、电话号码或邮箱地址');
            }
          }
        }
      }
    },

    // ===== 日期信息 =====
    purchaseDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'purchase_date',
      comment: '采购日期',
      validate: {
        isDate: {
          msg: '采购日期必须是有效的日期格式'
        },
        isValidPurchaseDate(value) {
          if (value) {
            const purchaseDate = new Date(value);
            const now = new Date();
            const oneYearAgo = new Date();
            oneYearAgo.setFullYear(now.getFullYear() - 5); // 允许5年前的采购日期

            if (purchaseDate > now) {
              throw new Error('采购日期不能是未来日期');
            }
            if (purchaseDate < oneYearAgo) {
              throw new Error('采购日期不能超过5年前');
            }
          }
        }
      }
    },
    expiryDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'expiry_date',
      comment: '过期日期',
      validate: {
        isDate: {
          msg: '过期日期必须是有效的日期格式'
        },
        isValidExpiryDate(value) {
          if (value && this.purchaseDate) {
            const expiryDate = new Date(value);
            const purchaseDate = new Date(this.purchaseDate);

            if (expiryDate <= purchaseDate) {
              throw new Error('过期日期必须晚于采购日期');
            }
          }
        }
      }
    },
    lastUpdateDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'last_update_date',
      comment: '最后盘点日期',
      validate: {
        isDate: {
          msg: '最后盘点日期必须是有效的日期格式'
        },
        isValidUpdateDate(value) {
          if (value) {
            const updateDate = new Date(value);
            const now = new Date();

            if (updateDate > now) {
              throw new Error('盘点日期不能是未来日期');
            }
          }
        }
      }
    },

    // ===== 状态和位置 =====
    status: {
      type: DataTypes.ENUM(
        'normal',
        'low_stock',
        'out_of_stock',
        'expired',
        'warning',
        'inactive'
      ),
      allowNull: false,
      defaultValue: 'normal',
      comment:
        '状态: normal-正常, low_stock-库存不足, out_of_stock-缺货, expired-过期, warning-预警, inactive-停用'
    },
    location: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '存放位置',
      validate: {
        len: {
          args: [0, 100],
          msg: '存放位置长度不能超过100字符'
        },
        isValidLocation(value) {
          if (value && !/^[\u4e00-\u9fa5a-zA-Z0-9\s\-_.()（）]+$/.test(value)) {
            throw new Error('存放位置只能包含中文、英文、数字、空格和常用符号');
          }
        }
      }
    },

    // ===== 其他信息 =====
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '备注描述',
      validate: {
        len: {
          args: [0, 1000],
          msg: '备注描述长度不能超过1000字符'
        }
      }
    },
    batchNumber: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'batch_number',
      comment: '批次号',
      validate: {
        len: {
          args: [0, 50],
          msg: '批次号长度不能超过50字符'
        },
        isValidBatchNumber(value) {
          if (value && !/^[A-Z0-9\-_]+$/.test(value)) {
            throw new Error('批次号只能包含大写字母、数字、横线和下划线');
          }
        }
      }
    },

    // ===== 审计信息 =====
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'created_by',
      comment: '创建者ID'
    },
    updatedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'updated_by',
      comment: '更新者ID'
    }
  },
  {
    tableName: 'unified_inventory',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      // 基础索引
      { fields: ['user_id'] },
      { fields: ['category'] },
      { fields: ['status'] },
      { fields: ['name'] },

      // 日期索引
      { fields: ['expiry_date'] },
      { fields: ['purchase_date'] },

      // 库存相关索引
      { fields: ['current_stock'] },
      { fields: ['user_id', 'category'] },
      { fields: ['user_id', 'status'] },

      // 预警索引（库存不足检查）
      { fields: ['user_id', 'current_stock', 'min_stock'] },

      // 过期检查索引
      { fields: ['status', 'expiry_date'] },

      // 搜索索引
      { fields: ['name', 'category'] }
    ],

    // 模型级别的钩子函数
    hooks: {
      beforeSave: async (instance) => {
        // 自动计算总价值
        if (instance.unitPrice && instance.currentStock) {
          instance.totalValue =
            parseFloat(instance.unitPrice) * parseFloat(instance.currentStock);
        }

        // 自动更新状态
        if (instance.currentStock !== undefined) {
          if (instance.currentStock <= 0) {
            instance.status = 'out_of_stock';
          } else if (
            instance.minStock &&
            instance.currentStock <= instance.minStock
          ) {
            instance.status = 'low_stock';
          } else if (
            instance.status === 'out_of_stock' ||
            instance.status === 'low_stock'
          ) {
            instance.status = 'normal';
          }
        }

        // 检查过期状态
        if (instance.expiryDate && new Date(instance.expiryDate) < new Date()) {
          instance.status = 'expired';
        }
      }
    }
  }
);

// 静态方法
UnifiedInventory.getCategoryOptions = function () {
  return [
    { value: 'feed', label: '饲料' },
    { value: 'medicine', label: '药品' },
    { value: 'equipment', label: '设备' },
    { value: 'materials', label: '物料' },
    { value: 'other', label: '其他' }
  ];
};

UnifiedInventory.getStatusOptions = function () {
  return [
    { value: 'normal', label: '正常' },
    { value: 'low_stock', label: '库存不足' },
    { value: 'out_of_stock', label: '缺货' },
    { value: 'expired', label: '过期' },
    { value: 'warning', label: '预警' },
    { value: 'inactive', label: '停用' }
  ];
};

// 实例方法
UnifiedInventory.prototype.isLowStock = function () {
  return this.minStock && this.currentStock <= this.minStock;
};

UnifiedInventory.prototype.isExpired = function () {
  return this.expiryDate && new Date(this.expiryDate) < new Date();
};

UnifiedInventory.prototype.calculateTotalValue = function () {
  return this.unitPrice && this.currentStock
    ? parseFloat(this.unitPrice) * parseFloat(this.currentStock)
    : 0;
};

module.exports = UnifiedInventory;
