const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Flock = sequelize.define(
  'Flock',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '用户ID'
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '鹅群名称',
      validate: {
        notEmpty: {
          msg: '鹅群名称不能为空'
        },
        len: {
          args: [2, 100],
          msg: '鹅群名称长度必须在2-100字符之间'
        },
        isValidFlockName(value) {
          if (!/^[\u4e00-\u9fa5a-zA-Z0-9\s\-_()（）]+$/.test(value)) {
            throw new Error('鹅群名称只能包含中文、英文、数字、空格和常用符号');
          }
        }
      }
    },
    batchNumber: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      field: 'batch_number',
      comment: '批次号',
      validate: {
        notEmpty: {
          msg: '批次号不能为空'
        },
        len: {
          args: [5, 50],
          msg: '批次号长度必须在5-50字符之间'
        },
        isValidBatchNumber(value) {
          if (!/^FLOCK-\d{13}$/.test(value)) {
            throw new Error(
              '批次号格式必须为：FLOCK-时间戳（如FLOCK-1234567890123）'
            );
          }
        }
      }
    },
    breed: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '品种'
    },
    totalCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      field: 'total_count',
      comment: '总数量',
      validate: {
        isInt: {
          msg: '总数量必须是整数'
        },
        min: {
          args: 0,
          msg: '总数量不能为负数'
        },
        max: {
          args: 100000,
          msg: '总数量不能超过100,000'
        },
        isValidTotalCount(value) {
          if (this.currentCount && value < this.currentCount) {
            throw new Error('总数量不能小于当前数量');
          }
        }
      }
    },
    currentCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      field: 'current_count',
      comment: '当前数量',
      validate: {
        isInt: {
          msg: '当前数量必须是整数'
        },
        min: {
          args: 0,
          msg: '当前数量不能为负数'
        },
        isValidCurrentCount(value) {
          if (this.totalCount && value > this.totalCount) {
            throw new Error('当前数量不能大于总数量');
          }
          if (
            this.maleCount &&
            this.femaleCount &&
            value !== this.maleCount + this.femaleCount
          ) {
            throw new Error('当前数量必须等于雄性数量加雌性数量');
          }
        }
      }
    },
    maleCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      field: 'male_count',
      comment: '雄性数量',
      validate: {
        isInt: {
          msg: '雄性数量必须是整数'
        },
        min: {
          args: 0,
          msg: '雄性数量不能为负数'
        },
        isValidMaleCount(value) {
          if (
            this.currentCount &&
            this.femaleCount &&
            value + this.femaleCount > this.currentCount
          ) {
            throw new Error('雄性数量加雌性数量不能大于当前总数');
          }
        }
      }
    },
    femaleCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      field: 'female_count',
      comment: '雌性数量',
      validate: {
        isInt: {
          msg: '雌性数量必须是整数'
        },
        min: {
          args: 0,
          msg: '雌性数量不能为负数'
        },
        isValidFemaleCount(value) {
          if (
            this.currentCount &&
            this.maleCount &&
            this.maleCount + value > this.currentCount
          ) {
            throw new Error('雄性数量加雌性数量不能大于当前总数');
          }
        }
      }
    },
    ageGroup: {
      type: DataTypes.ENUM('young', 'adult', 'breeding', 'retired'),
      allowNull: false,
      defaultValue: 'young',
      field: 'age_group',
      comment: '年龄组：幼鹅、成鹅、种鹅、淘汰'
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'sold', 'deceased'),
      allowNull: false,
      defaultValue: 'active',
      comment: '状态：活跃、非活跃、已售出、已死亡'
    },
    establishedDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'established_date',
      comment: '建群日期'
    },
    location: {
      type: DataTypes.STRING(200),
      allowNull: true,
      comment: '饲养位置'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '描述备注'
    },
    // 健康相关字段
    lastHealthCheckDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'last_health_check_date',
      comment: '最后健康检查日期'
    },
    healthStatus: {
      type: DataTypes.ENUM('excellent', 'good', 'fair', 'poor'),
      allowNull: true,
      defaultValue: 'good',
      field: 'health_status',
      comment: '健康状态'
    },
    // 生产相关字段
    avgDailyEggProduction: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 0,
      field: 'avg_daily_egg_production',
      comment: '平均日产蛋量'
    },
    avgEggWeight: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 0,
      field: 'avg_egg_weight',
      comment: '平均蛋重(克)'
    },
    // 饲料相关字段
    avgDailyFeedConsumption: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      defaultValue: 0,
      field: 'avg_daily_feed_consumption',
      comment: '平均日饲料消耗量(克)'
    },
    feedConversionRatio: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 0,
      field: 'feed_conversion_ratio',
      comment: '料蛋比'
    },
    // 环境相关字段
    avgTemperature: {
      type: DataTypes.DECIMAL(4, 1),
      allowNull: true,
      field: 'avg_temperature',
      comment: '平均温度(°C)'
    },
    avgHumidity: {
      type: DataTypes.DECIMAL(4, 1),
      allowNull: true,
      field: 'avg_humidity',
      comment: '平均湿度(%)'
    },
    // 经济相关字段
    totalCost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0,
      field: 'total_cost',
      comment: '总成本'
    },
    totalRevenue: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0,
      field: 'total_revenue',
      comment: '总收入'
    },
    // 额外数据库字段
    flockId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'flock_id',
      comment: '鹅群ID（兼容字段）'
    }
    // 时间戳 - 由 Sequelize 自动管理
  },
  {
    tableName: 'flocks',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['userId']
      },
      {
        fields: ['batchNumber']
      },
      {
        fields: ['breed']
      },
      {
        fields: ['status']
      },
      {
        fields: ['ageGroup']
      },
      {
        fields: ['establishedDate']
      }
    ],
    hooks: {
      beforeUpdate: (flock, options) => {
        // 更新时间戳
        flock.updatedAt = new Date();
      },
      beforeCreate: (flock, options) => {
        // 如果没有提供批次号，自动生成
        if (!flock.batchNumber) {
          flock.batchNumber = `FLOCK-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        }
      }
    }
  }
);

// 定义关联关系
Flock.associate = (models) => {
  // 鹅群属于用户
  Flock.belongsTo(models.User, {
    foreignKey: 'userId',
    as: 'user'
  });

  // 鹅群有多个生产记录
  if (models.ProductionRecord) {
    Flock.hasMany(models.ProductionRecord, {
      foreignKey: 'flockId',
      as: 'productionRecords'
    });
  }

  // 鹅群有多个健康记录
  if (models.HealthRecord) {
    Flock.hasMany(models.HealthRecord, {
      foreignKey: 'flockId',
      as: 'healthRecords'
    });
  }

  // 鹅群有多个饲料记录
  if (models.FeedRecord) {
    Flock.hasMany(models.FeedRecord, {
      foreignKey: 'flockId',
      as: 'feedRecords'
    });
  }
};

// 实例方法
Flock.prototype.calculateAge = function () {
  const now = new Date();
  const established = new Date(this.establishedDate);
  const diffTime = Math.abs(now - established);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

Flock.prototype.calculateSurvivalRate = function () {
  return ((this.currentCount / this.totalCount) * 100).toFixed(2);
};

Flock.prototype.calculateMortality = function () {
  return this.totalCount - this.currentCount;
};

Flock.prototype.calculateMortalityRate = function () {
  return (
    ((this.totalCount - this.currentCount) / this.totalCount) *
    100
  ).toFixed(2);
};

// 类方法
Flock.getBreedOptions = function () {
  return [
    { value: 'white_goose', label: '白鹅' },
    { value: 'grey_goose', label: '灰鹅' },
    { value: 'embden', label: '埃姆登鹅' },
    { value: 'toulouse', label: '图卢兹鹅' },
    { value: 'chinese', label: '中国鹅' },
    { value: 'african', label: '非洲鹅' },
    { value: 'pilgrim', label: '朝圣者鹅' },
    { value: 'sebastopol', label: '塞瓦斯托波尔鹅' }
  ];
};

Flock.getAgeGroupOptions = function () {
  return [
    { value: 'young', label: '幼鹅(0-8周)' },
    { value: 'adult', label: '成鹅(8周-6月)' },
    { value: 'breeding', label: '种鹅(6月以上)' },
    { value: 'retired', label: '淘汰鹅' }
  ];
};

Flock.getStatusOptions = function () {
  return [
    { value: 'active', label: '活跃' },
    { value: 'inactive', label: '非活跃' },
    { value: 'sold', label: '已售出' },
    { value: 'deceased', label: '已死亡' }
  ];
};

module.exports = Flock;
