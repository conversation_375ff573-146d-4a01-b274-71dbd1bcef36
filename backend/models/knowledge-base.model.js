// backend/models/knowledge-base.model.js
// 知识库数据模型

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const KnowledgeBase = sequelize.define(
  'KnowledgeBase',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'user_id',
      comment: '创建者用户ID'
    },
    flockId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'flock_id',
      comment: '关联鹅群ID'
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: '知识标题'
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '知识内容'
    },
    category: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '知识分类 (disease, breeding, feeding, environment, management)'
    },
    tags: {
      type: DataTypes.JSON,
      comment: '标签数组'
    },
    keywords: {
      type: DataTypes.STRING(500),
      comment: '关键词（用于搜索）'
    },
    difficulty: {
      type: DataTypes.STRING(20),
      defaultValue: 'beginner',
      comment: '难度等级 (beginner, intermediate, advanced)'
    },
    readTime: {
      type: DataTypes.INTEGER,
      field: 'read_time',
      comment: '预计阅读时间（分钟）'
    },
    viewCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'view_count',
      comment: '查看次数'
    },
    likeCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'like_count',
      comment: '点赞次数'
    },
    images: {
      type: DataTypes.JSON,
      comment: '相关图片'
    },
    videos: {
      type: DataTypes.JSON,
      comment: '相关视频'
    },
    attachments: {
      type: DataTypes.JSON,
      comment: '附件'
    },
    relatedArticles: {
      type: DataTypes.JSON,
      field: 'related_articles',
      comment: '相关文章ID数组'
    },
    status: {
      type: DataTypes.STRING(20),
      defaultValue: 'published',
      comment: '状态 (draft, published, archived)'
    },
    isRecommended: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_recommended',
      comment: '是否推荐'
    },
    publishTime: {
      type: DataTypes.DATE,
      field: 'publish_time',
      comment: '发布时间'
    },
    lastReviewTime: {
      type: DataTypes.DATE,
      field: 'last_review_time',
      comment: '最后审核时间'
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'created_by',
      comment: '创建者ID'
    },
    updatedBy: {
      type: DataTypes.INTEGER,
      field: 'updated_by',
      comment: '更新者ID'
    },
    reviewedBy: {
      type: DataTypes.INTEGER,
      field: 'reviewed_by',
      comment: '审核者ID'
    }
  },
  {
    tableName: 'knowledge_base',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    paranoid: true, // 软删除
    indexes: [
      {
        fields: ['category']
      },
      {
        fields: ['status']
      },
      {
        fields: ['isRecommended']
      },
      {
        fields: ['publishTime']
      },
      {
        name: 'idx_knowledge_search',
        fields: ['title', 'keywords']
      }
    ]
  }
);

module.exports = KnowledgeBase;
