// backend/models/ai-config.model.js
// AI配置数据模型

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const AIConfig = sequelize.define(
  'AIConfig',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    provider: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: 'AI服务提供商 (zhipu, siliconflow, openai, qianwen)'
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '服务商显示名称'
    },
    apiKey: {
      type: DataTypes.TEXT,
      allowNull: false,
      field: 'api_key',
      comment: 'API密钥（加密存储）'
    },
    baseUrl: {
      type: DataTypes.STRING(255),
      allowNull: false,
      field: 'base_url',
      comment: 'API基础URL'
    },
    models: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: '支持的模型配置'
    },
    maxTokens: {
      type: DataTypes.INTEGER,
      defaultValue: 4096,
      field: 'max_tokens',
      comment: '最大token数'
    },
    temperature: {
      type: DataTypes.DECIMAL(3, 2),
      defaultValue: 0.7,
      comment: '温度参数'
    },
    enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: '是否启用'
    },
    isDefault: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_default',
      comment: '是否为默认服务商'
    },
    priority: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '优先级（数字越大优先级越高）'
    },
    config: {
      type: DataTypes.JSON,
      comment: '其他配置参数'
    },
    createdBy: {
      type: DataTypes.INTEGER,
      field: 'created_by',
      comment: '创建者ID'
    },
    updatedBy: {
      type: DataTypes.INTEGER,
      field: 'updated_by',
      comment: '更新者ID'
    }
  },
  {
    tableName: 'ai_configs',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    paranoid: true, // 软删除
    indexes: [
      {
        fields: ['provider']
      },
      {
        fields: ['enabled']
      },
      {
        fields: ['is_default']
      }
    ]
  }
);

module.exports = AIConfig;
