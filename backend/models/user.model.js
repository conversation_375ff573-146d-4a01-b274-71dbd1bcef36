const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const User = sequelize.define(
  'User',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: {
          msg: '用户名不能为空'
        },
        len: {
          args: [3, 50],
          msg: '用户名长度必须在3-50字符之间'
        },
        isValidUsername(value) {
          if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(value)) {
            throw new Error('用户名只能包含中文、英文、数字和下划线');
          }
          if (/^\d+$/.test(value)) {
            throw new Error('用户名不能是纯数字');
          }
        }
      }
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: '密码不能为空'
        },
        len: {
          args: [6, 255],
          msg: '密码长度必须在6-255字符之间'
        },
        isValidPassword(value) {
          // 检查是否已经是哈希密码（bcrypt格式）
          if (!/^\$2[ayb]\$.{56}$/.test(value)) {
            // 如果不是哈希密码，则验证明文密码强度
            if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
              throw new Error(
                '密码必须包含至少一个小写字母、一个大写字母和一个数字'
              );
            }
          }
        }
      }
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: {
          msg: '邮箱不能为空'
        },
        isEmail: {
          msg: '请输入有效的邮箱地址'
        },
        len: {
          args: [5, 100],
          msg: '邮箱长度必须在5-100字符之间'
        }
      }
    },
    role: {
      type: DataTypes.ENUM(
        'platform_admin', // 平台超级管理员
        'platform_operator', // 平台运营人员
        'owner', // 租户拥有者
        'admin', // 租户管理员
        'manager', // 租户经理
        'staff', // 租户员工
        'viewer' // 只读用户
      ),
      defaultValue: 'staff',
      comment: '用户角色',
      validate: {
        isIn: {
          args: [
            [
              'platform_admin',
              'platform_operator',
              'owner',
              'admin',
              'manager',
              'staff',
              'viewer'
            ]
          ],
          msg: '角色必须是预定义角色之一'
        }
      }
    },
    tenantRole: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'tenant_role',
      comment: '租户内角色（用于灵活的权限控制）'
    },
    permissions: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '用户特殊权限（覆盖角色权限）'
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'suspended', 'pending'),
      defaultValue: 'active',
      comment: '用户状态'
    },
    lastLoginAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'last_login_at',
      comment: '最后登录时间'
    }
  },
  {
    tableName: 'users',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
);

module.exports = User;
