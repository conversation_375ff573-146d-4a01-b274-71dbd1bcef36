// backend/models/announcement.model.js
// 公告管理数据模型

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Announcement = sequelize.define(
  'Announcement',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: '公告标题'
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '公告内容'
    },
    type: {
      type: DataTypes.STRING(50),
      defaultValue: 'general',
      comment: '公告类型 (general, urgent, maintenance, update)'
    },
    priority: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '优先级（数字越大优先级越高）'
    },
    status: {
      type: DataTypes.STRING(20),
      defaultValue: 'draft',
      comment: '状态 (draft, published, archived)'
    },
    publishTime: {
      type: DataTypes.DATE,
      field: 'publish_time',
      comment: '发布时间'
    },
    expireTime: {
      type: DataTypes.DATE,
      field: 'expire_time',
      comment: '过期时间'
    },
    targetUsers: {
      type: DataTypes.JSON,
      field: 'target_users',
      comment: '目标用户（用户ID数组，空表示所有用户）'
    },
    attachments: {
      type: DataTypes.JSON,
      comment: '附件信息'
    },
    readCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'read_count',
      comment: '阅读次数'
    },
    isTop: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_top',
      comment: '是否置顶'
    },
    allowComment: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'allow_comment',
      comment: '是否允许评论'
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'created_by',
      comment: '创建者ID'
    },
    updatedBy: {
      type: DataTypes.INTEGER,
      field: 'updated_by',
      comment: '更新者ID'
    }
  },
  {
    tableName: 'announcements',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    paranoid: true, // 软删除
    indexes: [
      {
        fields: ['status']
      },
      {
        fields: ['type']
      },
      {
        fields: ['publishTime']
      },
      {
        fields: ['isTop', 'priority']
      }
    ]
  }
);

module.exports = Announcement;
