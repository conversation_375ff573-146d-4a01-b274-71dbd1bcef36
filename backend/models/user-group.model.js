const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const UserGroup = sequelize.define(
  'UserGroup',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id',
      comment: '用户ID'
    },
    flockId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'flock_id',
      comment: '鹅群ID（如果是鹅群相关组）'
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '用户组名称'
    },
    groupName: {
      type: DataTypes.STRING(100),
      allowNull: false,
      field: 'group_name',
      comment: '组显示名称'
    },
    description: {
      type: DataTypes.TEXT,
      comment: '用户组描述'
    },
    permissions: {
      type: DataTypes.JSON,
      comment: '权限列表'
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      defaultValue: 'active',
      comment: '状态'
    }
  },
  {
    tableName: 'user_groups',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    comment: '用户组表'
  }
);

module.exports = UserGroup;
