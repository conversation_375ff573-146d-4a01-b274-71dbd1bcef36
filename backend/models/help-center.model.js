const db = require('../config/database');

class HelpCenterModel {
  
  // ======================== 分类管理 ========================
  
  // 获取所有分类
  static async getCategories() {
    const query = `
      SELECT 
        c.*,
        (SELECT COUNT(*) FROM help_articles WHERE category_id = c.id AND is_published = true) as article_count,
        (SELECT COUNT(*) FROM help_faqs WHERE category_id = c.id AND is_published = true) as faq_count
      FROM help_categories c
      WHERE c.is_active = true
      ORDER BY c.sort_order ASC, c.created_at ASC
    `;
    return await db.query(query);
  }

  // 获取单个分类
  static async getCategoryById(id) {
    const query = `
      SELECT 
        c.*,
        (SELECT COUNT(*) FROM help_articles WHERE category_id = c.id AND is_published = true) as article_count,
        (SELECT COUNT(*) FROM help_faqs WHERE category_id = c.id AND is_published = true) as faq_count
      FROM help_categories c
      WHERE c.id = ? AND c.is_active = true
    `;
    const result = await db.query(query, [id]);
    return result[0];
  }

  // 创建分类
  static async createCategory(data) {
    const query = `
      INSERT INTO help_categories (name, description, icon, sort_order, created_by)
      VALUES (?, ?, ?, ?, ?)
    `;
    const result = await db.query(query, [
      data.name, data.description, data.icon, data.sort_order || 0, data.created_by
    ]);
    return result.insertId;
  }

  // 更新分类
  static async updateCategory(id, data) {
    const query = `
      UPDATE help_categories 
      SET name = ?, description = ?, icon = ?, sort_order = ?, updated_by = ?
      WHERE id = ?
    `;
    return await db.query(query, [
      data.name, data.description, data.icon, data.sort_order, data.updated_by, id
    ]);
  }

  // 删除分类
  static async deleteCategory(id) {
    const query = 'UPDATE help_categories SET is_active = false WHERE id = ?';
    return await db.query(query, [id]);
  }

  // ======================== 文章管理 ========================
  
  // 获取文章列表
  static async getArticles(filters = {}) {
    let query = `
      SELECT 
        a.*,
        c.name as category_name,
        c.icon as category_icon
      FROM help_articles a
      LEFT JOIN help_categories c ON a.category_id = c.id
      WHERE a.is_published = true
    `;
    const params = [];

    if (filters.category_id) {
      query += ' AND a.category_id = ?';
      params.push(filters.category_id);
    }

    if (filters.keyword) {
      query += ' AND (MATCH(a.title, a.summary, a.content) AGAINST(? IN NATURAL LANGUAGE MODE))';
      params.push(filters.keyword);
    }

    if (filters.tags) {
      query += ' AND JSON_CONTAINS(a.tags, ?)';
      params.push(JSON.stringify(filters.tags));
    }

    query += ' ORDER BY ';
    
    if (filters.order_by === 'views') {
      query += 'a.view_count DESC';
    } else if (filters.order_by === 'likes') {
      query += 'a.like_count DESC';
    } else {
      query += 'a.sort_order ASC, a.created_at DESC';
    }

    if (filters.limit) {
      query += ' LIMIT ?';
      params.push(parseInt(filters.limit));
    }

    return await db.query(query, params);
  }

  // 获取单篇文章
  static async getArticleById(id) {
    const query = `
      SELECT 
        a.*,
        c.name as category_name,
        c.icon as category_icon
      FROM help_articles a
      LEFT JOIN help_categories c ON a.category_id = c.id
      WHERE a.id = ? AND a.is_published = true
    `;
    const result = await db.query(query, [id]);
    
    if (result.length > 0) {
      // 增加浏览量
      await this.incrementArticleViews(id);
      return result[0];
    }
    return null;
  }

  // 增加文章浏览量
  static async incrementArticleViews(id) {
    const query = 'UPDATE help_articles SET view_count = view_count + 1 WHERE id = ?';
    return await db.query(query, [id]);
  }

  // 创建文章
  static async createArticle(data) {
    const query = `
      INSERT INTO help_articles (
        category_id, title, summary, content, content_type, featured_image,
        tags, meta_keywords, meta_description, sort_order, is_published, 
        is_featured, publish_at, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const result = await db.query(query, [
      data.category_id, data.title, data.summary, data.content, data.content_type || 'text',
      data.featured_image, JSON.stringify(data.tags || []), data.meta_keywords, 
      data.meta_description, data.sort_order || 0, data.is_published || false,
      data.is_featured || false, data.publish_at, data.created_by
    ]);
    return result.insertId;
  }

  // 更新文章
  static async updateArticle(id, data) {
    const query = `
      UPDATE help_articles 
      SET category_id = ?, title = ?, summary = ?, content = ?, content_type = ?,
          featured_image = ?, tags = ?, meta_keywords = ?, meta_description = ?,
          sort_order = ?, is_published = ?, is_featured = ?, publish_at = ?, updated_by = ?
      WHERE id = ?
    `;
    return await db.query(query, [
      data.category_id, data.title, data.summary, data.content, data.content_type,
      data.featured_image, JSON.stringify(data.tags || []), data.meta_keywords,
      data.meta_description, data.sort_order, data.is_published, data.is_featured,
      data.publish_at, data.updated_by, id
    ]);
  }

  // 删除文章
  static async deleteArticle(id) {
    const query = 'DELETE FROM help_articles WHERE id = ?';
    return await db.query(query, [id]);
  }

  // ======================== FAQ管理 ========================
  
  // 获取FAQ列表
  static async getFAQs(filters = {}) {
    let query = `
      SELECT 
        f.*,
        c.name as category_name
      FROM help_faqs f
      LEFT JOIN help_categories c ON f.category_id = c.id
      WHERE f.is_published = true
    `;
    const params = [];

    if (filters.category_id) {
      query += ' AND f.category_id = ?';
      params.push(filters.category_id);
    }

    if (filters.keyword) {
      query += ' AND (MATCH(f.question, f.answer) AGAINST(? IN NATURAL LANGUAGE MODE))';
      params.push(filters.keyword);
    }

    if (filters.featured) {
      query += ' AND f.is_featured = true';
    }

    query += ' ORDER BY f.sort_order ASC, f.helpful_count DESC, f.created_at DESC';

    if (filters.limit) {
      query += ' LIMIT ?';
      params.push(parseInt(filters.limit));
    }

    return await db.query(query, params);
  }

  // 获取单个FAQ
  static async getFAQById(id) {
    const query = `
      SELECT 
        f.*,
        c.name as category_name
      FROM help_faqs f
      LEFT JOIN help_categories c ON f.category_id = c.id
      WHERE f.id = ? AND f.is_published = true
    `;
    const result = await db.query(query, [id]);
    
    if (result.length > 0) {
      // 增加浏览量
      await this.incrementFAQViews(id);
      return result[0];
    }
    return null;
  }

  // 增加FAQ浏览量
  static async incrementFAQViews(id) {
    const query = 'UPDATE help_faqs SET view_count = view_count + 1 WHERE id = ?';
    return await db.query(query, [id]);
  }

  // 创建FAQ
  static async createFAQ(data) {
    const query = `
      INSERT INTO help_faqs (
        category_id, question, answer, tags, sort_order, 
        is_published, is_featured, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const result = await db.query(query, [
      data.category_id, data.question, data.answer, JSON.stringify(data.tags || []),
      data.sort_order || 0, data.is_published !== false, data.is_featured || false, data.created_by
    ]);
    return result.insertId;
  }

  // 更新FAQ
  static async updateFAQ(id, data) {
    const query = `
      UPDATE help_faqs 
      SET category_id = ?, question = ?, answer = ?, tags = ?, 
          sort_order = ?, is_published = ?, is_featured = ?, updated_by = ?
      WHERE id = ?
    `;
    return await db.query(query, [
      data.category_id, data.question, data.answer, JSON.stringify(data.tags || []),
      data.sort_order, data.is_published, data.is_featured, data.updated_by, id
    ]);
  }

  // 删除FAQ
  static async deleteFAQ(id) {
    const query = 'DELETE FROM help_faqs WHERE id = ?';
    return await db.query(query, [id]);
  }

  // ======================== 教程管理 ========================
  
  // 获取教程列表
  static async getTutorials(filters = {}) {
    let query = `
      SELECT 
        t.*,
        c.name as category_name
      FROM help_tutorials t
      LEFT JOIN help_categories c ON t.category_id = c.id
      WHERE t.is_published = true
    `;
    const params = [];

    if (filters.category_id) {
      query += ' AND t.category_id = ?';
      params.push(filters.category_id);
    }

    if (filters.difficulty) {
      query += ' AND t.difficulty = ?';
      params.push(filters.difficulty);
    }

    if (filters.featured) {
      query += ' AND t.is_featured = true';
    }

    query += ' ORDER BY t.sort_order ASC, t.view_count DESC, t.created_at DESC';

    if (filters.limit) {
      query += ' LIMIT ?';
      params.push(parseInt(filters.limit));
    }

    return await db.query(query, params);
  }

  // 获取单个教程
  static async getTutorialById(id) {
    const query = `
      SELECT 
        t.*,
        c.name as category_name
      FROM help_tutorials t
      LEFT JOIN help_categories c ON t.category_id = c.id
      WHERE t.id = ? AND t.is_published = true
    `;
    const result = await db.query(query, [id]);
    
    if (result.length > 0) {
      // 增加浏览量
      await this.incrementTutorialViews(id);
      return result[0];
    }
    return null;
  }

  // 增加教程浏览量
  static async incrementTutorialViews(id) {
    const query = 'UPDATE help_tutorials SET view_count = view_count + 1 WHERE id = ?';
    return await db.query(query, [id]);
  }

  // ======================== 搜索功能 ========================
  
  // 综合搜索
  static async search(keyword, filters = {}) {
    const results = {
      articles: [],
      faqs: [],
      tutorials: [],
      total: 0
    };

    // 记录搜索历史
    await this.recordSearchHistory(keyword, filters.user_id, filters.ip_address, filters.user_agent);

    // 搜索文章
    const articleQuery = `
      SELECT 'article' as type, id, title as title, summary as content, 
             view_count, created_at, category_id
      FROM help_articles 
      WHERE is_published = true 
        AND MATCH(title, summary, content) AGAINST(? IN NATURAL LANGUAGE MODE)
      ORDER BY view_count DESC
      LIMIT 10
    `;
    results.articles = await db.query(articleQuery, [keyword]);

    // 搜索FAQ
    const faqQuery = `
      SELECT 'faq' as type, id, question as title, answer as content,
             view_count, created_at, category_id
      FROM help_faqs 
      WHERE is_published = true 
        AND MATCH(question, answer) AGAINST(? IN NATURAL LANGUAGE MODE)
      ORDER BY helpful_count DESC
      LIMIT 10
    `;
    results.faqs = await db.query(faqQuery, [keyword]);

    // 搜索教程
    const tutorialQuery = `
      SELECT 'tutorial' as type, id, title as title, description as content,
             view_count, created_at, category_id
      FROM help_tutorials 
      WHERE is_published = true 
        AND MATCH(title, description, content) AGAINST(? IN NATURAL LANGUAGE MODE)
      ORDER BY view_count DESC
      LIMIT 5
    `;
    results.tutorials = await db.query(tutorialQuery, [keyword]);

    results.total = results.articles.length + results.faqs.length + results.tutorials.length;

    return results;
  }

  // 记录搜索历史
  static async recordSearchHistory(keyword, user_id = null, ip_address = null, user_agent = null) {
    const query = `
      INSERT INTO help_search_history (keyword, user_id, ip_address, user_agent, result_count)
      VALUES (?, ?, ?, ?, ?)
    `;
    return await db.query(query, [keyword, user_id, ip_address, user_agent, 0]);
  }

  // 获取搜索建议
  static async getSearchSuggestions(keyword, limit = 5) {
    const query = `
      SELECT keyword, COUNT(*) as count
      FROM help_search_history 
      WHERE keyword LIKE ? 
      GROUP BY keyword 
      ORDER BY count DESC, created_at DESC
      LIMIT ?
    `;
    return await db.query(query, [`%${keyword}%`, limit]);
  }

  // ======================== 反馈管理 ========================
  
  // 提交反馈
  static async submitFeedback(data) {
    const query = `
      INSERT INTO help_feedback (
        type, target_id, user_id, rating, is_helpful, content, 
        contact_info, ip_address
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const result = await db.query(query, [
      data.type, data.target_id, data.user_id, data.rating, 
      data.is_helpful, data.content, data.contact_info, data.ip_address
    ]);
    
    // 更新对应内容的反馈计数
    if (data.target_id && data.is_helpful !== null) {
      await this.updateFeedbackCount(data.type, data.target_id, data.is_helpful);
    }
    
    return result.insertId;
  }

  // 更新反馈计数
  static async updateFeedbackCount(type, target_id, is_helpful) {
    let table, field;
    
    switch (type) {
    case 'article':
      table = 'help_articles';
      field = is_helpful ? 'helpful_count' : 'unhelpful_count';
      break;
    case 'faq':
      table = 'help_faqs';
      field = is_helpful ? 'helpful_count' : 'unhelpful_count';
      break;
    case 'tutorial':
      table = 'help_tutorials';
      field = is_helpful ? 'like_count' : 'unhelpful_count';
      break;
    default:
      return;
    }

    const query = `UPDATE ${table} SET ${field} = ${field} + 1 WHERE id = ?`;
    return await db.query(query, [target_id]);
  }

  // ======================== 统计数据 ========================
  
  // 获取帮助中心统计
  static async getStatistics() {
    const queries = [
      'SELECT COUNT(*) as total_articles FROM help_articles WHERE is_published = true',
      'SELECT COUNT(*) as total_faqs FROM help_faqs WHERE is_published = true', 
      'SELECT COUNT(*) as total_tutorials FROM help_tutorials WHERE is_published = true',
      'SELECT SUM(view_count) as total_views FROM help_articles WHERE is_published = true',
      'SELECT COUNT(*) as total_searches FROM help_search_history WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)',
      'SELECT COUNT(*) as total_feedback FROM help_feedback WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)'
    ];

    const results = await Promise.all(queries.map(query => db.query(query)));
    
    return {
      total_articles: results[0][0].total_articles,
      total_faqs: results[1][0].total_faqs,
      total_tutorials: results[2][0].total_tutorials,
      total_views: results[3][0].total_views || 0,
      total_searches: results[4][0].total_searches,
      total_feedback: results[5][0].total_feedback,
      satisfaction_rate: 95 // 可以基于实际反馈计算
    };
  }

  // ======================== 设置管理 ========================
  
  // 获取设置
  static async getSetting(key) {
    const query = 'SELECT setting_value, setting_type FROM help_settings WHERE setting_key = ?';
    const result = await db.query(query, [key]);
    
    if (result.length > 0) {
      const { setting_value, setting_type } = result[0];
      
      switch (setting_type) {
      case 'number':
        return parseInt(setting_value);
      case 'boolean':
        return setting_value === 'true';
      case 'json':
        return JSON.parse(setting_value);
      default:
        return setting_value;
      }
    }
    return null;
  }

  // 获取公开设置
  static async getPublicSettings() {
    const query = 'SELECT setting_key, setting_value, setting_type FROM help_settings WHERE is_public = true';
    const results = await db.query(query);
    
    const settings = {};
    results.forEach(({ setting_key, setting_value, setting_type }) => {
      switch (setting_type) {
      case 'number':
        settings[setting_key] = parseInt(setting_value);
        break;
      case 'boolean':
        settings[setting_key] = setting_value === 'true';
        break;
      case 'json':
        settings[setting_key] = JSON.parse(setting_value);
        break;
      default:
        settings[setting_key] = setting_value;
      }
    });
    
    return settings;
  }

  // 更新设置
  static async updateSetting(key, value, type = 'string') {
    const query = `
      INSERT INTO help_settings (setting_key, setting_value, setting_type) 
      VALUES (?, ?, ?) 
      ON DUPLICATE KEY UPDATE setting_value = ?, setting_type = ?
    `;
    const stringValue = typeof value === 'object' ? JSON.stringify(value) : String(value);
    return await db.query(query, [key, stringValue, type, stringValue, type]);
  }
}

module.exports = HelpCenterModel;