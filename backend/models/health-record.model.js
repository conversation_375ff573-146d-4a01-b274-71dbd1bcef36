const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const HealthRecord = sequelize.define(
  'HealthRecord',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id'
    },
    flockId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'flock_id',
      references: {
        model: 'flocks',
        key: 'id'
      },
      comment: '鹅群ID'
    },
    checkDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      field: 'check_date',
      comment: '检查日期'
    },
    checkType: {
      type: DataTypes.ENUM('routine', 'vaccination', 'treatment', 'emergency'),
      defaultValue: 'routine',
      field: 'check_type',
      comment: '检查类型'
    },
    healthStatus: {
      type: DataTypes.ENUM('healthy', 'warning', 'sick', 'critical'),
      defaultValue: 'healthy',
      field: 'health_status',
      comment: '健康状态'
    },
    affectedCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'affected_count',
      comment: '受影响数量'
    },
    symptoms: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '症状描述'
    },
    diagnosis: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '诊断结果'
    },
    treatment: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '治疗方案'
    },
    medication: {
      type: DataTypes.STRING(200),
      allowNull: true,
      comment: '用药情况'
    },
    dosage: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '用药剂量'
    },
    veterinarian: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '兽医师'
    },
    followUpDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'follow_up_date',
      comment: '随访日期'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '备注信息'
    },
    recordStatus: {
      type: DataTypes.ENUM('pending', 'processing', 'completed'),
      defaultValue: 'pending',
      field: 'record_status',
      comment: '记录状态'
    }
  },
  {
    tableName: 'health_records',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
);

module.exports = HealthRecord;
