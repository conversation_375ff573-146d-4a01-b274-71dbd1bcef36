/**
 * 统一错误处理中间件
 * 提供标准化的错误响应和日志记录
 */

const fs = require('fs');
const path = require('path');

// 确保日志目录存在
const logDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

/**
 * 自定义错误类
 */
class AppError extends Error {
  constructor(message, statusCode, code = null) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 日志记录器
 */
class Logger {
  static log(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      ...meta
    };

    // 控制台输出
    const colorMap = {
      error: '\x1b[31m', // 红色
      warn: '\x1b[33m', // 黄色
      info: '\x1b[36m', // 青色
      debug: '\x1b[37m', // 白色
      reset: '\x1b[0m' // 重置
    };

    const color = colorMap[level] || colorMap.info;
    console.log(
      `${color}[${timestamp}] ${level.toUpperCase()}: ${message}${colorMap.reset}`
    );

    if (meta && Object.keys(meta).length > 0) {
      console.log(
        `${color}[META]:${colorMap.reset}`,
        JSON.stringify(meta, null, 2)
      );
    }

    // 写入日志文件
    this.writeToFile(level, logEntry);
  }

  static writeToFile(level, logEntry) {
    try {
      const date = new Date().toISOString().split('T')[0];
      const logFile = path.join(logDir, `${level}-${date}.log`);
      const logLine = JSON.stringify(logEntry) + '\n';

      fs.appendFileSync(logFile, logLine);
    } catch (error) {
      console.error('写入日志文件失败:', error);
    }
  }

  static error(message, meta = {}) {
    this.log('error', message, meta);
  }

  static warn(message, meta = {}) {
    this.log('warn', message, meta);
  }

  static info(message, meta = {}) {
    this.log('info', message, meta);
  }

  static debug(message, meta = {}) {
    this.log('debug', message, meta);
  }
}

/**
 * 请求日志中间件
 */
const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  const { method, url, ip, headers } = req;

  // 记录请求开始
  Logger.info('Request started', {
    method,
    url,
    ip,
    userAgent: headers['user-agent'],
    userId: req.user?.id,
    requestId: req.id || Math.random().toString(36).substr(2, 9)
  });

  // 监听响应结束
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const { statusCode } = res;

    const logLevel = statusCode >= 400 ? 'warn' : 'info';
    Logger.log(logLevel, 'Request completed', {
      method,
      url,
      statusCode,
      duration: `${duration}ms`,
      ip,
      userId: req.user?.id
    });
  });

  next();
};

/**
 * 数据库错误处理
 */
const handleDatabaseError = (error) => {
  Logger.error('Database error', {
    code: error.code,
    errno: error.errno,
    sqlMessage: error.sqlMessage,
    sql: error.sql
  });

  // MySQL错误码映射
  const errorMap = {
    ER_DUP_ENTRY: { status: 409, message: '数据已存在' },
    ER_NO_REFERENCED_ROW_2: { status: 400, message: '关联数据不存在' },
    ER_ROW_IS_REFERENCED_2: {
      status: 400,
      message: '数据被其他记录引用，无法删除'
    },
    ER_DATA_TOO_LONG: { status: 400, message: '数据长度超出限制' },
    ER_BAD_NULL_ERROR: { status: 400, message: '必填字段不能为空' },
    ER_ACCESS_DENIED_ERROR: { status: 500, message: '数据库访问被拒绝' },
    ECONNREFUSED: { status: 500, message: '数据库连接失败' },
    PROTOCOL_CONNECTION_LOST: { status: 500, message: '数据库连接丢失' }
  };

  const mappedError = errorMap[error.code] || {
    status: 500,
    message: '数据库操作失败'
  };
  return new AppError(mappedError.message, mappedError.status, error.code);
};

/**
 * 验证错误处理
 */
const handleValidationError = (error) => {
  Logger.warn('Validation error', {
    message: error.message,
    errors: error.errors
  });

  return new AppError('数据验证失败', 400, 'VALIDATION_ERROR');
};

/**
 * JWT错误处理
 */
const handleJWTError = (error) => {
  Logger.warn('JWT error', {
    name: error.name,
    message: error.message
  });

  const jwtErrorMap = {
    JsonWebTokenError: '无效的访问令牌',
    TokenExpiredError: '访问令牌已过期',
    NotBeforeError: '访问令牌尚未生效'
  };

  const message = jwtErrorMap[error.name] || '身份验证失败';
  return new AppError(message, 401, error.name);
};

/**
 * 主错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // 记录原始错误
  Logger.error('Unhandled error', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userId: req.user?.id
  });

  // 处理不同类型的错误
  if (err.code && err.code.startsWith('ER_')) {
    error = handleDatabaseError(err);
  } else if (err.name === 'ValidationError') {
    error = handleValidationError(err);
  } else if (err.name && err.name.includes('JsonWebToken')) {
    error = handleJWTError(err);
  } else if (err.name === 'CastError') {
    error = new AppError('无效的数据格式', 400, 'CAST_ERROR');
  } else if (err.code === 'ENOENT') {
    error = new AppError('文件不存在', 404, 'FILE_NOT_FOUND');
  } else if (err.code === 'EACCES') {
    error = new AppError('文件访问权限不足', 403, 'ACCESS_DENIED');
  }

  // 设置默认错误
  if (!error.statusCode) {
    error = new AppError('服务器内部错误', 500, 'INTERNAL_ERROR');
  }

  // 发送错误响应
  res.status(error.statusCode).json({
    success: false,
    error: {
      message: error.message,
      code: error.code,
      ...(process.env.NODE_ENV === 'development' && {
        stack: err.stack,
        details: err
      })
    },
    timestamp: new Date().toISOString(),
    path: req.url,
    method: req.method
  });
};

/**
 * 404错误处理
 */
const notFoundHandler = (req, res, next) => {
  const message = `路径 ${req.originalUrl} 不存在`;
  Logger.warn('Route not found', {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip
  });

  res.status(404).json({
    success: false,
    error: {
      message,
      code: 'ROUTE_NOT_FOUND'
    },
    timestamp: new Date().toISOString(),
    path: req.url,
    method: req.method
  });
};

/**
 * 异步错误捕获包装器
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 成功响应包装器
 */
const successResponse = (res, data, message = '操作成功', statusCode = 200) => {
  res.status(statusCode).json({
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  });
};

/**
 * 分页响应包装器
 */
const paginatedResponse = (res, data, pagination, message = '获取成功') => {
  res.json({
    success: true,
    message,
    data,
    pagination,
    timestamp: new Date().toISOString()
  });
};

module.exports = {
  AppError,
  Logger,
  requestLogger,
  errorHandler,
  notFoundHandler,
  asyncHandler,
  successResponse,
  paginatedResponse,
  handleDatabaseError,
  handleValidationError,
  handleJWTError
};
