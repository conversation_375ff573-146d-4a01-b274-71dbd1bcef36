/**
 * 智慧养鹅SAAS平台 - 数据权限控制中间件
 * 确保严格的多租户数据隔离和权限控制
 */

const winston = require('winston');

// Logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

/**
 * 数据权限级别定义
 */
const DATA_PERMISSION_LEVELS = {
  PLATFORM_ADMIN: 'platform_admin',     // 平台管理员：可访问所有租户数据
  TENANT_ADMIN: 'tenant_admin',         // 租户管理员：只能访问自己租户的所有数据
  TENANT_USER: 'tenant_user',           // 租户用户：只能访问自己创建/负责的数据
  READ_ONLY: 'read_only'                // 只读用户：只能查看被授权的数据
};

/**
 * 资源权限配置
 * 定义每个资源的访问权限级别
 */
const RESOURCE_PERMISSIONS = {
  // 平台级资源（只有平台管理员可访问）
  platform_admins: [DATA_PERMISSION_LEVELS.PLATFORM_ADMIN],
  tenants: [DATA_PERMISSION_LEVELS.PLATFORM_ADMIN],
  platform_announcements: [DATA_PERMISSION_LEVELS.PLATFORM_ADMIN],
  goose_prices: [DATA_PERMISSION_LEVELS.PLATFORM_ADMIN],
  knowledge_base: [DATA_PERMISSION_LEVELS.PLATFORM_ADMIN],
  mall_products: [DATA_PERMISSION_LEVELS.PLATFORM_ADMIN],
  ai_configs: [DATA_PERMISSION_LEVELS.PLATFORM_ADMIN],
  
  // 租户级资源（租户管理员和用户可访问，但有数据隔离）
  tenant_users: [DATA_PERMISSION_LEVELS.TENANT_ADMIN, DATA_PERMISSION_LEVELS.TENANT_USER],
  flocks: [DATA_PERMISSION_LEVELS.TENANT_ADMIN, DATA_PERMISSION_LEVELS.TENANT_USER],
  inventories: [DATA_PERMISSION_LEVELS.TENANT_ADMIN, DATA_PERMISSION_LEVELS.TENANT_USER],
  health_records: [DATA_PERMISSION_LEVELS.TENANT_ADMIN, DATA_PERMISSION_LEVELS.TENANT_USER],
  production_records: [DATA_PERMISSION_LEVELS.TENANT_ADMIN, DATA_PERMISSION_LEVELS.TENANT_USER],
  financial_records: [DATA_PERMISSION_LEVELS.TENANT_ADMIN, DATA_PERMISSION_LEVELS.TENANT_USER],
  
  // 系统级资源
  system_logs: [DATA_PERMISSION_LEVELS.PLATFORM_ADMIN, DATA_PERMISSION_LEVELS.TENANT_ADMIN],
  file_uploads: [DATA_PERMISSION_LEVELS.PLATFORM_ADMIN, DATA_PERMISSION_LEVELS.TENANT_ADMIN, DATA_PERMISSION_LEVELS.TENANT_USER]
};

/**
 * 角色到权限级别的映射
 */
const ROLE_TO_PERMISSION_LEVEL = {
  // 平台管理员角色
  super_admin: DATA_PERMISSION_LEVELS.PLATFORM_ADMIN,
  admin: DATA_PERMISSION_LEVELS.PLATFORM_ADMIN,
  operator: DATA_PERMISSION_LEVELS.PLATFORM_ADMIN,
  support: DATA_PERMISSION_LEVELS.READ_ONLY,
  
  // 租户用户角色
  tenant_admin: DATA_PERMISSION_LEVELS.TENANT_ADMIN,
  manager: DATA_PERMISSION_LEVELS.TENANT_USER,
  user: DATA_PERMISSION_LEVELS.TENANT_USER
};

/**
 * 数据权限控制类
 */
class DataPermissionController {
  
  /**
   * 检查用户对资源的访问权限
   * @param {Object} user - 用户信息
   * @param {string} resource - 资源名称
   * @param {string} operation - 操作类型（create, read, update, delete）
   * @returns {boolean} - 是否有权限
   */
  static checkResourceAccess(user, resource, operation = 'read') {
    if (!user || !resource) {
      return false;
    }

    // 获取用户权限级别
    const userPermissionLevel = ROLE_TO_PERMISSION_LEVEL[user.role];
    if (!userPermissionLevel) {
      return false;
    }

    // 获取资源所需权限
    const requiredPermissions = RESOURCE_PERMISSIONS[resource];
    if (!requiredPermissions) {
      logger.warn('Resource not defined in permissions', { resource, user: user.username });
      return false;
    }

    // 检查权限
    return requiredPermissions.includes(userPermissionLevel);
  }

  /**
   * 构建租户数据隔离查询条件
   * @param {Object} user - 用户信息
   * @param {string} resource - 资源名称
   * @returns {Object} - 查询条件
   */
  static buildTenantFilter(user, resource) {
    const filter = {};

    if (!user) {
      return filter;
    }

    // 平台管理员无需租户过滤
    if (user.user_type === 'platform_admin') {
      return filter;
    }

    // 租户用户需要租户ID过滤
    if (user.user_type === 'tenant_user' && user.tenant_id) {
      filter.tenant_id = user.tenant_id;

      // 对于某些资源，普通用户只能访问自己创建的数据
      if (user.role === 'user') {
        const personalResources = ['health_records', 'production_records', 'financial_records'];
        if (personalResources.includes(resource)) {
          filter.created_by = user.id;
        }
      }
    }

    return filter;
  }

  /**
   * 构建完整的WHERE子句
   * @param {Object} user - 用户信息
   * @param {string} resource - 资源名称
   * @param {Object} additionalFilters - 额外的过滤条件
   * @returns {Object} - {whereClause: string, params: array}
   */
  static buildSecureWhereClause(user, resource, additionalFilters = {}) {
    const tenantFilter = this.buildTenantFilter(user, resource);
    const allFilters = { ...tenantFilter, ...additionalFilters };

    const conditions = [];
    const params = [];

    Object.entries(allFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          conditions.push(`${key} IN (${value.map(() => '?').join(', ')})`);
          params.push(...value);
        } else {
          conditions.push(`${key} = ?`);
          params.push(value);
        }
      }
    });

    return {
      whereClause: conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '',
      params
    };
  }

  /**
   * 验证数据所有权
   * @param {Object} db - 数据库连接
   * @param {Object} user - 用户信息
   * @param {string} resource - 资源名称
   * @param {number} resourceId - 资源ID
   * @returns {Promise<boolean>} - 是否有权限
   */
  static async verifyDataOwnership(db, user, resource, resourceId) {
    if (!user || !resource || !resourceId) {
      return false;
    }

    // 平台管理员拥有所有数据的访问权限
    if (user.user_type === 'platform_admin') {
      return true;
    }

    try {
      const filter = this.buildTenantFilter(user, resource);
      const conditions = ['id = ?'];
      const params = [resourceId];

      // 添加租户过滤条件
      Object.entries(filter).forEach(([key, value]) => {
        conditions.push(`${key} = ?`);
        params.push(value);
      });

      const whereClause = `WHERE ${conditions.join(' AND ')}`;
      
      const result = await db.query(
        `SELECT COUNT(*) as count FROM ${resource} ${whereClause}`,
        params
      );

      return result && result[0] && result[0].count > 0;
    } catch (error) {
      logger.error('Error verifying data ownership', {
        error: error.message,
        user: user.username,
        resource,
        resourceId
      });
      return false;
    }
  }

  /**
   * 记录数据访问日志
   * @param {Object} user - 用户信息
   * @param {string} action - 操作类型
   * @param {string} resource - 资源名称
   * @param {number} resourceId - 资源ID
   * @param {string} ip - IP地址
   * @param {boolean} success - 是否成功
   */
  static logDataAccess(user, action, resource, resourceId, ip, success = true) {
    logger.info('Data access log', {
      userId: user?.id,
      username: user?.username,
      userType: user?.user_type,
      tenantId: user?.tenant_id,
      action,
      resource,
      resourceId,
      ip,
      success,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * 数据权限检查中间件
 * 在路由处理前检查用户是否有权限访问该资源
 */
const checkDataPermission = (resource, operation = 'read') => {
  return (req, res, next) => {
    try {
      const user = req.admin || req.user;
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: '用户未认证',
          code: 'UNAUTHORIZED'
        });
      }

      // 检查资源访问权限
      const hasPermission = DataPermissionController.checkResourceAccess(user, resource, operation);
      
      if (!hasPermission) {
        DataPermissionController.logDataAccess(
          user, operation, resource, null, req.ip, false
        );
        
        return res.status(403).json({
          success: false,
          message: '没有权限访问该资源',
          code: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      // 添加数据过滤器到请求对象
      req.dataFilter = DataPermissionController.buildTenantFilter(user, resource);
      
      next();
    } catch (error) {
      logger.error('Data permission check error', {
        error: error.message,
        resource,
        operation,
        userId: req.user?.id || req.admin?.id
      });

      res.status(500).json({
        success: false,
        message: '权限检查失败'
      });
    }
  };
};

/**
 * 数据所有权验证中间件
 * 用于验证用户是否拥有特定资源的访问权限
 */
const verifyDataOwnership = (resource) => {
  return async (req, res, next) => {
    try {
      const user = req.admin || req.user;
      const resourceId = parseInt(req.params.id);

      if (!user) {
        return res.status(401).json({
          success: false,
          message: '用户未认证',
          code: 'UNAUTHORIZED'
        });
      }

      if (isNaN(resourceId)) {
        return res.status(400).json({
          success: false,
          message: '无效的资源ID'
        });
      }

      // 验证数据所有权
      const hasOwnership = await DataPermissionController.verifyDataOwnership(
        req.db, user, resource, resourceId
      );

      if (!hasOwnership) {
        DataPermissionController.logDataAccess(
          user, 'access_denied', resource, resourceId, req.ip, false
        );

        return res.status(404).json({
          success: false,
          message: '资源不存在或无权访问',
          code: 'RESOURCE_NOT_FOUND'
        });
      }

      next();
    } catch (error) {
      logger.error('Data ownership verification error', {
        error: error.message,
        resource,
        resourceId: req.params.id,
        userId: req.user?.id || req.admin?.id
      });

      res.status(500).json({
        success: false,
        message: '权限验证失败'
      });
    }
  };
};

/**
 * 租户数据隔离中间件
 * 自动为查询添加租户过滤条件
 */
const enforceTenantIsolation = (req, res, next) => {
  const user = req.admin || req.user;
  
  if (user && user.user_type === 'tenant_user' && user.tenant_id) {
    // 为查询添加租户过滤
    req.tenantFilter = {
      tenant_id: user.tenant_id
    };
  }

  next();
};

/**
 * API访问日志中间件
 */
const logAPIAccess = (req, res, next) => {
  const user = req.admin || req.user;
  
  // 记录API访问
  logger.info('API access', {
    userId: user?.id,
    username: user?.username,
    userType: user?.user_type,
    tenantId: user?.tenant_id,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });

  next();
};

module.exports = {
  DataPermissionController,
  checkDataPermission,
  verifyDataOwnership,
  enforceTenantIsolation,
  logAPIAccess,
  DATA_PERMISSION_LEVELS,
  RESOURCE_PERMISSIONS,
  ROLE_TO_PERMISSION_LEVEL
};