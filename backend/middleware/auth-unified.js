/**
 * 智慧养鹅SAAS平台 - 统一权限认证中间件
 * 整合所有权限验证逻辑，提供一致的认证和授权服务
 * 
 * 功能：
 * - JWT Token验证
 * - 多租户权限控制
 * - 角色权限映射
 * - SAAS平台级权限管理
 */

const jwt = require('jsonwebtoken');

/**
 * SAAS平台四级权限体系
 * 1. 平台超级管理员 - 拥有所有权限
 * 2. 租户管理员 - 租户内所有权限  
 * 3. 部门经理 - 部门级权限
 * 4. 普通员工 - 基础权限
 */
const PERMISSION_LEVELS = {
  PLATFORM_SUPER_ADMIN: 'platform_super_admin',
  TENANT_ADMIN: 'tenant_admin', 
  DEPARTMENT_MANAGER: 'department_manager',
  EMPLOYEE: 'employee'
};

/**
 * 权限常量 - 按业务模块分类
 */
const PERMISSIONS = {
  // 平台管理权限
  PLATFORM: {
    TENANT_MANAGE: 'platform:tenant:manage',
    SYSTEM_CONFIG: 'platform:system:config',
    CROSS_TENANT_VIEW: 'platform:cross_tenant:view',
    ANALYTICS: 'platform:analytics:view',
    MONITORING: 'platform:monitoring:view'
  },
  
  // OA办公权限
  OA: {
    ACCESS: 'oa:access',
    FINANCE_VIEW: 'oa:finance:view', 
    FINANCE_MANAGE: 'oa:finance:manage',
    PURCHASE_VIEW: 'oa:purchase:view',
    PURCHASE_CREATE: 'oa:purchase:create',
    PURCHASE_APPROVE: 'oa:purchase:approve',
    REIMBURSEMENT_VIEW: 'oa:reimbursement:view',
    REIMBURSEMENT_CREATE: 'oa:reimbursement:create',
    REIMBURSEMENT_APPROVE: 'oa:reimbursement:approve',
    APPROVAL_PROCESS: 'oa:approval:process',
    STAFF_MANAGE: 'oa:staff:manage'
  },
  
  // 生产管理权限
  PRODUCTION: {
    VIEW: 'production:view',
    MANAGE: 'production:manage', 
    RECORD_CREATE: 'production:record:create',
    RECORD_UPDATE: 'production:record:update',
    INVENTORY_VIEW: 'production:inventory:view',
    INVENTORY_MANAGE: 'production:inventory:manage',
    ENVIRONMENT_CONTROL: 'production:environment:control'
  },
  
  // 健康管理权限
  HEALTH: {
    VIEW: 'health:view',
    MANAGE: 'health:manage',
    DIAGNOSIS: 'health:diagnosis',
    AI_DIAGNOSIS: 'health:ai_diagnosis'
  },
  
  // 商城权限
  SHOP: {
    VIEW: 'shop:view',
    MANAGE: 'shop:manage',
    ORDER_VIEW: 'shop:order:view',
    ORDER_PROCESS: 'shop:order:process',
    PRODUCT_MANAGE: 'shop:product:manage'
  },
  
  // 用户管理权限
  USER: {
    VIEW: 'user:view',
    MANAGE: 'user:manage',
    ROLE_ASSIGN: 'user:role:assign'
  }
};

/**
 * 角色权限映射
 */
const ROLE_PERMISSIONS = {
  // 平台超级管理员：所有权限
  [PERMISSION_LEVELS.PLATFORM_SUPER_ADMIN]: Object.values(PERMISSIONS).flatMap(module => Object.values(module)),
  
  // 租户管理员：租户内所有权限
  [PERMISSION_LEVELS.TENANT_ADMIN]: [
    ...Object.values(PERMISSIONS.OA),
    ...Object.values(PERMISSIONS.PRODUCTION),
    ...Object.values(PERMISSIONS.HEALTH),
    ...Object.values(PERMISSIONS.SHOP),
    ...Object.values(PERMISSIONS.USER)
  ],
  
  // 部门经理：部门管理权限（包含财务审批权限）
  [PERMISSION_LEVELS.DEPARTMENT_MANAGER]: [
    PERMISSIONS.OA.ACCESS,
    PERMISSIONS.OA.FINANCE_VIEW,
    PERMISSIONS.OA.FINANCE_MANAGE,
    PERMISSIONS.OA.PURCHASE_CREATE,
    PERMISSIONS.OA.PURCHASE_APPROVE,
    PERMISSIONS.OA.REIMBURSEMENT_VIEW,
    PERMISSIONS.OA.REIMBURSEMENT_CREATE,
    PERMISSIONS.OA.REIMBURSEMENT_APPROVE,
    PERMISSIONS.OA.APPROVAL_PROCESS,
    PERMISSIONS.PRODUCTION.VIEW,
    PERMISSIONS.PRODUCTION.MANAGE,
    PERMISSIONS.HEALTH.VIEW,
    PERMISSIONS.HEALTH.MANAGE,
    PERMISSIONS.SHOP.VIEW,
    PERMISSIONS.SHOP.ORDER_PROCESS,
    PERMISSIONS.USER.VIEW
  ],
  
  // 普通员工：基础权限
  [PERMISSION_LEVELS.EMPLOYEE]: [
    PERMISSIONS.OA.ACCESS,
    PERMISSIONS.OA.FINANCE_VIEW,
    PERMISSIONS.OA.PURCHASE_VIEW,
    PERMISSIONS.OA.REIMBURSEMENT_VIEW,
    PERMISSIONS.OA.REIMBURSEMENT_CREATE,
    PERMISSIONS.PRODUCTION.VIEW,
    PERMISSIONS.PRODUCTION.RECORD_CREATE,
    PERMISSIONS.HEALTH.VIEW,
    PERMISSIONS.SHOP.VIEW
  ]
};

/**
 * 统一权限管理器
 */
class UnifiedAuthManager {
  
  /**
   * 验证JWT Token
   */
  static verifyToken(token) {
    try {
      return jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      throw new Error('Invalid token');
    }
  }
  
  /**
   * 检查用户权限
   */
  static hasPermission(user, requiredPermission) {
    if (!user || !user.role) return false;
    
    const userPermissions = ROLE_PERMISSIONS[user.role] || [];
    return userPermissions.includes(requiredPermission);
  }
  
  /**
   * 检查多个权限（AND逻辑）
   */
  static hasAllPermissions(user, requiredPermissions) {
    return requiredPermissions.every(permission => 
      this.hasPermission(user, permission)
    );
  }
  
  /**
   * 检查多个权限（OR逻辑）
   */
  static hasAnyPermission(user, requiredPermissions) {
    return requiredPermissions.some(permission => 
      this.hasPermission(user, permission)
    );
  }
  
  /**
   * 获取用户所有权限
   */
  static getUserPermissions(user) {
    if (!user || !user.role) return [];
    return ROLE_PERMISSIONS[user.role] || [];
  }
  
  /**
   * 检查租户权限
   */
  static checkTenantAccess(user, tenantId) {
    // 平台超级管理员可以访问所有租户
    if (user.role === PERMISSION_LEVELS.PLATFORM_SUPER_ADMIN) {
      return true;
    }
    
    // 其他用户只能访问自己的租户
    return user.tenantId === tenantId;
  }
}

/**
 * 统一认证中间件
 */
const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: '访问令牌缺失',
        code: 'TOKEN_MISSING'
      });
    }
    
    const token = authHeader.substring(7);
    const decoded = UnifiedAuthManager.verifyToken(token);
    
    // 将用户信息附加到请求对象
    req.user = decoded;
    req.userId = decoded.id;
    req.userRole = decoded.role;
    req.tenantId = decoded.tenantId;
    
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: '认证失败',
      code: 'AUTHENTICATION_FAILED',
      error: error.message
    });
  }
};

/**
 * 权限验证中间件工厂
 */
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '用户未认证',
        code: 'USER_NOT_AUTHENTICATED'
      });
    }
    
    if (!UnifiedAuthManager.hasPermission(req.user, permission)) {
      return res.status(403).json({
        success: false,
        message: '权限不足',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: permission,
        userRole: req.user.role
      });
    }
    
    next();
  };
};

/**
 * 多权限验证中间件工厂
 */
const requirePermissions = (permissions, logic = 'AND') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '用户未认证',
        code: 'USER_NOT_AUTHENTICATED'
      });
    }
    
    const hasPermission = logic === 'AND' 
      ? UnifiedAuthManager.hasAllPermissions(req.user, permissions)
      : UnifiedAuthManager.hasAnyPermission(req.user, permissions);
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: '权限不足',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: permissions,
        logic: logic,
        userRole: req.user.role
      });
    }
    
    next();
  };
};

/**
 * 租户权限验证中间件
 */
const requireTenantAccess = (req, res, next) => {
  const tenantId = req.params.tenantId || req.body.tenantId || req.query.tenantId;
  
  if (!tenantId) {
    return res.status(400).json({
      success: false,
      message: '租户ID缺失',
      code: 'TENANT_ID_MISSING'
    });
  }
  
  if (!UnifiedAuthManager.checkTenantAccess(req.user, tenantId)) {
    return res.status(403).json({
      success: false,
      message: '无权访问该租户资源',
      code: 'TENANT_ACCESS_DENIED'
    });
  }
  
  next();
};

module.exports = {
  PERMISSION_LEVELS,
  PERMISSIONS,
  ROLE_PERMISSIONS,
  UnifiedAuthManager,
  authenticate,
  requirePermission,
  requirePermissions,
  requireTenantAccess
};