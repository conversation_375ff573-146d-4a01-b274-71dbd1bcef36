/**
 * 智慧养鹅SAAS平台 - 安全数据库查询封装
 * 自动应用多租户数据隔离和权限控制
 */

const { DataPermissionController } = require('./data-permission');
const winston = require('winston');

// Logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

/**
 * 安全数据库查询类
 * 封装所有数据库操作，确保多租户数据隔离
 */
class SecureDatabase {
  constructor(db, user = null) {
    this.db = db;
    this.user = user;
  }

  /**
   * 安全查询 - 自动应用权限过滤
   * @param {string} table - 表名
   * @param {Object} conditions - 查询条件
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 查询结果
   */
  async select(table, conditions = {}, options = {}) {
    try {
      // 检查资源访问权限
      if (!DataPermissionController.checkResourceAccess(this.user, table, 'read')) {
        throw new Error('没有权限访问该资源');
      }

      // 构建安全查询条件
      const secureConditions = this._buildSecureConditions(table, conditions);
      const { whereClause, params } = DataPermissionController.buildSecureWhereClause(
        this.user, table, secureConditions
      );

      // 构建SELECT查询
      const columns = options.columns || '*';
      const orderBy = options.orderBy ? `ORDER BY ${options.orderBy}` : '';
      const limit = options.limit ? `LIMIT ${options.limit}` : '';
      const offset = options.offset ? `OFFSET ${options.offset}` : '';

      const sql = `SELECT ${columns} FROM ${table} ${whereClause} ${orderBy} ${limit} ${offset}`.trim();

      // 记录查询日志
      this._logQuery('SELECT', table, secureConditions, sql);

      return await this.db.query(sql, params);
    } catch (error) {
      logger.error('Secure select error', {
        error: error.message,
        table,
        conditions,
        user: this.user?.username
      });
      throw error;
    }
  }

  /**
   * 安全查询单条记录
   * @param {string} table - 表名
   * @param {Object} conditions - 查询条件
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async selectOne(table, conditions = {}, options = {}) {
    const results = await this.select(table, conditions, { ...options, limit: 1 });
    return results && results.length > 0 ? results[0] : null;
  }

  /**
   * 安全插入
   * @param {string} table - 表名
   * @param {Object} data - 插入数据
   * @returns {Promise<Object>} 插入结果
   */
  async insert(table, data) {
    try {
      // 检查创建权限
      if (!DataPermissionController.checkResourceAccess(this.user, table, 'create')) {
        throw new Error('没有权限创建该资源');
      }

      // 自动添加租户ID和创建者信息
      const secureData = this._enrichDataForInsert(table, data);

      const columns = Object.keys(secureData);
      const values = Object.values(secureData);
      const placeholders = values.map(() => '?').join(', ');

      const sql = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders})`;

      // 记录操作日志
      this._logQuery('INSERT', table, secureData, sql);

      return await this.db.query(sql, values);
    } catch (error) {
      logger.error('Secure insert error', {
        error: error.message,
        table,
        data,
        user: this.user?.username
      });
      throw error;
    }
  }

  /**
   * 安全更新
   * @param {string} table - 表名
   * @param {Object} data - 更新数据
   * @param {Object} conditions - 更新条件
   * @returns {Promise<Object>} 更新结果
   */
  async update(table, data, conditions) {
    try {
      // 检查更新权限
      if (!DataPermissionController.checkResourceAccess(this.user, table, 'update')) {
        throw new Error('没有权限更新该资源');
      }

      // 构建安全更新条件（包含租户隔离）
      const secureConditions = this._buildSecureConditions(table, conditions);
      const { whereClause, params: whereParams } = DataPermissionController.buildSecureWhereClause(
        this.user, table, secureConditions
      );

      if (!whereClause) {
        throw new Error('更新条件不能为空');
      }

      // 构建SET子句
      const setClauses = [];
      const setParams = [];

      Object.entries(data).forEach(([key, value]) => {
        setClauses.push(`${key} = ?`);
        setParams.push(value);
      });

      // 自动添加更新时间
      setClauses.push('updated_at = NOW()');

      const sql = `UPDATE ${table} SET ${setClauses.join(', ')} ${whereClause}`;
      const params = [...setParams, ...whereParams];

      // 记录操作日志
      this._logQuery('UPDATE', table, { data, conditions: secureConditions }, sql);

      return await this.db.query(sql, params);
    } catch (error) {
      logger.error('Secure update error', {
        error: error.message,
        table,
        data,
        conditions,
        user: this.user?.username
      });
      throw error;
    }
  }

  /**
   * 安全删除
   * @param {string} table - 表名
   * @param {Object} conditions - 删除条件
   * @param {boolean} softDelete - 是否软删除
   * @returns {Promise<Object>} 删除结果
   */
  async delete(table, conditions, softDelete = false) {
    try {
      // 检查删除权限
      if (!DataPermissionController.checkResourceAccess(this.user, table, 'delete')) {
        throw new Error('没有权限删除该资源');
      }

      // 构建安全删除条件
      const secureConditions = this._buildSecureConditions(table, conditions);
      const { whereClause, params } = DataPermissionController.buildSecureWhereClause(
        this.user, table, secureConditions
      );

      if (!whereClause) {
        throw new Error('删除条件不能为空');
      }

      let sql;
      if (softDelete) {
        // 软删除 - 更新状态
        sql = `UPDATE ${table} SET status = 'deleted', updated_at = NOW() ${whereClause}`;
      } else {
        // 硬删除
        sql = `DELETE FROM ${table} ${whereClause}`;
      }

      // 记录操作日志
      this._logQuery('DELETE', table, secureConditions, sql);

      return await this.db.query(sql, params);
    } catch (error) {
      logger.error('Secure delete error', {
        error: error.message,
        table,
        conditions,
        user: this.user?.username
      });
      throw error;
    }
  }

  /**
   * 安全计数查询
   * @param {string} table - 表名
   * @param {Object} conditions - 查询条件
   * @returns {Promise<number>} 记录数量
   */
  async count(table, conditions = {}) {
    try {
      const result = await this.select(table, conditions, { columns: 'COUNT(*) as count' });
      return result && result[0] ? result[0].count : 0;
    } catch (error) {
      logger.error('Secure count error', {
        error: error.message,
        table,
        conditions,
        user: this.user?.username
      });
      throw error;
    }
  }

  /**
   * 安全分页查询
   * @param {string} table - 表名
   * @param {Object} conditions - 查询条件
   * @param {number} page - 页码
   * @param {number} pageSize - 每页数量
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 分页结果
   */
  async paginate(table, conditions = {}, page = 1, pageSize = 20, options = {}) {
    try {
      const offset = (page - 1) * pageSize;
      
      // 获取总数
      const total = await this.count(table, conditions);
      
      // 获取分页数据
      const data = await this.select(table, conditions, {
        ...options,
        limit: pageSize,
        offset: offset
      });

      return {
        data,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
          hasNext: page < Math.ceil(total / pageSize),
          hasPrev: page > 1
        }
      };
    } catch (error) {
      logger.error('Secure paginate error', {
        error: error.message,
        table,
        conditions,
        page,
        pageSize,
        user: this.user?.username
      });
      throw error;
    }
  }

  /**
   * 安全事务执行
   * @param {Function} callback - 事务回调函数
   * @returns {Promise<any>} 事务结果
   */
  async transaction(callback) {
    try {
      return await this.db.transaction(async (connection) => {
        const secureDb = new SecureDatabase(connection, this.user);
        return await callback(secureDb);
      });
    } catch (error) {
      logger.error('Secure transaction error', {
        error: error.message,
        user: this.user?.username
      });
      throw error;
    }
  }

  /**
   * 构建安全查询条件
   * @param {string} table - 表名
   * @param {Object} conditions - 原始条件
   * @returns {Object} 安全条件
   */
  _buildSecureConditions(table, conditions) {
    const secureConditions = { ...conditions };

    // 对于租户级资源，自动添加租户过滤
    if (this.user && this.user.user_type === 'tenant_user' && this.user.tenant_id) {
      const tenantTables = [
        'tenant_users', 'flocks', 'inventories', 'health_records',
        'production_records', 'financial_records', 'system_logs', 'file_uploads'
      ];

      if (tenantTables.includes(table)) {
        secureConditions.tenant_id = this.user.tenant_id;
      }
    }

    return secureConditions;
  }

  /**
   * 为插入数据添加必要字段
   * @param {string} table - 表名
   * @param {Object} data - 原始数据
   * @returns {Object} 增强后的数据
   */
  _enrichDataForInsert(table, data) {
    const enrichedData = { ...data };

    // 添加租户ID
    if (this.user && this.user.user_type === 'tenant_user' && this.user.tenant_id) {
      const tenantTables = [
        'tenant_users', 'flocks', 'inventories', 'health_records',
        'production_records', 'financial_records', 'file_uploads'
      ];

      if (tenantTables.includes(table)) {
        enrichedData.tenant_id = this.user.tenant_id;
      }
    }

    // 添加创建者ID
    if (this.user && this.user.id) {
      const tablesWithCreatedBy = [
        'flocks', 'inventories', 'health_records', 'production_records',
        'financial_records', 'platform_announcements', 'knowledge_base',
        'goose_prices', 'file_uploads'
      ];

      if (tablesWithCreatedBy.includes(table)) {
        enrichedData.created_by = this.user.id;
      }
    }

    // 添加时间戳
    if (!enrichedData.created_at) {
      enrichedData.created_at = new Date();
    }
    enrichedData.updated_at = new Date();

    return enrichedData;
  }

  /**
   * 记录查询日志
   * @param {string} operation - 操作类型
   * @param {string} table - 表名
   * @param {Object} params - 参数
   * @param {string} sql - SQL语句
   */
  _logQuery(operation, table, params, sql) {
    logger.debug('Database operation', {
      operation,
      table,
      sql: sql.substring(0, 200) + (sql.length > 200 ? '...' : ''),
      userId: this.user?.id,
      username: this.user?.username,
      tenantId: this.user?.tenant_id,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * 数据库中间件 - 为请求提供安全的数据库访问
 */
const secureDbMiddleware = (req, res, next) => {
  const user = req.admin || req.user;
  req.secureDb = new SecureDatabase(req.db, user);
  next();
};

module.exports = {
  SecureDatabase,
  secureDbMiddleware
};