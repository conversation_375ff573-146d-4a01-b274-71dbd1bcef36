/**
 * 统一响应格式中间件
 * 提供标准化的API响应格式
 */

/**
 * 成功响应格式化
 * @param {Object} res Express响应对象
 * @param {*} data 响应数据
 * @param {string} message 响应消息
 * @param {number} statusCode HTTP状态码
 */
function successResponse(
  res,
  data = null,
  message = '操作成功',
  statusCode = 200
) {
  const response = {
    success: true,
    data: data,
    message: message,
    timestamp: new Date().toISOString()
  };

  // 如果是列表数据且包含分页信息，特殊处理
  if (data && typeof data === 'object' && data.items && data.pagination) {
    response.data = {
      items: data.items,
      pagination: data.pagination
    };
  }

  return res.status(statusCode).json(response);
}

/**
 * 错误响应格式化
 * @param {Object} res Express响应对象
 * @param {string} message 错误消息
 * @param {number} statusCode HTTP状态码
 * @param {string} errorCode 错误代码
 * @param {Array} details 错误详情
 */
function errorResponse(
  res,
  message = '操作失败',
  statusCode = 500,
  errorCode = 'INTERNAL_ERROR',
  details = null
) {
  const response = {
    success: false,
    error: {
      code: errorCode,
      message: message,
      details: details
    },
    timestamp: new Date().toISOString()
  };

  return res.status(statusCode).json(response);
}

/**
 * 分页数据格式化
 * @param {Array} items 数据项
 * @param {number} page 当前页码
 * @param {number} limit 每页数量
 * @param {number} total 总数量
 */
function formatPaginatedData(items, page, limit, total) {
  const totalPages = Math.ceil(total / limit);

  return {
    items: items,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: parseInt(total),
      totalPages: totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  };
}

/**
 * 验证错误响应
 * @param {Object} res Express响应对象
 * @param {Array} errors 验证错误数组
 */
function validationErrorResponse(res, errors) {
  const details = errors.map((error) => ({
    field: error.path || error.field,
    message: error.message,
    value: error.value
  }));

  return errorResponse(
    res,
    '请求参数验证失败',
    400,
    'VALIDATION_ERROR',
    details
  );
}

/**
 * 认证错误响应
 * @param {Object} res Express响应对象
 * @param {string} message 错误消息
 */
function authenticationErrorResponse(res, message = '认证失败，请重新登录') {
  return errorResponse(res, message, 401, 'AUTHENTICATION_ERROR');
}

/**
 * 授权错误响应
 * @param {Object} res Express响应对象
 * @param {string} message 错误消息
 */
function authorizationErrorResponse(res, message = '权限不足，无法访问该资源') {
  return errorResponse(res, message, 403, 'AUTHORIZATION_ERROR');
}

/**
 * 资源不存在错误响应
 * @param {Object} res Express响应对象
 * @param {string} resource 资源名称
 */
function notFoundErrorResponse(res, resource = '资源') {
  return errorResponse(res, `${resource}不存在`, 404, 'NOT_FOUND_ERROR');
}

/**
 * 资源冲突错误响应
 * @param {Object} res Express响应对象
 * @param {string} message 错误消息
 */
function conflictErrorResponse(res, message = '资源冲突') {
  return errorResponse(res, message, 409, 'CONFLICT_ERROR');
}

/**
 * 响应中间件
 * 在res对象上添加便捷的响应方法
 */
function responseMiddleware(req, res, next) {
  // 成功响应方法
  res.success = (data, message, statusCode) => {
    return successResponse(res, data, message, statusCode);
  };

  // 创建成功响应
  res.created = (data, message = '创建成功') => {
    return successResponse(res, data, message, 201);
  };

  // 无内容响应
  res.noContent = (message = '操作成功') => {
    return res.status(204).json({
      success: true,
      message: message,
      timestamp: new Date().toISOString()
    });
  };

  // 分页响应
  res.paginated = (items, page, limit, total, message = '获取成功') => {
    const paginatedData = formatPaginatedData(items, page, limit, total);
    return successResponse(res, paginatedData, message);
  };

  // 错误响应方法
  res.error = (message, statusCode, errorCode, details) => {
    return errorResponse(res, message, statusCode, errorCode, details);
  };

  // 验证错误
  res.validationError = (errors) => {
    return validationErrorResponse(res, errors);
  };

  // 认证错误
  res.authenticationError = (message) => {
    return authenticationErrorResponse(res, message);
  };

  // 授权错误
  res.authorizationError = (message) => {
    return authorizationErrorResponse(res, message);
  };

  // 资源不存在
  res.notFound = (resource) => {
    return notFoundErrorResponse(res, resource);
  };

  // 资源冲突
  res.conflict = (message) => {
    return conflictErrorResponse(res, message);
  };

  // 服务器错误
  res.serverError = (message = '服务器内部错误') => {
    return errorResponse(res, message, 500, 'INTERNAL_ERROR');
  };

  next();
}

/**
 * 全局错误处理中间件
 * @param {Error} err 错误对象
 * @param {Object} req Express请求对象
 * @param {Object} res Express响应对象
 * @param {Function} next 下一个中间件
 */
function globalErrorHandler(err, req, res, next) {
  // 记录错误日志
  console.error('API Error:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });

  // 如果响应已经发送，交给默认错误处理器
  if (res.headersSent) {
    return next(err);
  }

  // 处理不同类型的错误
  if (err.name === 'ValidationError') {
    // Mongoose验证错误
    const errors = Object.values(err.errors).map((error) => ({
      field: error.path,
      message: error.message,
      value: error.value
    }));
    return res.validationError(errors);
  }

  if (err.name === 'CastError') {
    // Mongoose类型转换错误
    return res.error('无效的ID格式', 400, 'INVALID_ID');
  }

  if (err.code === 11000) {
    // MongoDB重复键错误
    const field = Object.keys(err.keyPattern)[0];
    return res.conflict(`${field}已存在`);
  }

  if (err.name === 'JsonWebTokenError') {
    // JWT错误
    return res.authenticationError('无效的访问令牌');
  }

  if (err.name === 'TokenExpiredError') {
    // JWT过期错误
    return res.authenticationError('访问令牌已过期');
  }

  // 默认服务器错误
  res.serverError(
    process.env.NODE_ENV === 'production' ? '服务器内部错误' : err.message
  );
}

module.exports = {
  responseMiddleware,
  globalErrorHandler,
  successResponse,
  errorResponse,
  formatPaginatedData,
  validationErrorResponse,
  authenticationErrorResponse,
  authorizationErrorResponse,
  notFoundErrorResponse,
  conflictErrorResponse
};
