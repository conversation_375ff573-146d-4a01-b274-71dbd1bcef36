const winston = require('winston');

// Logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

/**
 * 租户中间件
 * 提供多租户数据隔离和租户上下文管理
 */
class TenantMiddleware {
  /**
   * 从路径参数提取租户代码
   */
  static extractTenant(req, res, next) {
    try {
      const tenantCode = req.params.tenantCode;
      
      if (!tenantCode) {
        return res.status(400).json({
          success: false,
          message: '租户代码缺失',
          code: 'TENANT_CODE_MISSING'
        });
      }

      // 验证租户代码格式
      if (!/^[a-zA-Z0-9_-]+$/.test(tenantCode)) {
        return res.status(400).json({
          success: false,
          message: '租户代码格式无效',
          code: 'INVALID_TENANT_CODE'
        });
      }

      req.tenantCode = tenantCode;
      next();
    } catch (error) {
      logger.error('Extract tenant failed', {
        error: error.message,
        requestId: req.requestId
      });
      return res.status(500).json({
        success: false,
        message: '提取租户信息失败'
      });
    }
  }

  /**
   * 从认证信息提取租户（用于兼容性）
   */
  static extractTenantFromAuth(req, res, next) {
    try {
      if (req.tenant && req.tenant.tenantCode) {
        req.tenantCode = req.tenant.tenantCode;
      }
      next();
    } catch (error) {
      logger.error('Extract tenant from auth failed', {
        error: error.message,
        requestId: req.requestId
      });
      next();
    }
  }

  /**
   * 验证租户权限（确保用户只能访问自己租户的数据）
   */
  static validateTenantAccess(req, res, next) {
    try {
      const requestTenantCode = req.params.tenantCode || req.tenantCode;
      const userTenantCode = req.tenant ? req.tenant.tenantCode : null;

      // 平台管理员可以访问所有租户数据
      if (req.admin) {
        return next();
      }

      // 租户用户只能访问自己的数据
      if (!userTenantCode) {
        return res.status(401).json({
          success: false,
          message: '用户租户信息缺失',
          code: 'USER_TENANT_MISSING'
        });
      }

      if (requestTenantCode && requestTenantCode !== userTenantCode) {
        return res.status(403).json({
          success: false,
          message: '无权访问其他租户的数据',
          code: 'TENANT_ACCESS_DENIED'
        });
      }

      next();
    } catch (error) {
      logger.error('Validate tenant access failed', {
        error: error.message,
        requestId: req.requestId
      });
      return res.status(500).json({
        success: false,
        message: '验证租户权限失败'
      });
    }
  }

  /**
   * 租户数据隔离中间件
   * 自动在查询中添加tenant_id过滤条件
   */
  static enforceDataIsolation(req, res, next) {
    try {
      // 平台管理员不需要数据隔离
      if (req.admin) {
        return next();
      }

      if (!req.tenant || !req.tenant.id) {
        return res.status(401).json({
          success: false,
          message: '租户信息缺失',
          code: 'TENANT_INFO_MISSING'
        });
      }

      // 将租户ID添加到请求上下文
      req.tenantFilter = {
        tenant_id: req.tenant.id
      };

      next();
    } catch (error) {
      logger.error('Enforce data isolation failed', {
        error: error.message,
        requestId: req.requestId
      });
      return res.status(500).json({
        success: false,
        message: '数据隔离设置失败'
      });
    }
  }

  /**
   * 检查租户订阅状态
   */
  static async checkSubscriptionStatus(req, res, next) {
    try {
      if (!req.tenant) {
        return next();
      }

      // 检查租户状态
      if (req.tenant.status !== 'active') {
        return res.status(403).json({
          success: false,
          message: '租户账户已被暂停',
          code: 'TENANT_SUSPENDED'
        });
      }

      // 检查订阅状态（如果有订阅信息）
      if (req.tenant.subscriptionEndDate) {
        const now = new Date();
        const endDate = new Date(req.tenant.subscriptionEndDate);
        
        if (now > endDate) {
          return res.status(403).json({
            success: false,
            message: '租户订阅已过期',
            code: 'SUBSCRIPTION_EXPIRED'
          });
        }

        // 如果即将过期（7天内），添加警告头
        const daysLeft = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));
        if (daysLeft <= 7) {
          res.setHeader('X-Subscription-Warning', `订阅将在${daysLeft}天后过期`);
        }
      }

      next();
    } catch (error) {
      logger.error('Check subscription status failed', {
        error: error.message,
        requestId: req.requestId
      });
      return res.status(500).json({
        success: false,
        message: '检查订阅状态失败'
      });
    }
  }

  /**
   * 检查租户资源限制
   */
  static checkResourceLimits(resourceType) {
    return async (req, res, next) => {
      try {
        if (!req.tenant) {
          return next();
        }

        const limits = {
          flocks: req.tenant.maxFlocks,
          users: req.tenant.maxUsers,
          storage: req.tenant.maxStorageGb
        };

        if (limits[resourceType]) {
          // 这里可以添加具体的资源检查逻辑
          // 例如查询当前资源使用量并与限制比较
          const currentUsage = await getCurrentResourceUsage(req.db, req.tenant.id, resourceType);
          
          if (currentUsage >= limits[resourceType]) {
            return res.status(429).json({
              success: false,
              message: `已达到${resourceType}资源限制`,
              code: 'RESOURCE_LIMIT_EXCEEDED',
              limit: limits[resourceType],
              current: currentUsage
            });
          }
        }

        next();
      } catch (error) {
        logger.error('Check resource limits failed', {
          error: error.message,
          requestId: req.requestId
        });
        return res.status(500).json({
          success: false,
          message: '检查资源限制失败'
        });
      }
    };
  }

  /**
   * 租户上下文日志记录
   */
  static logTenantContext(req, res, next) {
    const originalSend = res.send;
    
    res.send = function(data) {
      // 记录租户操作日志
      logger.info('Tenant operation', {
        tenantId: req.tenant?.id,
        tenantCode: req.tenant?.tenantCode,
        userId: req.user?.id,
        method: req.method,
        path: req.path,
        statusCode: res.statusCode,
        requestId: req.requestId
      });

      return originalSend.call(this, data);
    };

    next();
  }
}

/**
 * 获取当前资源使用量（辅助函数）
 */
async function getCurrentResourceUsage(db, tenantId, resourceType) {
  try {
    let query;
    let params = [tenantId];

    switch (resourceType) {
      case 'flocks':
        query = 'SELECT COUNT(*) as count FROM flocks WHERE tenant_id = ? AND status = "active"';
        break;
      case 'users':
        query = 'SELECT COUNT(*) as count FROM tenant_users WHERE tenant_id = ? AND status = "active"';
        break;
      case 'storage':
        query = 'SELECT COALESCE(SUM(file_size), 0) as count FROM file_uploads WHERE tenant_id = ? AND status = "active"';
        break;
      default:
        return 0;
    }

    const result = await db.query(query, params);
    const data = Array.isArray(result) ? result[0] : result;
    
    if (resourceType === 'storage') {
      // 转换字节到GB
      return Math.ceil(data.count / (1024 * 1024 * 1024));
    }
    
    return data.count || 0;
  } catch (error) {
    logger.error('Get resource usage failed', {
      tenantId,
      resourceType,
      error: error.message
    });
    return 0;
  }
}

module.exports = TenantMiddleware;