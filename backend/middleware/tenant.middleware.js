/**
 * 租户中间件 - 处理多租户请求路由和权限控制
 * Tenant Middleware - Handle multi-tenant request routing and access control
 */

const jwt = require('jsonwebtoken');
const tenantDatabaseManager = require('../models/tenant-database.model');

/**
 * 租户识别中间件
 * 从请求头或子域名中识别租户信息
 */
const identifyTenant = async (req, res, next) => {
  try {
    let tenantCode = null;

    // 方法1: 从请求头获取租户代码
    if (req.headers['x-tenant-code']) {
      tenantCode = req.headers['x-tenant-code'];
    }

    // 方法2: 从子域名获取租户代码
    if (!tenantCode && req.get('host')) {
      const host = req.get('host');
      const parts = host.split('.');
      if (parts.length > 2 && parts[0] !== 'www') {
        tenantCode = parts[0].toUpperCase();
      }
    }

    // 方法3: 从URL路径参数获取
    if (!tenantCode && req.params.tenantCode) {
      tenantCode = req.params.tenantCode.toUpperCase();
    }

    // 方法4: 从查询参数获取
    if (!tenantCode && req.query.tenant) {
      tenantCode = req.query.tenant.toUpperCase();
    }

    if (!tenantCode) {
      return res.status(400).json({
        success: false,
        message: '缺少租户标识',
        code: 'TENANT_CODE_MISSING'
      });
    }

    // 验证租户是否存在且有效
    const tenantInfo = await validateTenant(tenantCode);
    if (!tenantInfo) {
      return res.status(404).json({
        success: false,
        message: '租户不存在或已停用',
        code: 'TENANT_NOT_FOUND'
      });
    }

    // 设置租户信息到请求对象
    req.tenant = tenantInfo;
    req.tenantCode = tenantCode;

    // 获取或创建租户数据库连接
    const connection =
      await tenantDatabaseManager.createTenantConnection(tenantInfo);
    req.tenantDb = connection;

    next();
  } catch (error) {
    console.error('租户识别失败:', error);
    return res.status(500).json({
      success: false,
      message: '租户识别失败',
      code: 'TENANT_IDENTIFICATION_ERROR'
    });
  }
};

/**
 * 验证租户信息
 * @param {string} tenantCode 租户代码
 * @returns {Object|null} 租户信息
 */
async function validateTenant(tenantCode) {
  try {
    const saasDb = tenantDatabaseManager.getSaasConnection();
    if (!saasDb) {
      await tenantDatabaseManager.initSaasConnection();
    }

    const [results] = await saasDb.query(
      'SELECT * FROM tenants WHERE tenantCode = ? AND status = "active"',
      {
        replacements: [tenantCode],
        type: saasDb.QueryTypes.SELECT
      }
    );

    return results.length > 0 ? results[0] : null;
  } catch (error) {
    console.error('验证租户失败:', error);
    return null;
  }
}

/**
 * 租户权限验证中间件
 * @param {Array} allowedPlans 允许的订阅计划
 * @param {Array} allowedFeatures 要求的功能权限
 */
const requireTenantPermission = (allowedPlans = [], allowedFeatures = []) => {
  return (req, res, next) => {
    try {
      const tenant = req.tenant;

      if (!tenant) {
        return res.status(401).json({
          success: false,
          message: '租户信息缺失',
          code: 'TENANT_INFO_MISSING'
        });
      }

      // 检查订阅计划
      if (
        allowedPlans.length > 0 &&
        !allowedPlans.includes(tenant.subscriptionPlan)
      ) {
        return res.status(403).json({
          success: false,
          message: '当前订阅计划不支持此功能',
          code: 'SUBSCRIPTION_PLAN_INSUFFICIENT',
          requiredPlans: allowedPlans,
          currentPlan: tenant.subscriptionPlan
        });
      }

      // 检查功能权限
      if (allowedFeatures.length > 0) {
        const tenantFeatures = tenant.features
          ? JSON.parse(tenant.features)
          : [];
        const hasRequiredFeatures = allowedFeatures.every((feature) =>
          tenantFeatures.includes(feature)
        );

        if (!hasRequiredFeatures) {
          return res.status(403).json({
            success: false,
            message: '租户缺少必需的功能权限',
            code: 'FEATURE_PERMISSION_DENIED',
            requiredFeatures: allowedFeatures,
            tenantFeatures: tenantFeatures
          });
        }
      }

      // 检查订阅是否过期
      if (
        tenant.subscriptionEndDate &&
        new Date(tenant.subscriptionEndDate) < new Date()
      ) {
        return res.status(403).json({
          success: false,
          message: '订阅已过期，请续费后继续使用',
          code: 'SUBSCRIPTION_EXPIRED',
          expiredDate: tenant.subscriptionEndDate
        });
      }

      next();
    } catch (error) {
      console.error('租户权限验证失败:', error);
      return res.status(500).json({
        success: false,
        message: '权限验证失败',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
};

/**
 * 租户用户认证中间件
 */
const authenticateTenantUser = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '缺少认证令牌',
        code: 'TOKEN_MISSING'
      });
    }

    // 验证JWT令牌
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || (() => { throw new Error('JWT_SECRET environment variable is required'); })()
    );

    // 验证用户是否属于当前租户
    if (decoded.tenantId !== req.tenant.id) {
      return res.status(403).json({
        success: false,
        message: '用户不属于当前租户',
        code: 'TENANT_USER_MISMATCH'
      });
    }

    // 从租户数据库获取用户信息
    const [userResults] = await req.tenantDb.query(
      'SELECT * FROM users WHERE id = ? AND status = "active"',
      {
        replacements: [decoded.userId],
        type: req.tenantDb.QueryTypes.SELECT
      }
    );

    if (userResults.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户不存在或已停用',
        code: 'USER_NOT_FOUND'
      });
    }

    req.user = userResults[0];
    req.userId = userResults[0].id;

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: '无效的认证令牌',
        code: 'INVALID_TOKEN'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '认证令牌已过期',
        code: 'TOKEN_EXPIRED'
      });
    }

    console.error('租户用户认证失败:', error);
    return res.status(500).json({
      success: false,
      message: '认证失败',
      code: 'AUTHENTICATION_ERROR'
    });
  }
};

/**
 * 租户API限流中间件
 */
const rateLimitTenant = (req, res, next) => {
  try {
    const tenant = req.tenant;

    if (!tenant) {
      return next();
    }

    // 这里可以实现基于租户的API限流逻辑
    // 例如根据订阅计划设置不同的限流规则
    const rateLimits = {
      trial: 100, // 试用版: 100次/小时
      basic: 1000, // 基础版: 1000次/小时
      standard: 5000, // 标准版: 5000次/小时
      premium: 20000, // 高级版: 20000次/小时
      enterprise: -1 // 企业版: 无限制
    };

    const limit = rateLimits[tenant.subscriptionPlan] || 100;

    if (limit > 0) {
      // 这里应该实现真实的限流逻辑，例如使用Redis
      // 现在暂时跳过，后续可以集成Redis或其他限流方案
    }

    next();
  } catch (error) {
    console.error('租户限流检查失败:', error);
    next(); // 限流失败不阻断请求
  }
};

/**
 * 记录租户API使用统计
 */
const recordTenantUsage = async (req, res, next) => {
  // 在响应结束后记录使用统计
  const originalSend = res.send;
  res.send = function (data) {
    // 异步记录使用统计，不阻塞响应
    setImmediate(async () => {
      try {
        if (req.tenant) {
          await recordApiUsage(
            req.tenant.id,
            req.method,
            req.path,
            res.statusCode
          );
        }
      } catch (error) {
        console.error('记录API使用统计失败:', error);
      }
    });

    return originalSend.call(this, data);
  };

  next();
};

/**
 * 记录API使用情况
 * @param {number} tenantId 租户ID
 * @param {string} method HTTP方法
 * @param {string} path 请求路径
 * @param {number} statusCode 状态码
 */
async function recordApiUsage(tenantId, method, path, statusCode) {
  try {
    const saasDb = tenantDatabaseManager.getSaasConnection();
    if (!saasDb) return;

    const today = new Date().toISOString().split('T')[0];

    // 更新或插入使用统计
    await saasDb.query(
      `
      INSERT INTO platform_usage_stats (tenantId, statDate, apiCalls, dataOperations) 
      VALUES (?, ?, 1, 1)
      ON DUPLICATE KEY UPDATE 
      apiCalls = apiCalls + 1,
      dataOperations = dataOperations + 1
    `,
      {
        replacements: [tenantId, today]
      }
    );
  } catch (error) {
    console.error('记录API使用统计失败:', error);
  }
}

module.exports = {
  identifyTenant,
  requireTenantPermission,
  authenticateTenantUser,
  rateLimitTenant,
  recordTenantUsage,
  validateTenant
};
