require('dotenv').config();
const { Sequelize } = require('sequelize');

// 根据环境选择配置
const env = process.env.NODE_ENV || 'development';
const config = require('./config.json')[env];

// 如果是本地环境但没有配置，则使用本地配置
const dbConfig = config || require('./config.json').local;

// 创建Sequelize实例
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    logging: dbConfig.logging,
    pool: dbConfig.pool
  }
);

// 测试数据库连接
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    return false;
  }
};

// 同步数据库模型
const syncDatabase = async (force = false) => {
  try {
    await sequelize.sync({ force });
    return true;
  } catch (error) {
    console.error('❌ 数据库模型同步失败:', error.message);
    return false;
  }
};

module.exports = {
  sequelize,
  testConnection,
  syncDatabase
};
