/**
 * SAAS管理系统深度探索脚本
 * 使用Playwright自动化点击每个菜单项，检测缺失的页面和功能
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

class AdminExplorer {
  constructor() {
    this.browser = null;
    this.page = null;
    this.findings = {
      missingPages: [],
      errors: [],
      menuStructure: [],
      databaseErrors: []
    };
  }

  async init() {
    console.log('🚀 启动管理系统探索器...');
    this.browser = await chromium.launch({ 
      headless: false, // 设置为false以便观察
      slowMo: 1000 // 慢速执行以便观察
    });
    this.page = await this.browser.newPage();
    
    // 监听页面错误
    this.page.on('pageerror', (error) => {
      this.findings.errors.push({
        type: 'page_error',
        message: error.message,
        url: this.page.url()
      });
    });

    // 监听控制台错误
    this.page.on('console', (msg) => {
      if (msg.type() === 'error') {
        this.findings.errors.push({
          type: 'console_error',
          message: msg.text(),
          url: this.page.url()
        });
      }
    });
  }

  async login() {
    console.log('🔐 正在登录管理系统...');
    await this.page.goto('http://localhost:3002/auth/login');
    
    // 等待页面加载
    await this.page.waitForTimeout(2000);
    
    // 填写登录信息
    await this.page.fill('input[name="username"]', 'admin');
    await this.page.fill('input[name="password"]', 'admin123456');
    
    // 点击登录
    await this.page.click('button[type="submit"]');
    
    // 等待页面响应
    try {
      await this.page.waitForResponse(response => response.url().includes('/auth/login'), { timeout: 10000 });
      await this.page.waitForTimeout(2000);
    } catch (error) {
      console.log('⚠️ 登录响应超时，继续探索...');
    }
    
    console.log('✅ 登录尝试完成，当前页面:', this.page.url());
  }

  async exploreMenu() {
    console.log('📋 开始探索菜单结构...');
    
    // 等待页面完全加载
    await this.page.waitForTimeout(2000);
    
    // 获取所有菜单项
    const menuItems = await this.page.locator('.main-sidebar .nav-link').all();
    console.log(`📝 发现 ${menuItems.length} 个菜单项`);
    
    for (let i = 0; i < menuItems.length; i++) {
      const menuItem = menuItems[i];
      
      try {
        // 获取菜单文本
        const menuText = await menuItem.textContent();
        const menuHref = await menuItem.getAttribute('href');
        
        console.log(`\n🔍 探索菜单: ${menuText?.trim()}`);
        
        // 检查是否有子菜单
        const hasSubMenu = await menuItem.locator('..').locator('.nav-treeview').count() > 0;
        
        if (hasSubMenu) {
          // 展开子菜单
          await menuItem.click();
          await this.page.waitForTimeout(500);
          
          // 获取子菜单项
          const subMenuItems = await menuItem.locator('..').locator('.nav-treeview .nav-link').all();
          
          for (const subMenuItem of subMenuItems) {
            await this.exploreMenuItem(subMenuItem, `${menuText?.trim()} > `);
          }
        } else {
          await this.exploreMenuItem(menuItem);
        }
        
      } catch (error) {
        this.findings.errors.push({
          type: 'menu_exploration_error',
          message: error.message,
          menuIndex: i
        });
      }
    }
  }

  async exploreMenuItem(menuItem, parentPath = '') {
    try {
      const menuText = await menuItem.textContent();
      const menuHref = await menuItem.getAttribute('href');
      const fullPath = parentPath + menuText?.trim();
      
      console.log(`  📄 点击: ${fullPath}`);
      
      // 点击菜单项
      await menuItem.click();
      await this.page.waitForTimeout(1500);
      
      const currentUrl = this.page.url();
      
      // 检查页面内容
      const pageContent = await this.page.content();
      
      // 检测404或错误页面
      if (pageContent.includes('404') || pageContent.includes('Not Found') || 
          pageContent.includes('页面不存在') || pageContent.includes('错误')) {
        this.findings.missingPages.push({
          menuPath: fullPath,
          url: currentUrl,
          href: menuHref,
          issue: '页面不存在或返回错误'
        });
      }
      
      // 检测数据库错误
      if (pageContent.includes('数据库') && (pageContent.includes('错误') || pageContent.includes('失败'))) {
        this.findings.databaseErrors.push({
          menuPath: fullPath,
          url: currentUrl,
          issue: '数据库相关错误'
        });
      }
      
      // 检测空白页面
      const mainContent = await this.page.locator('.content-wrapper').textContent();
      if (mainContent && mainContent.trim().length < 50) {
        this.findings.missingPages.push({
          menuPath: fullPath,
          url: currentUrl,
          href: menuHref,
          issue: '页面内容为空或极少'
        });
      }
      
      // 记录菜单结构
      this.findings.menuStructure.push({
        path: fullPath,
        url: currentUrl,
        href: menuHref,
        hasContent: mainContent ? mainContent.trim().length > 50 : false
      });
      
      // 截图保存
      const screenshotPath = `./screenshots/${fullPath.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')}.png`;
      await this.page.screenshot({ path: screenshotPath });
      
    } catch (error) {
      this.findings.errors.push({
        type: 'menu_item_error',
        message: error.message,
        menuPath: parentPath + menuText?.trim()
      });
    }
  }

  async generateReport() {
    console.log('\n📊 生成探索报告...');
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalMenuItems: this.findings.menuStructure.length,
        missingPages: this.findings.missingPages.length,
        errors: this.findings.errors.length,
        databaseErrors: this.findings.databaseErrors.length
      },
      findings: this.findings
    };
    
    // 创建截图目录
    if (!fs.existsSync('./screenshots')) {
      fs.mkdirSync('./screenshots');
    }
    
    // 保存报告
    fs.writeFileSync('./admin-exploration-report.json', JSON.stringify(report, null, 2));
    
    // 生成可读报告
    let readableReport = `# SAAS管理系统探索报告\n\n`;
    readableReport += `生成时间: ${new Date().toLocaleString()}\n\n`;
    readableReport += `## 📊 概览\n`;
    readableReport += `- 总菜单项数: ${report.summary.totalMenuItems}\n`;
    readableReport += `- 缺失页面数: ${report.summary.missingPages}\n`;
    readableReport += `- 错误数: ${report.summary.errors}\n`;
    readableReport += `- 数据库错误数: ${report.summary.databaseErrors}\n\n`;
    
    if (this.findings.missingPages.length > 0) {
      readableReport += `## ❌ 缺失或有问题的页面\n\n`;
      this.findings.missingPages.forEach((page, index) => {
        readableReport += `${index + 1}. **${page.menuPath}**\n`;
        readableReport += `   - URL: ${page.url}\n`;
        readableReport += `   - 问题: ${page.issue}\n\n`;
      });
    }
    
    if (this.findings.databaseErrors.length > 0) {
      readableReport += `## 🗄️ 数据库相关错误\n\n`;
      this.findings.databaseErrors.forEach((error, index) => {
        readableReport += `${index + 1}. **${error.menuPath}**\n`;
        readableReport += `   - URL: ${error.url}\n`;
        readableReport += `   - 问题: ${error.issue}\n\n`;
      });
    }
    
    if (this.findings.errors.length > 0) {
      readableReport += `## ⚠️ 技术错误\n\n`;
      this.findings.errors.forEach((error, index) => {
        readableReport += `${index + 1}. **${error.type}**\n`;
        readableReport += `   - 错误信息: ${error.message}\n`;
        if (error.url) readableReport += `   - URL: ${error.url}\n`;
        readableReport += `\n`;
      });
    }
    
    fs.writeFileSync('./admin-exploration-report.md', readableReport);
    
    console.log('✅ 报告已生成:');
    console.log('- JSON格式: ./admin-exploration-report.json');
    console.log('- Markdown格式: ./admin-exploration-report.md');
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async run() {
    try {
      await this.init();
      await this.login();
      await this.exploreMenu();
      await this.generateReport();
    } catch (error) {
      console.error('❌ 探索过程中出现错误:', error);
      this.findings.errors.push({
        type: 'critical_error',
        message: error.message
      });
      await this.generateReport();
    } finally {
      await this.close();
    }
  }
}

// 运行探索器
if (require.main === module) {
  const explorer = new AdminExplorer();
  explorer.run().then(() => {
    console.log('🎉 管理系统探索完成！');
  }).catch(error => {
    console.error('💥 探索失败:', error);
  });
}

module.exports = AdminExplorer;