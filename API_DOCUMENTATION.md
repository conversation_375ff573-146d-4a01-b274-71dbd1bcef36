# 智慧养鹅SaaS平台 - API接口规范文档

## 📋 目录
- [API概览](#api概览)
- [认证授权](#认证授权)
- [响应格式](#响应格式)
- [错误处理](#错误处理)
- [核心接口](#核心接口)
- [多租户API](#多租户api)
- [管理后台API](#管理后台api)
- [业务模块API](#业务模块api)

## 🌐 API概览

### 基础信息
- **基础URL**: `https://api.smartgoose.com`
- **当前版本**: v1.0
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 版本说明
```
/api/v1/    # 主要业务API (微信小程序使用)
/api/v2/    # 新版TypeScript API (逐步迁移)
/admin/     # 管理后台API
/platform/  # SaaS平台管理API
```

### 环境地址
```
开发环境: http://localhost:3000
测试环境: https://test-api.smartgoose.com  
生产环境: https://api.smartgoose.com
```

## 🔐 认证授权

### JWT Token认证
所有API请求需要在Header中携带JWT Token：

```http
Authorization: Bearer <jwt_token>
```

### 获取Token
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "code": "微信登录code",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL"
  }
}
```

**响应示例:**
```json
{
  "success": true,
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_string",
    "expiresIn": 604800,
    "user": {
      "id": 1,
      "nickname": "张三",
      "avatar": "https://...",
      "tenantId": "tenant_001"
    }
  },
  "timestamp": "2025-08-28T10:30:00.000Z"
}
```

### 权限等级
```
GUEST     = 0  # 游客
USER      = 1  # 普通用户
MANAGER   = 2  # 管理员
ADMIN     = 3  # 超级管理员
PLATFORM  = 4  # 平台管理员
```

## 📄 响应格式

### 标准响应结构
```json
{
  "success": boolean,
  "code": number,
  "message": "string",
  "data": object | array | null,
  "timestamp": "ISO8601时间戳",
  "requestId": "请求唯一标识"
}
```

### 分页响应结构
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [...],
    "pagination": {
      "current": 1,
      "pageSize": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

### HTTP状态码说明
```
200  成功
201  创建成功
400  请求参数错误
401  未授权
403  权限不足
404  资源不存在
409  资源冲突
422  数据验证失败
429  请求频率过高
500  服务器内部错误
503  服务不可用
```

## ❌ 错误处理

### 错误响应格式
```json
{
  "success": false,
  "code": 400,
  "message": "请求参数错误",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": [
      {
        "field": "email",
        "message": "邮箱格式不正确",
        "value": "invalid-email"
      }
    ]
  },
  "timestamp": "2025-08-28T10:30:00.000Z",
  "requestId": "req_123456789"
}
```

### 错误码定义
```javascript
// 通用错误码 (1000-1999)
1000: '请求参数错误'
1001: '缺少必需参数'
1002: '参数格式错误'
1003: '参数值超出范围'

// 认证错误码 (2000-2999)
2000: 'Token无效或已过期'
2001: '权限不足'
2002: '用户不存在'
2003: '密码错误'

// 业务错误码 (3000-3999)
3000: '鹅群不存在'
3001: '生产记录重复'
3002: '库存不足'
3003: '订单状态异常'

// 系统错误码 (5000-5999)
5000: '服务器内部错误'
5001: '数据库连接失败'
5002: '外部服务调用失败'
```

## 🏠 核心接口

### 用户管理
```http
# 获取用户信息
GET /api/v1/user/profile

# 更新用户信息  
PUT /api/v1/user/profile
{
  "nickname": "新昵称",
  "phone": "13800138000",
  "email": "<EMAIL>"
}

# 获取用户权限
GET /api/v1/user/permissions
```

### 首页数据
```http
# 获取首页概览数据
GET /api/v1/home/<USER>

# 响应示例
{
  "success": true,
  "data": {
    "stats": {
      "totalFlocks": 10,
      "totalGoose": 500,
      "healthyGoose": 485,
      "todayProduction": 120
    },
    "weather": {
      "temperature": 25,
      "humidity": 65,
      "condition": "sunny"
    },
    "recentRecords": [...],
    "announcements": [...]
  }
}
```

## 🏢 多租户API

### 租户管理
```http
# 获取租户信息
GET /api/v1/tenant/info

# 更新租户配置
PUT /api/v1/tenant/config
{
  "name": "租户名称",
  "settings": {
    "maxUsers": 100,
    "features": ["ai_diagnosis", "finance_analysis"]
  }
}

# 租户用户列表
GET /api/v1/tenant/users?page=1&pageSize=20
```

### 数据隔离
所有业务API都会自动应用租户数据隔离：
```http
# 自动添加租户过滤条件
GET /api/v1/flock/list
# SQL: SELECT * FROM flocks WHERE tenant_id = 'current_tenant'
```

## 🐣 业务模块API

### 鹅群管理
```http
# 鹅群列表
GET /api/v1/flock/list?status=active&page=1

# 创建鹅群
POST /api/v1/flock/create
{
  "name": "A区1号鹅群",
  "breed": "狮头鹅",
  "count": 50,
  "location": "A区1号棚",
  "manager": "张三"
}

# 鹅群详情
GET /api/v1/flock/:id

# 更新鹅群
PUT /api/v1/flock/:id

# 删除鹅群 (软删除)
DELETE /api/v1/flock/:id
```

### 健康管理
```http
# 健康记录列表
GET /api/v1/health/records?flockId=1&startDate=2025-01-01

# 创建健康记录
POST /api/v1/health/records
{
  "flockId": 1,
  "type": "health_check",
  "status": "healthy",
  "checkDate": "2025-08-28",
  "notes": "整体状况良好",
  "images": ["url1", "url2"]
}

# AI健康诊断
POST /api/v1/health/ai-diagnosis
{
  "symptoms": ["食欲不振", "精神萎靡"],
  "images": ["symptom_image_url"],
  "flockId": 1
}
```

### 生产管理
```http
# 生产记录列表
GET /api/v1/production/records

# 创建生产记录
POST /api/v1/production/records
{
  "flockId": 1,
  "date": "2025-08-28",
  "eggCount": 45,
  "feedConsumption": 25.5,
  "weather": "sunny",
  "notes": "正常产蛋"
}

# 生产统计
GET /api/v1/production/statistics?period=month
```

### 商城管理
```http
# 商品列表
GET /api/v1/shop/products?category=feed&page=1

# 商品详情
GET /api/v1/shop/products/:id

# 添加到购物车
POST /api/v1/shop/cart/add
{
  "productId": 1,
  "quantity": 2,
  "specifications": "20kg装"
}

# 购物车列表
GET /api/v1/shop/cart

# 创建订单
POST /api/v1/shop/orders
{
  "items": [...],
  "address": {...},
  "paymentMethod": "wechat_pay"
}
```

### OA办公模块
```http
# 费用申请
POST /api/v1/workspace/expense/apply
{
  "amount": 1000,
  "category": "feed_purchase",
  "description": "采购饲料",
  "attachments": ["receipt_url"]
}

# 审批列表
GET /api/v1/workspace/approval/pending

# 审批操作
POST /api/v1/workspace/approval/:id/approve
{
  "action": "approve", // approve, reject
  "comment": "同意申请"
}
```

## ⚙️ 管理后台API

### 租户管理
```http
# 租户列表
GET /admin/tenants?status=active&page=1

# 创建租户
POST /admin/tenants
{
  "name": "新农场",
  "plan": "premium",
  "maxUsers": 100,
  "features": ["ai", "finance"],
  "contact": {
    "name": "张三",
    "phone": "13800138000",
    "email": "<EMAIL>"
  }
}

# 租户详情
GET /admin/tenants/:id

# 暂停/激活租户
PUT /admin/tenants/:id/status
{
  "status": "suspended", // active, suspended
  "reason": "欠费停用"
}
```

### 系统监控
```http
# 系统状态
GET /admin/system/status

# 响应示例
{
  "success": true,
  "data": {
    "database": {
      "status": "healthy",
      "responseTime": 25,
      "connections": 15
    },
    "memory": {
      "used": "256MB",
      "available": "1024MB",
      "usage": 25
    },
    "api": {
      "totalRequests": 10000,
      "avgResponseTime": 150,
      "errorRate": 0.1
    }
  }
}

# 性能指标
GET /admin/system/metrics?period=24h

# 错误日志
GET /admin/system/logs?level=error&page=1
```

## 🔍 请求示例

### cURL示例
```bash
# 获取鹅群列表
curl -X GET \
  'https://api.smartgoose.com/api/v1/flock/list?page=1&pageSize=10' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: application/json'

# 创建生产记录
curl -X POST \
  'https://api.smartgoose.com/api/v1/production/records' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "flockId": 1,
    "date": "2025-08-28",
    "eggCount": 45,
    "feedConsumption": 25.5
  }'
```

### 微信小程序示例
```javascript
// 封装的API客户端
const apiClient = require('../../utils/unified-api-client.js');

// 获取鹅群列表
apiClient.get('/flock/list', {
  page: 1,
  pageSize: 10
}).then(res => {
  if (res.success) {
    console.log('鹅群数据:', res.data);
  }
}).catch(err => {
  console.error('请求失败:', err);
});

// 创建生产记录
apiClient.post('/production/records', {
  flockId: 1,
  date: '2025-08-28',
  eggCount: 45,
  feedConsumption: 25.5
}).then(res => {
  wx.showToast({
    title: '记录创建成功',
    icon: 'success'
  });
});
```

## 📝 开发规范

### 请求规范
1. **HTTP方法**: 遵循RESTful规范
   - GET: 查询数据
   - POST: 创建数据
   - PUT: 更新数据
   - DELETE: 删除数据

2. **URL命名**: 使用复数名词，小写字母，连接符用-
   ```
   ✅ /api/v1/flock-records
   ❌ /api/v1/FlockRecord
   ```

3. **参数传递**:
   - Query参数: 用于过滤、分页、排序
   - Path参数: 用于资源标识
   - Body参数: 用于数据提交

### 响应规范
1. 统一使用标准响应格式
2. 时间格式使用ISO8601
3. 金额使用分为单位
4. 布尔值使用true/false

### 安全规范
1. 所有API都需要认证
2. 敏感数据加密传输
3. 实施请求频率限制
4. 记录操作审计日志

## 🧪 测试工具

### Postman集合
项目提供了完整的Postman API测试集合，包含：
- 环境变量配置
- 认证Token自动刷新
- 完整的接口测试用例
- 自动化测试脚本

### API测试脚本
```bash
# 运行API集成测试
npm run test:api

# 生成API文档
npm run docs:generate

# 验证API响应格式
npm run api:validate
```

---

**文档版本**: v1.0  
**更新时间**: 2025-08-28  
**维护团队**: 智慧养鹅开发团队

如有疑问，请联系技术支持：<EMAIL>