{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.rulers": [100], "editor.insertSpaces": true, "editor.tabSize": 2, "editor.detectIndentation": false, "files.associations": {"*.wxml": "xml", "*.wxss": "css", "*.wxs": "javascript"}, "files.exclude": {"**/node_modules": true, "**/miniprogram_npm": true, "**/.DS_Store": true, "**/logs": true, "**/*.log": true}, "search.exclude": {"**/node_modules": true, "**/miniprogram_npm": true, "**/logs": true}, "emmet.includeLanguages": {"wxml": "html"}, "wechat-miniprogram.formatOnSave": true, "wechat-miniprogram.lintOnSave": true, "minapp-vscode.documentSelector": ["wxml", "wxss"], "eslint.validate": ["javascript", "javascriptreact"], "json.schemas": [{"fileMatch": ["app.json"], "url": "https://dldir1.qq.com/WechatWebDev/plugins/editor/json-schema/app.json"}, {"fileMatch": ["project.config.json"], "url": "https://dldir1.qq.com/WechatWebDev/plugins/editor/json-schema/project.config.json"}, {"fileMatch": [".project-standards.json"], "schema": {"type": "object", "properties": {"projectName": {"type": "string"}, "version": {"type": "string"}, "standards": {"type": "object", "properties": {"enforceContext7Query": {"type": "boolean"}, "requireSequentialThinking": {"type": "boolean"}, "enforceConsistency": {"type": "boolean"}, "mandatoryBestPractices": {"type": "boolean"}}}}}}], "typescript.preferences.includePackageJsonAutoImports": "on", "javascript.preferences.includePackageJsonAutoImports": "on", "git.ignoreLimitWarning": true, "git.autofetch": true, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.wordWrap": "wordWrapColumn", "editor.wordWrapColumn": 100}, "prettier.configPath": ".prettier<PERSON>", "prettier.ignorePath": ".prettieri<PERSON>re", "todo-tree.general.tags": ["BUG", "HACK", "FIXME", "TODO", "XXX", "[ ]", "[x]"], "todo-tree.highlights.defaultHighlight": {"icon": "alert", "type": "tag", "foreground": "red", "background": "white", "opacity": 50, "iconColour": "blue"}, "todo-tree.highlights.customHighlight": {"TODO": {"icon": "check", "foreground": "blue"}, "FIXME": {"icon": "bug", "foreground": "red"}, "BUG": {"icon": "bug", "foreground": "red"}}, "kiroAgent.configureMCP": "Disabled"}