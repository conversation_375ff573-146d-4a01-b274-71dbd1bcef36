name: Quality Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  quality-check:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16.x, 18.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint
    
    - name: Run consistency check
      run: npm run check:consistency
    
    - name: Run standards check
      run: npm run check:standards
    
    - name: Run tests
      run: npm test
    
    - name: Run integration tests
      run: npm run test:integration
      continue-on-error: true
    
    - name: Generate quality report
      run: |
        echo "## Quality Check Report" >> $GITHUB_STEP_SUMMARY
        echo "### ✅ Linting: Passed" >> $GITHUB_STEP_SUMMARY
        echo "### ✅ Consistency Check: Passed" >> $GITHUB_STEP_SUMMARY
        echo "### ✅ Standards Check: Passed" >> $GITHUB_STEP_SUMMARY
        echo "### ✅ Unit Tests: Passed" >> $GITHUB_STEP_SUMMARY