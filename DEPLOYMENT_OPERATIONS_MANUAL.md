# 智慧养鹅SaaS平台 - 生产部署和运维手册

## 📋 目录
- [部署准备](#部署准备)
- [环境配置](#环境配置)
- [数据库设置](#数据库设置)
- [应用部署](#应用部署)
- [监控配置](#监控配置)
- [运维管理](#运维管理)
- [故障排除](#故障排除)
- [备份恢复](#备份恢复)

## 🚀 部署准备

### 系统要求
```
操作系统: Ubuntu 20.04+ / CentOS 7+ / Docker
CPU:     4核心或以上
内存:    8GB或以上 (建议16GB)
磁盘:    100GB或以上 SSD
网络:    公网IP，开放端口 80, 443, 3000-3003
```

### 软件依赖
```bash
# Node.js环境
Node.js >= 16.0.0
npm >= 8.0.0

# 数据库
MySQL >= 8.0
Redis >= 6.0 (可选，用于缓存)

# 进程管理
PM2 >= 5.0

# Web服务器
Nginx >= 1.18 (反向代理)

# 监控工具
htop, iotop, nethogs
```

### 预安装脚本
```bash
#!/bin/bash
# 服务器环境准备脚本

echo "🔧 开始配置服务器环境..."

# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装MySQL
sudo apt install mysql-server -y
sudo mysql_secure_installation

# 安装Redis (可选)
sudo apt install redis-server -y

# 安装Nginx
sudo apt install nginx -y

# 安装PM2
sudo npm install -g pm2

# 创建应用用户
sudo adduser --system --group smartgoose

# 创建应用目录
sudo mkdir -p /opt/smartgoose
sudo chown -R smartgoose:smartgoose /opt/smartgoose

echo "✅ 服务器环境配置完成"
```

## ⚙️ 环境配置

### 1. 创建生产环境配置
```bash
# 复制环境配置模板
cp .env.template .env.production

# 编辑生产配置
vim .env.production
```

### 2. 生产环境配置示例
```bash
# 生产环境配置
NODE_ENV=production
PORT=3000
ADMIN_PORT=3002
SAAS_PORT=3003

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=smartgoose_prod
DB_PASSWORD=super_secure_password_here
DB_NAME=smartgoose_production
DB_SSL=false

# JWT配置
JWT_SECRET=production_jwt_secret_key_very_long_and_secure
JWT_EXPIRES_IN=7d

# 微信配置
WECHAT_APPID=wx68a4d75dd80665ec
WECHAT_SECRET=your_production_wechat_secret

# 安全配置
ALLOWED_ORIGINS=https://yourdomain.com,https://admin.yourdomain.com
RATE_LIMIT_MAX=1000
RATE_LIMIT_WINDOW=900000

# 监控配置
MONITORING_ENABLED=true
LOG_LEVEL=warn
```

### 3. 数据库用户和权限设置
```sql
-- 创建生产数据库用户
CREATE USER 'smartgoose_prod'@'localhost' IDENTIFIED BY 'super_secure_password_here';

-- 创建生产数据库
CREATE DATABASE smartgoose_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 授权
GRANT ALL PRIVILEGES ON smartgoose_production.* TO 'smartgoose_prod'@'localhost';
FLUSH PRIVILEGES;

-- 验证权限
SHOW GRANTS FOR 'smartgoose_prod'@'localhost';
```

## 🗄️ 数据库设置

### 1. 执行数据库初始化
```bash
# 运行数据库迁移
NODE_ENV=production npm run db:init

# 导入初始数据
NODE_ENV=production npm run db:seed
```

### 2. 数据库优化配置 (`/etc/mysql/mysql.conf.d/mysqld.cnf`)
```ini
[mysqld]
# 基础配置
max_connections = 500
max_user_connections = 450
thread_cache_size = 50
table_open_cache = 4000

# InnoDB配置
innodb_buffer_pool_size = 4G
innodb_log_file_size = 512M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# 查询优化
query_cache_size = 256M
query_cache_type = 1
tmp_table_size = 256M
max_heap_table_size = 256M

# 字符集
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 二进制日志
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7
```

### 3. 数据库备份脚本
```bash
#!/bin/bash
# 数据库备份脚本

BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="smartgoose_production"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 全量备份
mysqldump -u smartgoose_prod -p$DB_PASSWORD \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  $DB_NAME > $BACKUP_DIR/full_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/full_backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "✅ 数据库备份完成: full_backup_$DATE.sql.gz"
```

## 🚀 应用部署

### 1. 代码部署脚本
```bash
#!/bin/bash
# 应用部署脚本

APP_DIR="/opt/smartgoose"
REPO_URL="https://github.com/yourorg/smartgoose-saas.git"
BRANCH="main"

echo "🚀 开始部署智慧养鹅SaaS平台..."

# 切换到应用目录
cd $APP_DIR

# 备份当前版本
if [ -d "current" ]; then
    cp -r current backup_$(date +%Y%m%d_%H%M%S)
fi

# 拉取最新代码
git pull origin $BRANCH || {
    echo "❌ 代码拉取失败"
    exit 1
}

# 安装依赖 (仅生产依赖)
npm ci --production || {
    echo "❌ 依赖安装失败" 
    exit 1
}

# 执行清理脚本
node scripts/production-cleanup-safe.js

# 数据库迁移
NODE_ENV=production npm run db:migrate || {
    echo "❌ 数据库迁移失败"
    exit 1
}

# 重启应用服务
pm2 restart ecosystem.config.js --env production || {
    echo "❌ 应用重启失败"
    exit 1
}

echo "✅ 应用部署完成"

# 健康检查
sleep 10
curl -f http://localhost:3000/health || {
    echo "⚠️ 健康检查失败，请检查应用状态"
    exit 1
}

echo "✅ 部署验证通过"
```

### 2. PM2配置 (`ecosystem.config.js`)
```javascript
module.exports = {
  apps: [
    {
      name: 'smartgoose-api',
      script: 'backend/app.js',
      cwd: '/opt/smartgoose',
      instances: 4, // 4个进程实例
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'development'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: '/var/log/smartgoose/api-error.log',
      out_file: '/var/log/smartgoose/api-out.log',
      log_file: '/var/log/smartgoose/api-combined.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024',
      kill_timeout: 5000,
      listen_timeout: 10000,
      autorestart: true,
      max_restarts: 5,
      min_uptime: '30s'
    },
    {
      name: 'smartgoose-admin',
      script: 'backend/admin/server.js',
      cwd: '/opt/smartgoose',
      instances: 2,
      exec_mode: 'cluster',
      env_production: {
        NODE_ENV: 'production',
        PORT: 3002
      },
      error_file: '/var/log/smartgoose/admin-error.log',
      out_file: '/var/log/smartgoose/admin-out.log',
      max_memory_restart: '512M'
    },
    {
      name: 'smartgoose-saas-admin',
      script: 'backend/saas-admin/server.js',
      cwd: '/opt/smartgoose',
      instances: 1,
      env_production: {
        NODE_ENV: 'production',
        PORT: 3003
      },
      error_file: '/var/log/smartgoose/saas-error.log',
      out_file: '/var/log/smartgoose/saas-out.log',
      max_memory_restart: '256M'
    }
  ]
};
```

### 3. Nginx配置 (`/etc/nginx/sites-available/smartgoose`)
```nginx
# 智慧养鹅SaaS平台 Nginx配置

# 上游服务定义
upstream smartgoose_api {
    least_conn;
    server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3001 max_fails=3 fail_timeout=30s backup;
    keepalive 32;
}

upstream smartgoose_admin {
    server 127.0.0.1:3002 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream smartgoose_saas {
    server 127.0.0.1:3003 max_fails=3 fail_timeout=30s;
    keepalive 8;
}

# API服务配置
server {
    listen 80;
    listen [::]:80;
    server_name api.smartgoose.com;
    
    # HTTPS重定向
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name api.smartgoose.com;

    # SSL证书
    ssl_certificate /etc/ssl/certs/smartgoose.pem;
    ssl_certificate_key /etc/ssl/private/smartgoose.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 日志
    access_log /var/log/nginx/smartgoose-api.access.log;
    error_log /var/log/nginx/smartgoose-api.error.log;

    # 限流
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    # 客户端配置
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # API代理
    location /api/ {
        proxy_pass http://smartgoose_api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
    }

    # 健康检查
    location /health {
        proxy_pass http://smartgoose_api;
        access_log off;
    }

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# 管理后台配置
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name admin.smartgoose.com;

    ssl_certificate /etc/ssl/certs/smartgoose.pem;
    ssl_certificate_key /etc/ssl/private/smartgoose.key;

    # 基础认证 (可选)
    auth_basic "Smart Goose Admin";
    auth_basic_user_file /etc/nginx/.htpasswd;

    location / {
        proxy_pass http://smartgoose_admin;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📊 监控配置

### 1. 集成生产监控
```javascript
// 在主应用中集成监控
const ProductionMonitor = require('./utils/production-monitor');

const monitor = new ProductionMonitor({
  healthCheckInterval: 30000,
  slowThreshold: 1000,
  errorThreshold: 10,
  webhookUrl: process.env.ALERT_WEBHOOK_URL,
  endpoints: [
    { name: 'database', url: 'http://localhost:3000/health/database' },
    { name: 'redis', url: 'http://localhost:3000/health/redis' }
  ]
});

// 使用监控中间件
app.use(monitor.middleware());
```

### 2. 日志轮转配置 (`/etc/logrotate.d/smartgoose`)
```
/var/log/smartgoose/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 smartgoose smartgoose
    postrotate
        pm2 reloadLogs
    endscript
}
```

### 3. 系统监控脚本
```bash
#!/bin/bash
# 系统资源监控脚本

LOG_FILE="/var/log/smartgoose/system-monitor.log"

# 获取系统信息
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.2f"), $3/$2 * 100.0}')
DISK_USAGE=$(df -h / | awk '/\// {print $(NF-1)}' | sed 's/%//')
LOAD_AVERAGE=$(uptime | awk -F'load average:' '{print $2}')

# 检查PM2进程状态
PM2_STATUS=$(pm2 jlist | jq -r '.[].pm2_env.status' | grep -c "online")

# 记录日志
echo "$(date '+%Y-%m-%d %H:%M:%S') CPU:${CPU_USAGE}% MEM:${MEMORY_USAGE}% DISK:${DISK_USAGE}% LOAD:${LOAD_AVERAGE} PM2:${PM2_STATUS}" >> $LOG_FILE

# 告警检查
if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    echo "⚠️ CPU使用率过高: ${CPU_USAGE}%"
fi

if (( $(echo "$MEMORY_USAGE > 85" | bc -l) )); then
    echo "⚠️ 内存使用率过高: ${MEMORY_USAGE}%"
fi

if [ "$DISK_USAGE" -gt 85 ]; then
    echo "⚠️ 磁盘使用率过高: ${DISK_USAGE}%"
fi
```

## 🔧 运维管理

### 1. 常用运维命令
```bash
# PM2管理
pm2 status              # 查看进程状态
pm2 logs smartgoose-api # 查看实时日志
pm2 monit              # 监控面板
pm2 restart all        # 重启所有进程
pm2 reload all         # 零停机重载

# 数据库管理
mysql -u root -p -e "SHOW PROCESSLIST;"  # 查看连接
mysql -u root -p -e "SHOW ENGINE INNODB STATUS\G"  # 查看状态

# 服务管理
systemctl status nginx  # Nginx状态
systemctl status mysql  # MySQL状态
systemctl restart smartgoose-api  # 重启API服务

# 日志查看
tail -f /var/log/smartgoose/api-combined.log  # 应用日志
tail -f /var/log/nginx/smartgoose-api.access.log  # 访问日志
journalctl -u smartgoose-api -f  # 系统日志
```

### 2. 性能调优脚本
```bash
#!/bin/bash
# 系统性能调优

echo "🔧 开始系统性能调优..."

# 内核参数优化
echo "调整内核参数..."
cat >> /etc/sysctl.conf << EOF
# 网络优化
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.core.rmem_default = 262144
net.core.wmem_default = 262144
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr

# 文件系统优化
fs.file-max = 1000000
fs.nr_open = 1000000
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
EOF

# 应用限制优化
echo "调整用户限制..."
cat >> /etc/security/limits.conf << EOF
smartgoose soft nofile 100000
smartgoose hard nofile 100000
smartgoose soft nproc 100000
smartgoose hard nproc 100000
EOF

# 重新加载配置
sysctl -p
echo "✅ 性能调优完成"
```

### 3. 自动化监控脚本
```bash
#!/bin/bash
# 自动化健康检查和恢复

# 配置
API_URL="http://localhost:3000/health"
ADMIN_URL="http://localhost:3002/health"
ALERT_EMAIL="<EMAIL>"

# 检查API服务
check_api() {
    if ! curl -f $API_URL > /dev/null 2>&1; then
        echo "❌ API服务异常，尝试重启..."
        pm2 restart smartgoose-api
        sleep 30
        
        if ! curl -f $API_URL > /dev/null 2>&1; then
            echo "❌ API服务重启失败，发送告警"
            echo "API服务异常" | mail -s "Smart Goose Alert" $ALERT_EMAIL
        else
            echo "✅ API服务恢复正常"
        fi
    else
        echo "✅ API服务正常"
    fi
}

# 检查管理后台
check_admin() {
    if ! curl -f $ADMIN_URL > /dev/null 2>&1; then
        echo "❌ 管理后台异常，尝试重启..."
        pm2 restart smartgoose-admin
        sleep 30
        
        if ! curl -f $ADMIN_URL > /dev/null 2>&1; then
            echo "❌ 管理后台重启失败，发送告警"
            echo "管理后台异常" | mail -s "Smart Goose Alert" $ALERT_EMAIL
        else
            echo "✅ 管理后台恢复正常"  
        fi
    else
        echo "✅ 管理后台正常"
    fi
}

# 执行检查
check_api
check_admin

# 清理日志 (保留30天)
find /var/log/smartgoose -name "*.log" -mtime +30 -delete
```

## 🚨 故障排除

### 常见问题和解决方案

#### 1. 应用启动失败
```bash
# 检查日志
pm2 logs smartgoose-api --lines 100

# 常见原因及解决方案
# - 端口被占用
lsof -i :3000
kill -9 <PID>

# - 数据库连接失败
mysql -u smartgoose_prod -p
# 检查用户权限和密码

# - 环境变量缺失
cat .env.production
# 检查必需的环境变量
```

#### 2. 内存泄漏问题
```bash
# 监控内存使用
pm2 monit

# 生成堆快照
kill -USR2 <node_pid>  # 如果配置了heapdump

# 重启高内存使用的进程
pm2 restart smartgoose-api
```

#### 3. 数据库连接池耗尽
```bash
# 检查数据库连接
mysql -u root -p -e "SHOW PROCESSLIST;"

# 检查慢查询
mysql -u root -p -e "SHOW FULL PROCESSLIST;" | grep -v "Sleep"

# 调整连接池配置
# 修改 backend/database/connection-optimized.js
```

#### 4. 高并发响应慢
```bash
# 检查系统负载
htop
iotop

# 检查Nginx访问日志
tail -f /var/log/nginx/smartgoose-api.access.log | grep " 5[0-9][0-9] "

# 调整PM2集群数量
pm2 scale smartgoose-api +2
```

## 💾 备份恢复

### 1. 完整备份脚本
```bash
#!/bin/bash
# 完整系统备份

BACKUP_BASE="/backup"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_BASE/full_backup_$DATE"

mkdir -p $BACKUP_DIR

echo "🗄️ 开始完整备份..."

# 应用代码备份
echo "备份应用代码..."
tar -czf $BACKUP_DIR/code.tar.gz -C /opt smartgoose/

# 数据库备份
echo "备份数据库..."
mysqldump -u smartgoose_prod -p$DB_PASSWORD \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  --all-databases > $BACKUP_DIR/database.sql

# 配置文件备份
echo "备份配置文件..."
cp -r /etc/nginx/sites-available $BACKUP_DIR/nginx_sites/
cp /etc/nginx/nginx.conf $BACKUP_DIR/nginx.conf
cp /opt/smartgoose/.env.production $BACKUP_DIR/env.production

# Nginx日志备份
echo "备份日志..."
tar -czf $BACKUP_DIR/logs.tar.gz /var/log/smartgoose/ /var/log/nginx/smartgoose*

# 压缩整个备份
echo "压缩备份..."
tar -czf "$BACKUP_BASE/smartgoose_full_backup_$DATE.tar.gz" -C $BACKUP_BASE "full_backup_$DATE"
rm -rf $BACKUP_DIR

# 上传到云存储 (可选)
if [ ! -z "$BACKUP_CLOUD_URL" ]; then
    echo "上传备份到云存储..."
    # aws s3 cp "$BACKUP_BASE/smartgoose_full_backup_$DATE.tar.gz" s3://your-backup-bucket/
fi

# 清理旧备份 (保留30天)
find $BACKUP_BASE -name "smartgoose_full_backup_*.tar.gz" -mtime +30 -delete

echo "✅ 完整备份完成: smartgoose_full_backup_$DATE.tar.gz"
```

### 2. 快速恢复脚本
```bash
#!/bin/bash
# 快速恢复脚本

BACKUP_FILE="$1"
RESTORE_DIR="/opt/smartgoose_restore"

if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <backup_file.tar.gz>"
    exit 1
fi

echo "🔄 开始系统恢复..."

# 停止服务
echo "停止服务..."
pm2 stop all
systemctl stop nginx

# 创建恢复目录
mkdir -p $RESTORE_DIR
cd $RESTORE_DIR

# 解压备份
echo "解压备份文件..."
tar -xzf $BACKUP_FILE

# 恢复数据库
echo "恢复数据库..."
mysql -u root -p < database.sql

# 恢复应用代码
echo "恢复应用代码..."
rm -rf /opt/smartgoose_old
mv /opt/smartgoose /opt/smartgoose_old
tar -xzf code.tar.gz -C /opt/

# 恢复配置
echo "恢复配置文件..."
cp nginx_sites/* /etc/nginx/sites-available/
cp nginx.conf /etc/nginx/
cp env.production /opt/smartgoose/.env.production

# 重新安装依赖
echo "安装依赖..."
cd /opt/smartgoose
npm ci --production

# 启动服务
echo "启动服务..."
systemctl start nginx
pm2 start ecosystem.config.js --env production

# 验证恢复
echo "验证服务状态..."
sleep 10
curl -f http://localhost:3000/health && echo "✅ API服务正常"
curl -f http://localhost:3002/health && echo "✅ 管理后台正常"

echo "✅ 系统恢复完成"
```

---

## 🎯 快速操作指南

### 日常运维检查清单
- [ ] 检查系统资源使用情况
- [ ] 查看应用错误日志
- [ ] 验证备份是否正常执行
- [ ] 检查SSL证书有效期
- [ ] 监控数据库性能指标
- [ ] 验证监控告警是否正常

### 紧急事件响应
1. **服务不可用**: 检查PM2状态 → 查看错误日志 → 重启服务
2. **数据库异常**: 检查连接数 → 查看慢查询 → 重启MySQL
3. **内存不足**: 检查进程内存 → 重启高内存进程 → 扩容服务器
4. **磁盘空间不足**: 清理日志 → 清理临时文件 → 扩容磁盘

### 性能优化建议
- 定期分析慢查询日志
- 监控内存使用趋势
- 调整PM2集群大小
- 优化Nginx缓存策略
- 定期清理过期数据

---

**手册版本**: v1.0  
**更新时间**: 2025-08-28  
**维护团队**: 智慧养鹅运维团队

📞 紧急联系方式: <EMAIL> | 400-xxx-xxxx