#!/usr/bin/env node
/**
 * 智慧养鹅SaaS平台 - 系统健康检查脚本
 * 独立运行，不依赖数据库连接，用于基础系统验证
 */

const fs = require('fs');
const path = require('path');
const http = require('http');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

class SystemHealthChecker {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      overall: 'UNKNOWN',
      checks: [],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        warnings: 0
      }
    };
  }

  /**
   * 执行所有健康检查
   */
  async runAllChecks() {
    console.log('🔍 智慧养鹅SaaS平台 - 系统健康检查');
    console.log('================================================\n');

    // 基础系统检查
    await this.checkNodeVersion();
    await this.checkNpmVersion();  
    await this.checkProjectStructure();
    await this.checkEnvironmentFiles();
    await this.checkPackageJson();
    await this.checkPortAvailability();
    await this.checkDiskSpace();
    await this.checkMemoryUsage();
    await this.checkLogDirectories();
    await this.checkScriptPermissions();

    // 生成报告
    this.generateReport();
    return this.results;
  }

  /**
   * Node.js版本检查
   */
  async checkNodeVersion() {
    try {
      const version = process.version;
      const majorVersion = parseInt(version.slice(1).split('.')[0]);
      
      if (majorVersion >= 16) {
        this.addResult('Node.js版本', 'PASS', `版本 ${version} (要求 >= 16.0.0)`, '✅');
      } else {
        this.addResult('Node.js版本', 'FAIL', `版本 ${version} 过低，要求 >= 16.0.0`, '❌');
      }
    } catch (error) {
      this.addResult('Node.js版本', 'ERROR', error.message, '⚠️');
    }
  }

  /**
   * npm版本检查
   */
  async checkNpmVersion() {
    try {
      const { stdout } = await execAsync('npm --version');
      const version = stdout.trim();
      const majorVersion = parseInt(version.split('.')[0]);
      
      if (majorVersion >= 8) {
        this.addResult('npm版本', 'PASS', `版本 ${version} (要求 >= 8.0.0)`, '✅');
      } else {
        this.addResult('npm版本', 'FAIL', `版本 ${version} 过低，要求 >= 8.0.0`, '❌');
      }
    } catch (error) {
      this.addResult('npm版本', 'ERROR', '无法获取npm版本: ' + error.message, '⚠️');
    }
  }

  /**
   * 项目结构检查
   */
  async checkProjectStructure() {
    const requiredPaths = [
      'backend/app.js',
      'backend/saas-admin/app.js', 
      'database/saas-platform-init.sql',
      'package.json',
      'scripts/',
      'pages/',
      'utils/'
    ];

    let missing = [];
    let found = [];

    for (const reqPath of requiredPaths) {
      const fullPath = path.join(process.cwd(), reqPath);
      if (fs.existsSync(fullPath)) {
        found.push(reqPath);
      } else {
        missing.push(reqPath);
      }
    }

    if (missing.length === 0) {
      this.addResult('项目结构', 'PASS', `所有必要文件存在 (${found.length}项)`, '✅');
    } else {
      this.addResult('项目结构', 'FAIL', `缺失文件: ${missing.join(', ')}`, '❌');
    }
  }

  /**
   * 环境配置文件检查
   */
  async checkEnvironmentFiles() {
    const envFiles = ['.env.production', '.env.template'];
    let results = [];

    for (const file of envFiles) {
      const exists = fs.existsSync(path.join(process.cwd(), file));
      if (exists) {
        try {
          const content = fs.readFileSync(path.join(process.cwd(), file), 'utf8');
          const lines = content.split('\\n').filter(l => l.trim() && !l.startsWith('#'));
          results.push(`${file}: ${lines.length} 配置项`);
        } catch (error) {
          results.push(`${file}: 读取错误`);
        }
      } else {
        results.push(`${file}: 缺失`);
      }
    }

    const hasProduction = fs.existsSync('.env.production');
    if (hasProduction) {
      this.addResult('环境配置', 'PASS', results.join(', '), '✅');
    } else {
      this.addResult('环境配置', 'WARN', '.env.production 缺失, ' + results.join(', '), '⚠️');
    }
  }

  /**
   * package.json检查
   */
  async checkPackageJson() {
    try {
      const packagePath = path.join(process.cwd(), 'package.json');
      if (!fs.existsSync(packagePath)) {
        this.addResult('package.json', 'FAIL', 'package.json 不存在', '❌');
        return;
      }

      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      
      // 检查必要字段
      const required = ['name', 'version', 'main', 'scripts', 'dependencies'];
      const missing = required.filter(field => !packageJson[field]);
      
      // 检查关键脚本
      const requiredScripts = ['start', 'start:all', 'test'];
      const missingScripts = requiredScripts.filter(script => 
        !packageJson.scripts || !packageJson.scripts[script]
      );

      // 检查关键依赖
      const keyDeps = ['express', 'sequelize', 'mysql2'];
      const missingDeps = keyDeps.filter(dep => 
        !packageJson.dependencies || !packageJson.dependencies[dep]
      );

      if (missing.length === 0 && missingScripts.length === 0 && missingDeps.length === 0) {
        this.addResult('package.json', 'PASS', 
          `完整配置 - ${Object.keys(packageJson.dependencies || {}).length} 个依赖`, '✅');
      } else {
        const issues = [];
        if (missing.length > 0) issues.push(`缺失字段: ${missing.join(',')}`);
        if (missingScripts.length > 0) issues.push(`缺失脚本: ${missingScripts.join(',')}`);
        if (missingDeps.length > 0) issues.push(`缺失依赖: ${missingDeps.join(',')}`);
        
        this.addResult('package.json', 'FAIL', issues.join('; '), '❌');
      }
    } catch (error) {
      this.addResult('package.json', 'ERROR', 'package.json 解析错误: ' + error.message, '⚠️');
    }
  }

  /**
   * 端口可用性检查
   */
  async checkPortAvailability() {
    const ports = [3000, 3002, 3003];
    let availablePorts = [];
    let occupiedPorts = [];

    for (const port of ports) {
      const isAvailable = await this.isPortAvailable(port);
      if (isAvailable) {
        availablePorts.push(port);
      } else {
        occupiedPorts.push(port);
      }
    }

    if (occupiedPorts.length === 0) {
      this.addResult('端口可用性', 'PASS', `所有端口可用: ${availablePorts.join(', ')}`, '✅');
    } else {
      this.addResult('端口可用性', 'WARN', 
        `占用端口: ${occupiedPorts.join(', ')}, 可用端口: ${availablePorts.join(', ')}`, '⚠️');
    }
  }

  /**
   * 检查端口是否可用
   */
  isPortAvailable(port) {
    return new Promise((resolve) => {
      const server = http.createServer();
      
      server.listen(port, () => {
        server.close(() => resolve(true));
      });
      
      server.on('error', () => resolve(false));
    });
  }

  /**
   * 磁盘空间检查
   */
  async checkDiskSpace() {
    try {
      const { stdout } = await execAsync('df -h .');
      const lines = stdout.trim().split('\\n');
      if (lines.length >= 2) {
        const parts = lines[1].split(/\\s+/);
        const usage = parts[4]; // 使用率，如 "45%"
        const usagePercent = parseInt(usage.replace('%', ''));
        
        if (usagePercent < 85) {
          this.addResult('磁盘空间', 'PASS', `使用率 ${usage}, 可用空间充足`, '✅');
        } else if (usagePercent < 95) {
          this.addResult('磁盘空间', 'WARN', `使用率 ${usage}, 空间紧张`, '⚠️');
        } else {
          this.addResult('磁盘空间', 'FAIL', `使用率 ${usage}, 磁盘空间不足`, '❌');
        }
      } else {
        this.addResult('磁盘空间', 'WARN', '无法解析磁盘使用率信息', '⚠️');
      }
    } catch (error) {
      this.addResult('磁盘空间', 'WARN', '无法检查磁盘空间: ' + error.message, '⚠️');
    }
  }

  /**
   * 内存使用检查
   */
  async checkMemoryUsage() {
    try {
      const totalMem = require('os').totalmem();
      const freeMem = require('os').freemem();
      const usedMem = totalMem - freeMem;
      const usagePercent = (usedMem / totalMem) * 100;
      
      const totalGB = (totalMem / 1024 / 1024 / 1024).toFixed(1);
      const usedGB = (usedMem / 1024 / 1024 / 1024).toFixed(1);
      const freeGB = (freeMem / 1024 / 1024 / 1024).toFixed(1);
      
      if (usagePercent < 80) {
        this.addResult('内存使用', 'PASS', 
          `使用 ${usedGB}GB / ${totalGB}GB (${usagePercent.toFixed(1)}%), 剩余 ${freeGB}GB`, '✅');
      } else if (usagePercent < 90) {
        this.addResult('内存使用', 'WARN',
          `使用 ${usedGB}GB / ${totalGB}GB (${usagePercent.toFixed(1)}%), 内存紧张`, '⚠️');
      } else {
        this.addResult('内存使用', 'FAIL',
          `使用 ${usedGB}GB / ${totalGB}GB (${usagePercent.toFixed(1)}%), 内存不足`, '❌');
      }
    } catch (error) {
      this.addResult('内存使用', 'ERROR', '内存检查失败: ' + error.message, '⚠️');
    }
  }

  /**
   * 日志目录检查
   */
  async checkLogDirectories() {
    const logDir = path.join(process.cwd(), 'logs');
    
    try {
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
      
      // 检查写权限
      const testFile = path.join(logDir, 'health-check-test.log');
      fs.writeFileSync(testFile, 'Health check test\\n');
      fs.unlinkSync(testFile);
      
      this.addResult('日志目录', 'PASS', 'logs/ 目录存在且可写入', '✅');
    } catch (error) {
      this.addResult('日志目录', 'FAIL', '日志目录创建或写入失败: ' + error.message, '❌');
    }
  }

  /**
   * 脚本执行权限检查
   */
  async checkScriptPermissions() {
    const scripts = [
      'scripts/start-dev.sh',
      'scripts/production-cleanup-safe.js',
      'start-local-dev.sh'
    ];

    let results = [];
    for (const script of scripts) {
      const scriptPath = path.join(process.cwd(), script);
      if (fs.existsSync(scriptPath)) {
        try {
          const stats = fs.statSync(scriptPath);
          const isExecutable = !!(stats.mode & parseInt('111', 8));
          results.push(`${script}: ${isExecutable ? '可执行' : '无执行权限'}`);
        } catch (error) {
          results.push(`${script}: 权限检查失败`);
        }
      } else {
        results.push(`${script}: 不存在`);
      }
    }

    const hasExecutableScripts = results.some(r => r.includes('可执行'));
    if (hasExecutableScripts) {
      this.addResult('脚本权限', 'PASS', results.join(', '), '✅');
    } else {
      this.addResult('脚本权限', 'WARN', '部分脚本无执行权限: ' + results.join(', '), '⚠️');
    }
  }

  /**
   * 添加检查结果
   */
  addResult(name, status, details, icon) {
    this.results.checks.push({
      name,
      status,
      details,
      icon
    });

    this.results.summary.total++;
    switch (status) {
      case 'PASS':
        this.results.summary.passed++;
        break;
      case 'FAIL':
      case 'ERROR':
        this.results.summary.failed++;
        break;
      case 'WARN':
        this.results.summary.warnings++;
        break;
    }

    // 控制台输出
    console.log(`${icon} ${name}: ${details}`);
  }

  /**
   * 生成最终报告
   */
  generateReport() {
    console.log('\\n================================================');
    
    // 确定整体状态
    if (this.results.summary.failed === 0) {
      if (this.results.summary.warnings === 0) {
        this.results.overall = 'HEALTHY';
        console.log('🎉 系统健康状态: 优秀');
      } else {
        this.results.overall = 'WARNING';
        console.log('⚠️  系统健康状态: 良好 (有警告)');
      }
    } else {
      this.results.overall = 'UNHEALTHY';
      console.log('❌ 系统健康状态: 异常');
    }

    // 汇总统计
    console.log(`\\n📊 检查汇总:`);
    console.log(`   总检查项: ${this.results.summary.total}`);
    console.log(`   ✅ 通过: ${this.results.summary.passed}`);
    console.log(`   ⚠️  警告: ${this.results.summary.warnings}`);
    console.log(`   ❌ 失败: ${this.results.summary.failed}`);

    // 建议行动
    if (this.results.summary.failed > 0) {
      console.log('\\n🔧 建议行动:');
      console.log('   1. 解决上述失败项目');
      console.log('   2. 重新运行健康检查');
      console.log('   3. 联系技术支持团队');
    } else if (this.results.summary.warnings > 0) {
      console.log('\\n💡 建议优化:');
      console.log('   1. 关注警告项目');
      console.log('   2. 建议在空闲时间处理');
      console.log('   3. 监控系统资源使用');
    }

    // 保存报告到文件
    const reportPath = path.join(process.cwd(), 'logs', 'health-check-report.json');
    try {
      fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
      console.log(`\\n📄 详细报告已保存到: ${reportPath}`);
    } catch (error) {
      console.log(`\\n⚠️  报告保存失败: ${error.message}`);
    }

    console.log('\\n================================================');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const checker = new SystemHealthChecker();
  checker.runAllChecks()
    .then((results) => {
      process.exit(results.overall === 'HEALTHY' || results.overall === 'WARNING' ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ 健康检查执行失败:', error);
      process.exit(1);
    });
}

module.exports = SystemHealthChecker;