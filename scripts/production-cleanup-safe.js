#!/usr/bin/env node

/**
 * 智慧养鹅SaaS平台 - 生产环境安全清理脚本
 * 清理开发调试文件、日志文件和临时文件
 * 
 * 使用方法: node scripts/production-cleanup-safe.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🧹 开始智慧养鹅SaaS平台安全清理...\n');

// 项目根目录
const ROOT_DIR = path.join(__dirname, '..');

/**
 * 安全删除文件/目录
 */
function safeDelete(filePath) {
  try {
    const fullPath = path.resolve(filePath);
    if (fs.existsSync(fullPath)) {
      const stats = fs.lstatSync(fullPath);
      if (stats.isDirectory()) {
        fs.rmSync(fullPath, { recursive: true, force: true });
        console.log(`✅ 已删除目录: ${filePath}`);
      } else {
        fs.unlinkSync(fullPath);
        console.log(`✅ 已删除文件: ${filePath}`);
      }
      return true;
    }
  } catch (error) {
    console.log(`⚠️  删除失败: ${filePath} - ${error.message}`);
    return false;
  }
  return false;
}

/**
 * 清理日志文件
 */
function cleanupLogs() {
  console.log('📝 清理日志文件...');
  
  const logFiles = [
    'logs/server.log',
    'logs/admin.log', 
    'logs/saas-admin.log',
    'frontend.log',
    'backend/saas-admin/backend.log'
  ];
  
  let cleaned = 0;
  logFiles.forEach(file => {
    if (safeDelete(path.join(ROOT_DIR, file))) {
      cleaned++;
    }
  });
  
  console.log(`📝 已清理 ${cleaned} 个日志文件\n`);
}

/**
 * 清理测试截图和报告
 */
function cleanupTestFiles() {
  console.log('📸 清理测试文件和截图...');
  
  const testDirs = [
    'screenshots',
    'test-results',
    'playwright-report',
    'audit-videos'
  ];
  
  const testFiles = [
    '*.html', // 各种测试响应HTML文件
    'test-*.json',
    'admin-test-report.json',
    'comprehensive-*.json',
    'critical-modules-test-report.json',
    'fix-test-report.json',
    'final-*.json',
    'module-test-report.json',
    'saas-admin-*.json'
  ];
  
  let cleaned = 0;
  
  // 清理测试目录
  testDirs.forEach(dir => {
    if (safeDelete(path.join(ROOT_DIR, dir))) {
      cleaned++;
    }
  });
  
  console.log(`📸 已清理 ${cleaned} 个测试目录\n`);
}

/**
 * 清理备份文件
 */
function cleanupBackups() {
  console.log('💾 清理备份文件...');
  
  const backupFiles = [
    'backend/saas-admin/routes/complete-admin.js.backup'
  ];
  
  let cleaned = 0;
  backupFiles.forEach(file => {
    if (safeDelete(path.join(ROOT_DIR, file))) {
      cleaned++;
    }
  });
  
  console.log(`💾 已清理 ${cleaned} 个备份文件\n`);
}

/**
 * 清理临时响应文件
 */
function cleanupTempFiles() {
  console.log('🗂️  清理临时响应文件...');
  
  const tempFiles = [
    'api-management-response.html',
    'reports-response.html', 
    'system-response.html',
    'cookies.txt',
    'backend/cookies.txt',
    'backend/saas-admin/cookies.txt'
  ];
  
  let cleaned = 0;
  tempFiles.forEach(file => {
    if (safeDelete(path.join(ROOT_DIR, file))) {
      cleaned++;
    }
  });
  
  console.log(`🗂️  已清理 ${cleaned} 个临时文件\n`);
}

/**
 * 清理重复的测试脚本
 */
function cleanupDuplicateScripts() {
  console.log('🔄 识别重复的测试脚本...');
  
  const duplicateScripts = [
    'admin-comprehensive-test.js',
    'comprehensive-admin-audit.js', 
    'final-comprehensive-test.js',
    'manual-test.js',
    'quick-test.js',
    'test-admin-functionality.sh',
    'test-all-modules.js',
    'test-api-endpoints.js',
    'test-critical-modules.js',
    'test-fixed-modules.js'
  ];
  
  let cleaned = 0;
  duplicateScripts.forEach(script => {
    if (safeDelete(path.join(ROOT_DIR, script))) {
      cleaned++;
    }
  });
  
  console.log(`🔄 已清理 ${cleaned} 个重复脚本\n`);
}

/**
 * 优化package.json - 移除开发依赖
 */
function optimizePackageJson() {
  console.log('📦 优化package.json...');
  
  try {
    const packagePath = path.join(ROOT_DIR, 'package.json');
    const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // 添加生产环境脚本
    packageData.scripts = {
      ...packageData.scripts,
      'start:production': 'NODE_ENV=production node backend/app.js',
      'logs:clear': 'rm -rf logs/*.log && echo "日志已清理"',
      'health:check': 'node scripts/health-check.js'
    };
    
    fs.writeFileSync(packagePath, JSON.stringify(packageData, null, 2));
    console.log('📦 package.json 已优化\n');
  } catch (error) {
    console.log(`⚠️  优化package.json失败: ${error.message}\n`);
  }
}

/**
 * 创建.gitignore优化
 */
function optimizeGitignore() {
  console.log('🔒 优化.gitignore...');
  
  const gitignoreContent = `# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 日志文件
logs/
*.log
backend/saas-admin/backend.log
frontend.log

# 测试和临时文件
test-results/
screenshots/
playwright-report/
audit-videos/
*.html
test-*.json
*-test-report.json

# 备份文件
*.backup
*.bak
*.tmp
*.temp

# 环境配置
.env.local
.env.development.local
.env.test.local
.env.production.local

# 微信开发者工具
project.private.config.json

# 系统文件
.DS_Store
Thumbs.db

# 临时文件
cookies.txt
*-response.html
`;
  
  try {
    fs.writeFileSync(path.join(ROOT_DIR, '.gitignore'), gitignoreContent);
    console.log('🔒 .gitignore 已优化\n');
  } catch (error) {
    console.log(`⚠️  优化.gitignore失败: ${error.message}\n`);
  }
}

/**
 * 主清理流程
 */
function main() {
  console.log('🎯 智慧养鹅SaaS平台 - 生产环境清理\n');
  
  // 执行各项清理任务
  cleanupLogs();
  cleanupTestFiles();
  cleanupBackups();
  cleanupTempFiles();
  cleanupDuplicateScripts();
  optimizePackageJson();
  optimizeGitignore();
  
  console.log('✨ 清理完成! 项目已准备好生产部署\n');
  
  // 显示清理后的项目状态
  console.log('📊 清理后项目统计:');
  try {
    const stats = execSync('find . -name "*.js" -not -path "./node_modules/*" | wc -l', { cwd: ROOT_DIR }).toString().trim();
    console.log(`📄 JavaScript文件数: ${stats}`);
    
    const size = execSync('du -sh . --exclude=node_modules', { cwd: ROOT_DIR }).toString().split('\t')[0];
    console.log(`📁 项目大小(不含node_modules): ${size}`);
  } catch (error) {
    console.log('⚠️  无法获取项目统计信息');
  }
  
  console.log('\n🚀 建议下一步操作:');
  console.log('1. 检查数据库连接配置');
  console.log('2. 运行: npm run test 验证功能');
  console.log('3. 运行: npm run start:production 启动生产模式');
  console.log('4. 配置监控和日志轮转');
}

// 运行清理
main();