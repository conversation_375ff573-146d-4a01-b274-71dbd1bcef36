# 智慧养鹅SaaS平台 - 生产部署清单

## 🚀 部署前准备

### 1. 系统环境检查
- [ ] Node.js 版本 >= 16.0.0
- [ ] npm 版本 >= 8.0.0
- [ ] MySQL 8.0+ 服务运行中
- [ ] PM2 全局安装 (`npm install -g pm2`)
- [ ] 服务器防火墙配置完成

### 2. 依赖包安装
- [ ] 执行 `npm install --production` 
- [ ] 确认 mysql2 包正确安装
- [ ] 验证所有依赖包完整性

### 3. 环境配置
- [ ] 复制 `.env.production` 为 `.env`
- [ ] 填入真实的微信AppSecret
- [ ] 配置数据库连接信息
- [ ] 设置JWT密钥（生产环境专用）
- [ ] 配置邮件服务参数
- [ ] 设置SSL证书路径（如需要）

### 4. 数据库初始化
- [ ] 创建数据库：`smart_goose_saas`
- [ ] 执行初始化脚本：`npm run db:init`
- [ ] 导入基础数据：`npm run db:seed`
- [ ] 验证数据库连接：`npm run health:check`

## 🔧 部署执行步骤

### 5. 代码质量验证
- [ ] 运行代码检查：`npm run lint`
- [ ] 执行一致性检查：`npm run check:consistency`
- [ ] 运行单元测试：`npm test`
- [ ] 执行集成测试：`npm run test:integration`

### 6. 生产服务启动
- [ ] 清理旧日志：`npm run logs:clear`
- [ ] 启动生产监控：确保 `utils/production-monitor.js` 正常
- [ ] PM2集群启动：`pm2 start ecosystem.config.js --env production`
- [ ] 验证服务状态：`pm2 status`

### 7. 服务端口验证
- [ ] 主API服务：http://localhost:3000 ✓
- [ ] 管理后台：http://localhost:3002 ✓  
- [ ] SAAS管理：http://localhost:3003 ✓
- [ ] 健康检查端点：http://localhost:3000/health ✓

## 🔍 部署后验证

### 8. API功能测试
- [ ] 用户认证API：`/api/v1/auth/login`
- [ ] 数据列表API：`/api/v1/geese/list`
- [ ] 租户隔离验证：多租户数据不混淆
- [ ] 权限控制验证：角色访问限制正常

### 9. 性能监控
- [ ] 监控系统启动：生产监控面板正常
- [ ] 响应时间监控：平均响应时间 < 200ms
- [ ] 错误率监控：错误率 < 1%
- [ ] 内存使用监控：内存使用率 < 80%

### 10. 安全检查
- [ ] HTTPS配置（生产环境）
- [ ] CORS策略验证
- [ ] 请求限流功能
- [ ] SQL注入防护
- [ ] JWT密钥安全性

## 🚨 监控和告警

### 11. 日志系统
- [ ] Winston日志正常写入：`logs/app.log`
- [ ] 错误日志监控：`logs/error.log`
- [ ] 访问日志记录：`logs/access.log`
- [ ] 日志轮转配置：避免磁盘空间满

### 12. 告警机制
- [ ] 邮件告警配置：系统错误通知
- [ ] 健康检查告警：服务异常提醒  
- [ ] 性能告警：响应时间过长提醒
- [ ] 资源告警：内存/磁盘使用率告警

## 📋 故障排除清单

### 常见问题解决方案：

**数据库连接失败**
- 检查MySQL服务状态
- 验证数据库用户权限
- 确认网络连接通畅

**npm包安装失败**
- 清理npm缓存：`npm cache clean --force`
- 删除node_modules：`rm -rf node_modules`
- 重新安装：`npm install`

**服务启动失败**
- 检查端口占用：`lsof -i :3000`
- 验证环境变量：`node -e "console.log(process.env.NODE_ENV)"`
- 查看PM2日志：`pm2 logs`

**性能问题**
- 监控内存使用：`pm2 monit`
- 数据库查询优化
- 启用Redis缓存

## ✅ 部署完成确认

### 最终验证步骤：
1. [ ] 所有服务正常运行（3000, 3002, 3003端口）
2. [ ] 数据库连接稳定
3. [ ] API响应正常
4. [ ] 监控系统工作正常
5. [ ] 日志系统记录完整
6. [ ] 错误告警机制激活

### 部署签字确认：
- 技术负责人：_________________ 日期：_________
- 运维负责人：_________________ 日期：_________
- 项目经理：___________________ 日期：_________

---

**紧急联系方式**
- 技术支持：[技术团队联系方式]
- 运维支持：[运维团队联系方式]  
- 项目经理：[项目经理联系方式]

**备份和回滚计划**
- 数据库备份时间：每日凌晨2点
- 代码备份位置：[Git仓库地址]
- 回滚方案：[回滚步骤文档链接]