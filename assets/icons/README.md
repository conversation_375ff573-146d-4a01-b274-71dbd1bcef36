# TabBar 图标说明

## 当前状态
✅ 已生成符合微信小程序规范的SVG图标，需要转换为PNG格式。

## 微信小程序 TabBar 图标规范

### 官方要求
- **尺寸限制**：不能超过 81×81 像素
- **推荐尺寸**：64×64 像素（最佳实践）
- **格式**：PNG（推荐）或 SVG
- **背景**：透明背景
- **数量**：2-5 个标签

### 颜色规范
- **普通状态**：#7A7E83（灰色）
- **选中状态**：#0066CC（蓝色，与导航栏颜色一致）

## 需要的图标

### 1. 首页图标 (home)
- 普通状态：home.png (灰色 #7A7E83)
- 选中状态：home_selected.png (蓝色 #0066CC)
- 尺寸：64px × 64px
- 设计：房子图标
- ✅ SVG 已生成：home_new.svg, home_selected_new.svg

### 2. 健康图标 (health)
- 普通状态：health.png (灰色 #7A7E83)
- 选中状态：health_selected.png (蓝色 #0066CC)
- 尺寸：64px × 64px
- 设计：心率图标
- ✅ SVG 已生成：health_new.svg, health_selected_new.svg

### 3. 商城图标 (shop)
- 普通状态：shop.png (灰色 #7A7E83)
- 选中状态：shop_selected.png (蓝色 #0066CC)
- 尺寸：64px × 64px
- 设计：购物车图标
- ✅ SVG 已生成：shop_new.svg, shop_selected_new.svg

### 4. 我的图标 (profile)
- 普通状态：profile.png (灰色 #7A7E83)
- 选中状态：profile_selected.png (蓝色 #0066CC)
- 尺寸：64px × 64px
- 设计：用户图标
- ✅ SVG 已生成：profile_new.svg, profile_selected_new.svg

### 5. 生产图标 (production) - 备用
- 普通状态：production.png (灰色 #7A7E83)
- 选中状态：production_selected.png (蓝色 #0066CC)
- 尺寸：64px × 64px
- 设计：工厂/生产图标
- ✅ SVG 已生成：production_new.svg, production_selected_new.svg

## SVG 转 PNG 转换方法

### 方法1：命令行工具（推荐）
```bash
# 安装 ImageMagick
brew install imagemagick

# 运行转换脚本
./convert_icons.sh
```

### 方法2：在线转换工具
1. **Convertio**: https://convertio.co/svg-png/
2. **CloudConvert**: https://cloudconvert.com/svg-to-png
3. **SVG2PNG**: https://svg2png.com/

**转换设置**：
- 宽度：64px
- 高度：64px
- 背景：透明

### 方法3：设计软件
- **Figma**：导入SVG，导出为PNG (64×64px)
- **Sketch**：导入SVG，导出为PNG (64×64px)
- **Adobe Illustrator**：打开SVG，导出为PNG (64×64px)

## 图标特点
- 基于 Tabler Icons 开源图标库
- 线条风格一致，简洁清晰
- 符合微信小程序设计规范
- 在小尺寸下清晰可见
- 透明背景，适配不同主题

## 安装转换工具（可选）
```bash
# ImageMagick (推荐)
brew install imagemagick

# librsvg
brew install librsvg

# Inkscape
brew install --cask inkscape
```

## 使用步骤
1. 使用上述任一方法将 SVG 转换为 PNG
2. 确保生成的 PNG 图标尺寸为 64×64px
3. 图标将自动替换现有的占位符图标
4. 微信小程序将自动使用新的图标
